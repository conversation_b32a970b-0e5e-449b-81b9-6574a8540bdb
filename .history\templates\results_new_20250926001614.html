<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نتائج التحليل - نظام تحليل البيانات</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #63a0dd;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(252, 248, 248, 0.1);
            margin-bottom: 20px;
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: rgb(248, 242, 242);
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }

        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }

        .table th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
        }

        .table td {
            border: none;
            border-bottom: 1px solid #dee2e6;
        }

        .badge {
            font-size: 0.8rem;
        }

        .alert {
            border: none;
            border-radius: 10px;
        }

        .navbar {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .progress {
            height: 8px;
            border-radius: 4px;
        }

        .chart-wrapper {
            position: relative;
            height: 400px;
            margin-bottom: 20px;
        }

        .chart-canvas {
            max-height: 400px;
        }

        .distribution-table {
            font-size: 0.9rem;
        }

        .distribution-table th {
            background-color: #e9ecef;
            font-weight: 600;
            text-align: center;
        }

        .distribution-table td {
            vertical-align: middle;
        }

        .stats-summary {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
        }

        .chart-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .chart-card {
            border: none;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .chart-wrapper-large {
            position: relative;
            height: 500px;
            margin-bottom: 20px;
        }

        .chart-canvas-large {
            max-height: 500px;
        }

        .stats-summary-large {
            background-color: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            height: 100%;
        }

        .bg-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        }

        .text-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
        }

        .btn-gradient:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            color: white;
        }

        .distributions-section {
            margin-top: 30px;
        }

        .distributions-section .card {
            margin-bottom: 40px;
        }

        .distributions-section .chart-wrapper-large {
            height: 450px;
        }

        .distributions-section .table {
            margin-bottom: 0;
        }

        .distributions-section .stats-summary-large {
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        /* Table Sky Theme */
        .table-sky {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            overflow: hidden;
        }

        .table-sky thead th {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: white;
            font-weight: 600;
            text-align: center;
            vertical-align: middle;
            padding: 15px 10px;
        }

        .table-sky tbody td {
            background: rgba(255, 255, 255, 0.05);
            border: none;
            color: white;
            text-align: center;
            vertical-align: middle;
            padding: 12px 10px;
        }

        .table-sky tbody tr:hover td {
            background: rgba(255, 255, 255, 0.1);
        }

        .table-sky .badge {
            background: rgba(255, 255, 255, 0.2) !important;
            color: white !important;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        /* Sortable Headers */
        .sortable {
            cursor: pointer;
            user-select: none;
            position: relative;
        }

        .sortable:hover {
            background: rgba(255, 255, 255, 0.15) !important;
        }

        .sort-icon {
            margin-left: 5px;
            opacity: 0.5;
            transition: opacity 0.3s ease;
        }

        .sortable:hover .sort-icon {
            opacity: 1;
        }

        .sortable.sort-asc .sort-icon:before {
            content: '\f145';
            /* fa-sort-up */
        }

        .sortable.sort-desc .sort-icon:before {
            content: '\f144';
            /* fa-sort-down */
        }

        /* Merge Table Sortable Headers */
        #mergeAnalysisTable .sortable {
            background: rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            user-select: none;
        }

        #mergeAnalysisTable .sortable:hover {
            background: rgba(255, 255, 255, 0.2) !important;
            transform: translateY(-1px);
        }

        #mergeAnalysisTable .sort-icon {
            margin-right: 5px;
            margin-left: 0;
            font-size: 0.8em;
            opacity: 0.5;
            transition: opacity 0.3s ease;
        }

        #mergeAnalysisTable .sortable:hover .sort-icon {
            opacity: 1;
        }

        #mergeAnalysisTable .sortable.sort-asc .sort-icon:before {
            content: '\f145';
            /* fa-sort-up */
        }

        #mergeAnalysisTable .sortable.sort-desc .sort-icon:before {
            content: '\f144';
            /* fa-sort-down */
        }

        /* Person Key Column Styling */
        #mergeAnalysisTable tbody td:first-child {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white !important;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
            border: none;
            position: relative;
            overflow: hidden;
        }

        #mergeAnalysisTable tbody td:first-child::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        #mergeAnalysisTable tbody tr:hover td:first-child::before {
            left: 100%;
        }

        #mergeAnalysisTable tbody td:first-child small {
            color: rgba(255,255,255,0.8) !important;
            font-weight: 400;
            text-shadow: none;
        }

        #mergeAnalysisTable tbody td:first-child:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
            transform: scale(1.02);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            z-index: 10;
        }

        /* Enhanced table styling for merge table */
        #mergeAnalysisTable.table-sky tbody td:first-child {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
        }

        #mergeAnalysisTable.table-sky tbody tr:hover td:first-child {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
        }

        /* PersonRecord Table Styles */
        #personTable {
            width: 100%;
            border-collapse: collapse;
            text-align: center;
            margin-top: 20px;
        }

        #personTable th, #personTable td {
            border: 1px solid #333;
            padding: 8px;
        }

        #personTable th {
            background-color: #f2f2f2;
        }

        .note-normal {
            color: green;
            font-weight: bold;
        }

        .note-merge {
            color: orange;
            font-weight: bold;
        }

        .note-conflict {
            color: red;
            font-weight: bold;
        }

        .note-review {
            color: blue;
            font-weight: bold;
        }

        /* Enhanced PersonRecord Table Styles */
        #personTable {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 14px;
            background-color: #ffffff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            border-radius: 8px;
            overflow: hidden;
        }

        #personTable th, #personTable td {
            padding: 16px 12px;
            text-align: center;
            border: 1px solid #dee2e6;
            font-weight: 600;
        }

        #personTable th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
            transition: all 0.3s ease;
        }

        #personTable th:hover {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
            color: #667eea !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            border: 2px solid #667eea !important;
        }

        #personTable tbody tr:nth-child(even) {
            background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 25%, #d1ecf1 50%, #e6f3ff 75%, #f0f8ff 100%);
            border-bottom: 1px solid #b8daff;
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.8), 0 1px 3px rgba(0, 123, 255, 0.08);
            border-left: 3px solid #cce7ff;
        }

        #personTable tbody tr:nth-child(odd) {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 20%, #f0f4ff 40%, #e8f0ff 60%, #f0f4ff 80%, #ffffff 100%);
            border-bottom: 1px solid #d4edda;
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.9), 0 1px 3px rgba(40, 167, 69, 0.06);
            border-left: 3px solid #d1ecf1;
        }

        #personTable tbody tr:hover {
            background: linear-gradient(135deg, #e8f4fd 0%, #b3e5fc 30%, #81d4fa 60%, #4fc3f7 80%, #e8f4fd 100%);
            cursor: pointer;
            transform: translateY(-3px) scale(1.008);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 25px rgba(33, 150, 243, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.6);
            border-left: 6px solid #1976d2;
            border-radius: 0 12px 12px 0;
            z-index: 5;
        }

        /* Special styling for conflict rows - VIBRANT CRIMSON RED with enhanced glow */
        #personTable tbody tr:has(.merge-reason:contains("🚨")) {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 25%, #ef9a9a 50%, #e57373 75%, #ffebee 100%);
            border-left: 8px solid #d32f2f;
            box-shadow: 0 4px 15px rgba(211, 47, 47, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
            position: relative;
            border-radius: 0 12px 12px 0;
            animation: pulseConflict 3s infinite ease-in-out;
        }

        #personTable tbody tr:has(.merge-reason:contains("🚨")):hover {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 30%, #ef5350 60%, #f44336 80%, #ffebee 100%);
            border-left: 10px solid #b71c1c;
            box-shadow: 0 8px 25px rgba(211, 47, 47, 0.35), inset 0 1px 0 rgba(255, 255, 255, 0.4);
            transform: translateY(-5px) scale(1.012);
            z-index: 15;
        }

        /* Special styling for merge needed rows - WARM AMBER ORANGE with golden glow */
        #personTable tbody tr:has(.merge-reason:contains("⚠️")) {
            background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 25%, #ffe082 50%, #ffd54f 75%, #fff8e1 100%);
            border-left: 8px solid #f57c00;
            box-shadow: 0 4px 15px rgba(245, 124, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
            border-radius: 0 12px 12px 0;
            animation: pulseWarning 4s infinite ease-in-out;
        }

        #personTable tbody tr:has(.merge-reason:contains("⚠️")):hover {
            background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 30%, #ffe082 60%, #ffb74d 80%, #fff8e1 100%);
            border-left: 10px solid #ef6c00;
            box-shadow: 0 8px 25px rgba(245, 124, 0, 0.35), inset 0 1px 0 rgba(255, 255, 255, 0.4);
            transform: translateY(-5px) scale(1.012);
            z-index: 15;
        }

        /* Special styling for normal rows - FRESH EMERALD GREEN with minty glow */
        #personTable tbody tr:has(.merge-reason:contains("✅")) {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 25%, #a5d6a7 50%, #81c784 75%, #e8f5e8 100%);
            border-left: 8px solid #2e7d32;
            box-shadow: 0 4px 15px rgba(46, 125, 50, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
            border-radius: 0 12px 12px 0;
            animation: pulseSuccess 5s infinite ease-in-out;
        }

        #personTable tbody tr:has(.merge-reason:contains("✅")):hover {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 30%, #a5d6a7 60%, #66bb6a 80%, #e8f5e8 100%);
            border-left: 10px solid #1b5e20;
            box-shadow: 0 8px 25px rgba(46, 125, 50, 0.35), inset 0 1px 0 rgba(255, 255, 255, 0.4);
            transform: translateY(-5px) scale(1.012);
            z-index: 15;
        }

        /* Special styling for review needed rows - MYSTICAL INDIGO PURPLE with ethereal glow */
        #personTable tbody tr:has(.merge-reason:contains("❓")) {
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 25%, #ce93d8 50%, #ba68c8 75%, #f3e5f5 100%);
            border-left: 8px solid #7b1fa2;
            box-shadow: 0 4px 15px rgba(123, 31, 162, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
            border-radius: 0 12px 12px 0;
            animation: pulseReview 6s infinite ease-in-out;
        }

        #personTable tbody tr:has(.merge-reason:contains("❓")):hover {
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 30%, #ce93d8 60%, #ab47bc 80%, #f3e5f5 100%);
            border-left: 10px solid #6a1b9a;
            box-shadow: 0 8px 25px rgba(123, 31, 162, 0.35), inset 0 1px 0 rgba(255, 255, 255, 0.4);
            transform: translateY(-5px) scale(1.012);
            z-index: 15;
        }

        #personTable .person-name {
            font-weight: 700;
            color: #2c3e50;
            max-width: 200px;
            word-wrap: break-word;
            background: #f8f9fa;
            padding: 10px 14px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
            transition: all 0.2s ease;
            font-size: 14px;
            line-height: 1.4;
        }

        #personTable .insurance-numbers, #personTable .manual-numbers {
            font-family: 'Courier New', monospace;
            background: #fff3cd;
            padding: 8px 12px;
            border-radius: 6px;
            color: #856404;
            border: 1px solid #ffeaa7;
            font-weight: 600;
            transition: all 0.2s ease;
            font-size: 13px;
            line-height: 1.4;
            word-spacing: 3px;
            letter-spacing: 0.5px;
        }

        #personTable .merge-reason {
            padding: 10px 14px;
            border-radius: 8px;
            font-weight: 700;
            border: 2px solid;
            text-transform: uppercase;
            font-size: 12px;
            letter-spacing: 0.5px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            text-shadow: 0 1px 1px rgba(0,0,0,0.1);
            line-height: 1.3;
            word-wrap: break-word;
        }

        /* Specific styling for each merge reason type */
        #personTable tbody tr:has(.merge-reason:contains("🚨")) .merge-reason {
            background: linear-gradient(135deg, #ff4757 0%, #ff3838 50%, #ff4757 100%);
            color: white;
            border-color: #ff3838;
            box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3);
        }

        #personTable tbody tr:has(.merge-reason:contains("⚠️")) .merge-reason {
            background: linear-gradient(135deg, #ffa726 0%, #fb8c00 50%, #ffa726 100%);
            color: white;
            border-color: #fb8c00;
            box-shadow: 0 2px 8px rgba(255, 167, 38, 0.3);
        }

        #personTable tbody tr:has(.merge-reason:contains("✅")) .merge-reason {
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 50%, #4caf50 100%);
            color: white;
            border-color: #388e3c;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
        }

        #personTable tbody tr:has(.merge-reason:contains("❓")) .merge-reason {
            background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 50%, #9c27b0 100%);
            color: white;
            border-color: #7b1fa2;
            box-shadow: 0 2px 8px rgba(156, 39, 176, 0.3);
        }

        #personTable .merge-reason::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        #personTable .merge-reason:hover::before {
            left: 100%;
        }

        #personTable .additional-info {
            font-size: 13px;
            color: #495057;
            max-width: 160px;
            word-wrap: break-word;
            background-color: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }

        #personTable .document-number {
            font-family: 'Courier New', monospace;
            background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
            color: #383d41;
            padding: 6px 10px;
            border-radius: 6px;
            font-weight: 600;
            border: 1px solid #adb5bd;
            font-size: 13px;
            letter-spacing: 0.5px;
        }

        #personTable .count-info {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: 600;
            border: 1px solid #28a745;
            font-size: 13px;
        }

        /* Enhanced animations for table rows */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(40px) scale(0.95);
                filter: blur(2px);
            }

            to {
                opacity: 1;
                transform: translateY(0) scale(1);
                filter: blur(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-60px) skewX(-15deg) scale(0.9);
            }

            to {
                opacity: 1;
                transform: translateX(0) skewX(0deg) scale(1);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(60px) skewX(15deg) scale(0.9);
            }

            to {
                opacity: 1;
                transform: translateX(0) skewX(0deg) scale(1);
            }
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.1) rotate(-180deg);
                filter: blur(3px);
            }

            50% {
                opacity: 1;
                transform: scale(1.1) rotate(0deg);
                filter: blur(1px);
            }

            70% {
                transform: scale(0.95) rotate(0deg);
                filter: blur(0);
            }

            100% {
                opacity: 1;
                transform: scale(1) rotate(0deg);
                filter: blur(0);
            }
        }

        @keyframes pulseGlow {
            0%, 100% {
                box-shadow: 0 0 8px rgba(255, 255, 255, 0.4), 0 0 16px rgba(255, 255, 255, 0.2);
            }

            50% {
                box-shadow: 0 0 16px rgba(255, 255, 255, 0.6), 0 0 32px rgba(255, 255, 255, 0.4);
            }
        }

        @keyframes rainbowGlow {
            0% {
                box-shadow: 0 0 12px #ff6b6b, 0 0 24px #ff6b6b;
            }

            16.66% {
                box-shadow: 0 0 12px #ffa726, 0 0 24px #ffa726;
            }

            33.33% {
                box-shadow: 0 0 12px #ffeb3b, 0 0 24px #ffeb3b;
            }

            50% {
                box-shadow: 0 0 12px #4caf50, 0 0 24px #4caf50;
            }

            66.66% {
                box-shadow: 0 0 12px #2196f3, 0 0 24px #2196f3;
            }

            83.33% {
                box-shadow: 0 0 12px #9c27b0, 0 0 24px #9c27b0;
            }

            100% {
                box-shadow: 0 0 12px #ff6b6b, 0 0 24px #ff6b6b;
            }
        }

        @keyframes shimmer {
            0% {
                background-position: -200% 0;
            }

            100% {
                background-position: 200% 0;
            }
        }

        @keyframes pulseConflict {
            0%, 100% {
                                                    {% set critical_missing = critical_missing + 1 %}
                                                {% endif %}
                                            {% endif %}
                                        {% endfor %}

                                        <div class="col-md-4">
                                            <div class="card border-primary">
                                                <div class="card-body text-center">
                                                    <h5 class="text-primary">{{ critical_missing }}</h5>
                                                    <p class="mb-0">أعمدة حرجة تحتاج تدخل</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="card border-success">
                                                <div class="card-body text-center">
                                                    <h5 class="text-success">{{ analysis_data.analysis.column_analysis|length - critical_missing }}</h5>
                                                    <p class="mb-0">أعمدة في حالة جيدة</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="card border-info">
                                                <div class="card-body text-center">
                                                    <h5 class="text-info">{{ "%.1f"|format(analysis_data.analysis.basic_stats.data_quality_score) }}%</h5>
                                                    <p class="mb-0">معدل جودة البيانات</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Insurance Criteria Analysis -->
                        {% if 'insurance' in user_sectors and insurance_criteria_list %}
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    معايير تحليل قطاع التأمين
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <!-- General Criteria -->
                                    {% if 'data_quality' in insurance_criteria_list or 'functional_link' in insurance_criteria_list or 'chronological_sequence' in insurance_criteria_list or 'financial_data_match' in insurance_criteria_list or 'beneficiaries_rights' in insurance_criteria_list %}
                                    <div class="col-md-6 mb-3">
                                        <h6 class="text-primary mb-3">
                                            <i class="fas fa-star me-2"></i>
                                            معايير عامة
                                        </h6>
                                        <div class="row">
                                            {% if 'data_quality' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>جودة البيانات: اكتمال بيانات المشترك (الاسم، الرقم الوطني، تاريخ الميلاد، الرقم التأميني)</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% if 'functional_link' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>الربط الوظيفي: تطابق بيانات الوظيفة/الراتب مع الاشتراكات التأمينية</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% if 'chronological_sequence' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>التسلسل الزمني: انتظام الاشتراكات عبر السنوات (كشف الفجوات)</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% if 'financial_data_match' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>مطابقة البيانات المالية: التأكد من أن الاشتراكات والخصومات مطابقة للراتب الأساسي والمتغير</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% if 'beneficiaries_rights' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>حقوق المستحقين: بيانات الورثة والمستفيدين في حالة التقاعد أو الوفاة</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    {% endif %}

                                    <!-- Government Sector Criteria -->
                                    {% if 'government_service_accuracy' in insurance_criteria_list or 'allowance_verification' in insurance_criteria_list or 'salary_differences' in insurance_criteria_list or 'service_years_calculation' in insurance_criteria_list or 'dual_employment_check' in insurance_criteria_list %}
                                    <div class="col-md-6 mb-3">
                                        <h6 class="text-info mb-3">
                                            <i class="fas fa-building me-2"></i>
                                            معايير خاصة بالقطاع الحكومي
                                        </h6>
                                        <div class="row">
                                            {% if 'government_service_accuracy' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>صحة تسجيل الخدمة الفعلية (تاريخ التعيين – تاريخ الإحالة للتقاعد)</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% if 'allowance_verification' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>التحقق من مطابقة العلاوات السنوية والترقيات مع الاشتراكات</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% if 'salary_differences' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>مراقبة الفروقات بين الراتب الوظيفي والراتب التأميني</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% if 'service_years_calculation' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>احتساب سنوات الخدمة المضمونة لأغراض المعاش</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% if 'dual_employment_check' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>كشف حالات ازدواج الوظيفة أو الخدمة</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="row">
                                    <!-- Private Sector Criteria -->
                                    {% if 'regular_payments' in insurance_criteria_list or 'salary_manipulation_check' in insurance_criteria_list or 'employee_registration' in insurance_criteria_list or 'workforce_turnover' in insurance_criteria_list or 'evasion_detection' in insurance_criteria_list %}
                                    <div class="col-md-6 mb-3">
                                        <h6 class="text-warning mb-3">
                                            <i class="fas fa-industry me-2"></i>
                                            معايير خاصة بالقطاع الخاص
                                        </h6>
                                        <div class="row">
                                            {% if 'regular_payments' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>انتظام دفع الاشتراكات الشهرية من قبل صاحب العمل</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% if 'salary_manipulation_check' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>التأكد من عدم التلاعب بالراتب المصرح به (أقل من الراتب الفعلي)</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% if 'employee_registration' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>التحقق من تسجيل جميع الموظفين وعدم إغفال بعضهم</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% if 'workforce_turnover' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>تحليل حركة دوران العمالة (كثرة التوظيف/الاستقالات قد تشير لمخاطر)</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% if 'evasion_detection' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>كشف حالات التهرب من الاشتراك</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    {% endif %}

                                    <!-- Mixed Sector Criteria -->
                                    {% if 'contribution_distribution' in insurance_criteria_list or 'overlapping_check' in insurance_criteria_list or 'commitment_verification' in insurance_criteria_list or 'rights_maintenance' in insurance_criteria_list %}
                                    <div class="col-md-6 mb-3">
                                        <h6 class="text-secondary mb-3">
                                            <i class="fas fa-balance-scale me-2"></i>
                                            معايير خاصة بالقطاع المختلط
                                        </h6>
                                        <div class="row">
                                            {% if 'contribution_distribution' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>التحقق من توزيع الاشتراكات بين الدولة والقطاع الخاص (النسبة المحددة قانوناً)</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% if 'overlapping_check' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>مراجعة حالات التداخل (موظف مسجل كحكومي وقطاع خاص في نفس الوقت)</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% if 'commitment_verification' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>تحليل التزام الطرفين (الحكومة + المستثمر) بدفع حصصهم</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% if 'rights_maintenance' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>مراقبة حقوق الموظف في حالة تغير تبعية المؤسسة (خصخصة أو دمج)</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="row">
                                    <!-- Financial and Investment Criteria -->
                                    {% if 'fund_contributions' in insurance_criteria_list or 'fund_deficit_surplus' in insurance_criteria_list or 'investment_returns' in insurance_criteria_list or 'long_term_liabilities' in insurance_criteria_list %}
                                    <div class="col-md-6 mb-3">
                                        <h6 class="text-success mb-3">
                                            <i class="fas fa-chart-line me-2"></i>
                                            معايير مالية واستثمارية
                                        </h6>
                                        <div class="row">
                                            {% if 'fund_contributions' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>الالتزام بتحويل الاشتراكات إلى صندوق التأمينات بانتظام</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% if 'fund_deficit_surplus' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>تحليل العجز أو الفائض في موارد الصندوق</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% if 'investment_returns' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>متابعة العوائد الاستثمارية على أموال التأمينات</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% if 'long_term_liabilities' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>توقع التزامات الصندوق على المدى الطويل (Sustainability)</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    {% endif %}

                                    <!-- Intelligent Additional Criteria -->
                                    {% if 'predictive_analysis' in insurance_criteria_list or 'risk_detection' in insurance_criteria_list or 'age_group_analysis' in insurance_criteria_list or 'entitlement_analysis' in insurance_criteria_list %}
                                    <div class="col-md-6 mb-3">
                                        <h6 class="text-danger mb-3">
                                            <i class="fas fa-brain me-2"></i>
                                            معايير ذكية إضافية
                                        </h6>
                                        <div class="row">
                                            {% if 'predictive_analysis' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>التحليل التنبؤي: التنبؤ بعدد المتقاعدين خلال 5 أو 10 سنوات قادمة</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% if 'risk_detection' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>الكشف عن المخاطر: مثل حالات تضخيم سنوات الخدمة أو تسجيل راتب غير حقيقي</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% if 'age_group_analysis' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>تحليل الفئات العمرية: لتقدير العبء المستقبلي على الصندوق</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% if 'entitlement_analysis' in insurance_criteria_list %}
                                            <div class="col-12 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <span>تحليل الاستحقاقات: مقارنة الاشتراكات الفعلية بالمعاش المتوقع</span>
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>

                                <!-- Display Options -->
                                {% if insurance_display_list %}
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="alert alert-info">
                                            <h6 class="mb-2">
                                                <i class="fas fa-eye me-2"></i>
                                                خيارات العرض المحددة
                                            </h6>
                                            <div class="d-flex flex-wrap gap-2">
                                                {% if 'charts' in insurance_display_list %}
                                                <span class="badge bg-primary">
                                                    <i class="fas fa-chart-bar me-1"></i>
                                                    الرسوم البيانية
                                                </span>
                                                {% endif %}
                                                {% if 'tables' in insurance_display_list %}
                                                <span class="badge bg-success">
                                                    <i class="fas fa-table me-1"></i>
                                                    الجداول
                                                </span>
                                                {% endif %}
                                                {% if 'kpis' in insurance_display_list %}
                                                <span class="badge bg-warning text-dark">
                                                    <i class="fas fa-tachometer-alt me-1"></i>
                                                    مؤشرات الأداء
                                                </span>
                                                {% endif %}
                                                {% if 'alerts' in insurance_display_list %}
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                                    التنبيهات
                                                </span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}

                        <!-- Distributions -->
                        {% if analysis_data.analysis and analysis_data.analysis.distributions %}
                        <div class="distributions-section">
                            <div class="text-center mb-4">
                                <h4 class="mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    توزيعات البيانات
                                </h4>
                                <p class="text-muted mt-2">عرض توزيع القيم في كل عمود بشكل بصري</p>
                                <div class="mt-3">
                                    <button type="button" class="btn btn-outline-info btn-lg" onclick="exportCharts()" style="border: 2px solid #17a2b8; background: rgba(23, 162, 184, 0.1);">
                                        <i class="fas fa-file-pdf me-2"></i>
                                        تصدير الرسوم البيانية
                                    </button>
                                    <div class="mt-2">
                                        <small class="text-muted">اضغط هنا لتصدير جميع الرسوم البيانية كملف PDF</small>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                {% for col_name, distribution in analysis_data.analysis.distributions.items() %}
                                <div class="col-12 mb-5">
                                    <div class="card chart-card">
                                        <div class="card-header chart-header">
                                            <h5 class="mb-0 text-center">
                                                <i class="fas fa-chart-pie me-2"></i>
                                                {{ col_name }}
                                            </h5>
                                        </div>
                                        <div class="card-body">
                                            <!-- Chart Canvas - Full Width -->
                                            <div class="chart-wrapper-large mb-4">
                                                <canvas id="chart-{{ loop.index }}" class="chart-canvas-large"></canvas>
                                            </div>
                                        </div>

                                        <!-- Distribution Table and Stats in separate row -->
                                        <div class="row mt-3">
                                            <div class="col-md-8">
                                                <!-- Distribution Table -->
                                                <div class="table-responsive">
                                                    <table class="table table-sm table-striped table-hover distribution-table">
                                                        <thead>
                                                            <tr>
                                                                <th class="fw-bold">القيمة</th>
                                                                <th class="fw-bold text-center">العدد</th>
                                                                <th class="fw-bold text-center">النسبة (%)</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {% set total = distribution.values() | sum %}
                                                            {% for value, count in distribution.items() | sort(attribute='1', reverse=true) | list %}
                                                            {% if loop.index <= 15 %}  <!-- Show top 15 values for larger layout -->
                                                            <tr>
                                                                <td class="text-truncate fw-medium" style="max-width: 250px;" title="{{ value }}">
                                                                    {% if value|length > 30 %}
                                                                        {{ value[:27] }}...
                                                                    {% else %}
                                                                        {{ value }}
                                                                    {% endif %}
                                                                </td>
                                                                <td class="text-center">
                                                                    <span class="badge bg-primary fs-6 px-3 py-1">{{ "{:,}".format(count) }}</span>
                                                                </td>
                                                                <td class="text-center">
                                                                    <span class="badge bg-success fs-6 px-3 py-1">{{ "%.1f"|format((count / total) * 100) }}</span>
                                                                </td>
                                                            </tr>
                                                            {% endif %}
                                                            {% endfor %}
                                                            {% if distribution|length > 15 %}
                                                            <tr class="table-light">
                                                                <td colspan="3" class="text-center text-muted small">
                                                                    و {{ distribution|length - 15 }} قيم أخرى...
                                                                </td>
                                                            </tr>
                                                            {% endif %}
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>

                                            <div class="col-md-4">
                                                <!-- Summary Statistics -->
                                                <div class="stats-summary-large">
                                                    <h6 class="text-center mb-3 fw-bold">إحصائيات العمود</h6>
                                                    <div class="row text-center">
                                                        <div class="col-12 mb-3">
                                                            <div class="p-3 bg-primary bg-opacity-10 rounded">
                                                                <div class="fw-bold text-primary h4 mb-1">{{ "{:,}".format(total) }}</div>
                                                                <small class="text-muted">إجمالي القيم</small>
                                                            </div>
                                                        </div>
                                                        <div class="col-12 mb-3">
                                                            <div class="p-3 bg-success bg-opacity-10 rounded">
                                                                <div class="fw-bold text-success h4 mb-1">{{ distribution|length }}</div>
                                                                <small class="text-muted">قيم فريدة</small>
                                                            </div>
                                                        </div>
                                                        <div class="col-12">
                                                            <div class="p-3 bg-info bg-opacity-10 rounded">
                                                                <div class="fw-bold text-info h4 mb-1">
                                                                    {% set max_count = distribution.values() | list | sort(reverse=true) | first %}
                                                                    {{ "%.1f"|format((max_count / total) * 100) }}%
                                                                </div>
                                                                <small class="text-muted">أكبر نسبة</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}

                        <!-- Recommendations -->
                        {% if analysis_data.analysis and analysis_data.analysis.recommendations %}
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-lightbulb me-2"></i>
                                    التوصيات
                                </h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-group list-group-flush">
                                    {% for rec in analysis_data.analysis.recommendations %}
                                    <li class="list-group-item">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        {{ rec }}
                                    </li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Merge Analysis for Insurance Data -->
                        {% if analysis_data.analysis.sector_specific and analysis_data.analysis.sector_specific.get('merge_analysis') and analysis_data.analysis.sector_specific.merge_analysis.total_candidates > 0 %}
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-users-cog me-2"></i>
                                    تحليل الدمج - الأشخاص ذوي الأرقام المتعددة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>تم العثور على {{ analysis_data.analysis.sector_specific.get('merge_analysis', {}).get('total_candidates', 0) }} مرشح للدمج</strong>
                                    <br>
                                    <small>أشخاص لديهم نفس البيانات الأساسية ولكن أرقام تأمينية/يدوية مختلفة</small>
                                </div>

                                <!-- Search and Filter Controls -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="fas fa-search"></i>
                                            </span>
                                            <input type="text" id="mergeSearchInput" class="form-control" placeholder="البحث في الجدول..." onkeyup="filterMergeTable()">
                                            <button class="btn btn-outline-secondary" type="button" onclick="clearMergeSearch()">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="btn-group" role="group">
                                                <select id="mergeNoteFilter" class="form-select form-select-sm me-2" onchange="filterMergeTable()">
                                                    <option value="">جميع الملاحظات</option>
                                                    <option value="تضارب">🚨 تضارب أرقام أساسية</option>
                                                    <option value="يحتاج دمج">⚠️ يحتاج دمج</option>
                                                    <option value="طبيعي">✅ طبيعي</option>
                                                    <option value="مراجعة">❓ يحتاج مراجعة</option>
                                                </select>
                                                <button type="button" class="btn btn-sm btn-outline-info" onclick="resetMergeFilters()">
                                                    <i class="fas fa-undo me-1"></i>
                                                    إعادة تعيين
                                                </button>
                                            </div>
                                            <div class="d-flex align-items-center">
                                                <label class="form-label me-2 mb-0">عرض:</label>
                                                <select id="mergePageSize" class="form-select form-select-sm" style="width: auto;" onchange="changeMergePageSize()">
                                                    <option value="10">10</option>
                                                    <option value="25" selected>25</option>
                                                    <option value="50">50</option>
                                                    <option value="100">100</option>
                                                </select>
                                                <span class="ms-2 text-muted" id="mergeTotalCount">من أصل 0</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Export Controls -->
                                <div class="d-flex justify-content-center mb-3">
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="exportMergeAnalysisToCSV()" title="تصدير البيانات كملف CSV">
                                            <i class="fas fa-file-csv me-1"></i>
                                            تصدير CSV
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-success" onclick="exportMergeAnalysisToExcel()" title="تصدير البيانات كملف Excel">
                                            <i class="fas fa-file-excel me-1"></i>
                                            تصدير Excel
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="exportMergeAnalysisToPDF()" title="تصدير البيانات كملف PDF">
                                            <i class="fas fa-file-pdf me-1"></i>
                                            تصدير PDF
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportMergeAnalysisToJSON()" title="تصدير البيانات كملف JSON">
                                            <i class="fas fa-file-code me-1"></i>
                                            تصدير JSON
                                        </button>
                                    </div>
                                </div>

                                <!-- جدول PersonRecord ديناميكي مع ترتيب -->
                                <div class="table-responsive" style="overflow-x: auto;">
                                    <table id="personTable" class="table table-striped table-hover" style="min-width: 1400px; table-layout: fixed;">
                                        <thead class="table-dark">
                                            <tr>
                                                <th class="sortable" onclick="sortMergeTable(0)" style="width: 220px; min-width: 220px;">
                                                    <i class="fas fa-user me-2"></i>الاسم الكامل
                                                    <i class="fas fa-sort sort-icon"></i>
                                                </th>
                                                <th class="sortable" onclick="sortMergeTable(1)" style="width: 120px; min-width: 120px;">
                                                    <i class="fas fa-calendar-alt me-2"></i>تاريخ الميلاد
                                                    <i class="fas fa-sort sort-icon"></i>
                                                </th>
                                                <th class="sortable" onclick="sortMergeTable(2)" style="width: 160px; min-width: 160px;">
                                                    <i class="fas fa-id-card me-2"></i>الأرقام الأساسية
                                                    <i class="fas fa-sort sort-icon"></i>
                                                </th>
                                                <th class="sortable" onclick="sortMergeTable(3)" style="width: 180px; min-width: 180px;">
                                                    <i class="fas fa-edit me-2"></i>الأرقام اليدوية
                                                    <i class="fas fa-sort sort-icon"></i>
                                                </th>
                                                <th class="sortable" onclick="sortMergeTable(4)" style="width: 140px; min-width: 140px;">
                                                    <i class="fas fa-file-alt me-2"></i>رقم الوثيقة
                                                    <i class="fas fa-sort sort-icon"></i>
                                                </th>
                                                <th class="sortable text-center" onclick="sortMergeTable(5)" style="width: 80px; min-width: 80px;">
                                                    <i class="fas fa-hashtag me-2"></i>عدد الأرقام الأساسية
                                                    <i class="fas fa-sort sort-icon"></i>
                                                </th>
                                                <th class="sortable text-center" onclick="sortMergeTable(6)" style="width: 80px; min-width: 80px;">
                                                    <i class="fas fa-hashtag me-2"></i>عدد الأرقام اليدوية
                                                    <i class="fas fa-sort sort-icon"></i>
                                                </th>
                                                <th class="sortable text-center" onclick="sortMergeTable(7)" style="width: 200px; min-width: 200px;">
                                                    <i class="fas fa-exclamation-triangle me-2"></i>الملاحظة
                                                    <i class="fas fa-sort sort-icon"></i>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- سيتم تعبئة الجدول تلقائيًا بواسطة JavaScript -->
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Pagination Controls -->
                                <nav aria-label="Merge Analysis Pagination" class="mt-3">
                                    <div class="d-flex justify-content-between align-items-center flex-wrap">
                                        <div class="pagination-info mb-2 mb-md-0">
                                            <small class="text-muted">
                                                صفحة <strong id="currentPageNum">1</strong> من <strong id="totalPagesNum">1</strong>
                                            </small>
                                        </div>
                                        <ul class="pagination pagination-sm justify-content-center mb-0" id="mergePagination">
                                            <!-- Pagination will be generated by JavaScript -->
                                        </ul>
                                        <div class="pagination-jumper mb-2 mb-md-0">
                                            <small class="text-muted me-2">الانتقال إلى:</small>
                                            <input type="number" id="pageJumper" class="form-control form-control-sm d-inline-block" style="width: 60px;" min="1" onchange="jumpToPage(this.value)">
                                        </div>
                                    </div>
                                </nav>

                                <!-- Debug Info Container -->
                                <div id="mergeDebugInfo" style="display: block;">
                                    <div class="alert alert-info mt-3">
                                        <h6>معلومات التصحيح - تحليل الدمج:</h6>
                                        <p><strong>هل البيانات موجودة؟</strong> {{ (analysis_data.analysis.sector_specific.get('merge_analysis') is not none) | string }}</p>
                                        <p><strong>عدد المرشحين:</strong> {{ analysis_data.analysis.sector_specific.get('merge_analysis', {}).get('total_candidates', 0) }}</p>
                                        <p><strong>عدد المرشحين في القائمة:</strong> {{ analysis_data.analysis.sector_specific.get('merge_analysis', {}).get('merge_candidates', [])|length }}</p>
                                        <p><strong>مفاتيح البيانات:</strong> {{ analysis_data.analysis.sector_specific.keys() | list if analysis_data.analysis.sector_specific else 'لا توجد بيانات' }}</p>
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <div class="row text-center">
                                        <div class="col-md-3">
                                            <div class="p-3 bg-primary bg-opacity-10 rounded">
                                                <div class="fw-bold text-primary h4 mb-1">{{ analysis_data.analysis.sector_specific.get('merge_analysis', {}).get('total_candidates', 0) }}</div>
                                                <small class="text-muted">إجمالي المرشحين</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="p-3 bg-success bg-opacity-10 rounded">
                                                <div class="fw-bold text-success h4 mb-1">{{ analysis_data.analysis.sector_specific.get('merge_analysis', {}).get('analyzed_persons', 0) }}</div>
                                                <small class="text-muted">الأشخاص المحللين</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="p-3 bg-info bg-opacity-10 rounded">
                                                <div class="fw-bold text-info h4 mb-1">{{ analysis_data.analysis.sector_specific.get('merge_analysis', {}).get('person_columns_found', 0) }}</div>
                                                <small class="text-muted">أعمدة الشخص المكتشفة</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="p-3 bg-warning bg-opacity-10 rounded">
                                                <div class="fw-bold text-warning h4 mb-1">{{ analysis_data.analysis.sector_specific.get('merge_analysis', {}).get('number_columns_found', 0) }}</div>
                                                <small class="text-muted">أعمدة الأرقام المكتشفة</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% else %}
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>
                                    لم يتم العثور على أشخاص يحتاجون دمج أرقامهم
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Raw Data Section -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-code me-2"></i>
                                    البيانات الأولية (JSON)
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    البيانات الأولية متاحة في وحدة التحكم (Console) للمطورين
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Analysis Data for Merge Analysis -->
    <script id="mergeAnalysisData" type="application/json">
        {% if analysis_data.analysis.sector_specific and analysis_data.analysis.sector_specific.get('merge_analysis') and analysis_data.analysis.sector_specific.merge_analysis.get('merge_candidates') %}
            {{ analysis_data.analysis.sector_specific.merge_analysis.merge_candidates | tojson }}
        {% else %}
            []
        {% endif %}
    </script>

    <!-- Global Analysis Data -->
    <script type="text/javascript">
        window.analysisData = {{ analysis_data | tojson }};
    </script>

    <!-- Chart.js Script -->
    <script type="text/javascript">
        // Initialize charts when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeDistributionCharts();
        });

        function initializeDistributionCharts() {
            // Find all chart canvases
            const canvases = document.querySelectorAll('[id^="chart-"]');

            canvases.forEach((canvas, index) => {
                const canvasId = canvas.id;
                const chartIndex = parseInt(canvasId.replace('chart-', ''));

                // Get distribution data from the page
                const card = canvas.closest('.card');
                const table = card.querySelector('.table-responsive table') || card.querySelector('table');

                // Get column name from card header
                const header = card.querySelector('.card-header h5');
                const columnName = header ? header.textContent.trim() : 'Unknown';

                if (table) {
                    const rows = table.querySelectorAll('tbody tr');

                    const labels = [];
                    const values = [];

                    rows.forEach((row, rowIndex) => {
                        const cells = row.querySelectorAll('td');

                        if (cells.length >= 3) {
                            const value = cells[0].textContent.trim();
                            const countText = cells[1].textContent.trim();

                            // Parse count, removing commas from formatted numbers
                            const count = parseInt(countText.replace(/,/g, ''));
                            if (!isNaN(count)) {
                                labels.push(value.length > 20 ? value.substring(0, 20) + '...' : value);
                                values.push(count);
                            }
                        }
                    });

                    if (labels.length > 0 && values.length > 0) {
                        createChart(canvasId, labels, values);
                    }
                }
            });
        }

        function createChart(canvasId, labels, values) {
            const ctx = document.getElementById(canvasId);
            if (!ctx) return;

            // Fix: Explicitly set canvas dimensions to prevent Chart.js from using incorrect values
            const canvas = document.getElementById(canvasId);
            const wrapper = canvas.parentElement;
            canvas.width = wrapper.clientWidth;
            canvas.height = 450; // Match the CSS height
            canvas.style.width = '100%';
            canvas.style.height = '450px';
            canvas.style.maxHeight = '450px';

            // Calculate percentages
            const total = values.reduce((sum, val) => sum + val, 0);
            const percentages = values.map(val => ((val / total) * 100).toFixed(1));

            // Get chart index to determine type
            const chartIndex = parseInt(canvasId.replace('chart-', ''));

            // Define different chart types
            const chartTypes = ['pie', 'doughnut', 'bar', 'line', 'radar', 'polarArea'];
            const chartType = chartTypes[chartIndex % chartTypes.length];

            // Create chart configuration based on type
            let chartConfig = {
                type: chartType,
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'عدد القيم',
                        data: values,
                        backgroundColor: [
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(255, 205, 86, 0.8)',
                            'rgba(75, 192, 192, 0.8)',
                            'rgba(153, 102, 255, 0.8)',
                            'rgba(255, 159, 64, 0.8)',
                            'rgba(199, 199, 199, 0.8)',
                            'rgba(83, 102, 255, 0.8)',
                            'rgba(255, 99, 255, 0.8)',
                            'rgba(99, 255, 132, 0.8)'
                        ],
                        borderColor: [
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 99, 132, 1)',
                            'rgba(255, 205, 86, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(153, 102, 255, 1)',
                            'rgba(255, 159, 64, 1)',
                            'rgba(199, 199, 199, 1)',
                            'rgba(83, 102, 255, 1)',
                            'rgba(255, 99, 255, 1)',
                            'rgba(99, 255, 132, 1)'
                        ],
                        borderWidth: 2,
                        hoverBorderWidth: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: labels.length <= 15, // Show legend for more values in large layout
                            position: 'right',
                            labels: {
                                padding: 15,
                                usePointStyle: true,
                                font: {
                                    size: 12,
                                    weight: 'bold'
                                },
                                generateLabels: function(chart) {
                                    const datasets = chart.data.datasets;
                                    return datasets[0].data.map((value, index) => ({
                                        text: `${chart.data.labels[index]} (${percentages[index]}%)`,
                                        fillStyle: datasets[0].backgroundColor[index],
                                        strokeStyle: datasets[0].borderColor[index],
                                        lineWidth: 2,
                                        hidden: false,
                                        index: index
                                    }));
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0,0,0,0.9)',
                            titleColor: 'white',
                            bodyColor: 'white',
                            titleFont: {
                                size: 14,
                                weight: 'bold'
                            },
                            bodyFont: {
                                size: 12
                            },
                            callbacks: {
                                title: function(context) {
                                    return context[0].label;
                                },
                                label: function(context) {
                                    const value = context.parsed.y || context.parsed.r || context.parsed;
                                    const percentage = percentages[context.dataIndex];
                                    return [
                                        `العدد: ${value.toLocaleString()}`,
                                        `النسبة: ${percentage}%`
                                    ];
                                }
                            }
                        },
                        title: {
                            display: true,
                            text: `توزيع البيانات - ${columnName}`,
                            font: {
                                size: 16,
                                weight: 'bold'
                            },
                            padding: {
                                top: 10,
                                bottom: 30
                            }
                        }
                    },
                    layout: {
                        padding: {
                            top: 30,
                            bottom: 30,
                            left: 20,
                            right: 20
                        }
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeOutQuart'
                    }
                }
            };

            // Add specific options for different chart types - Enhanced for large layout
            if (chartType === 'bar') {
                chartConfig.options.scales = {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'عدد التكرارات',
                            font: {
                                size: 14,
                                weight: 'bold'
                            }
                        },
                        grid: {
                            display: true,
                            color: 'rgba(0,0,0,0.1)'
                        },
                        ticks: {
                            font: {
                                size: 12
                            }
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'القيم',
                            font: {
                                size: 14,
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45,
                            font: {
                                size: 11
                            }
                        },
                        grid: {
                            display: false
                        }
                    }
                };
                // Improve bar appearance
                chartConfig.data.datasets[0].borderRadius = 4;
                chartConfig.data.datasets[0].borderSkipped = false;
            } else if (chartType === 'line') {
                chartConfig.options.scales = {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'عدد التكرارات',
                            font: {
                                size: 14,
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            font: {
                                size: 12
                            }
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'القيم',
                            font: {
                                size: 14,
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45,
                            font: {
                                size: 11
                            }
                        }
                    }
                };
                // Enhanced line styling
                chartConfig.data.datasets[0].tension = 0.4;
                chartConfig.data.datasets[0].fill = true;
                chartConfig.data.datasets[0].backgroundColor = 'rgba(54, 162, 235, 0.1)';
                chartConfig.data.datasets[0].pointRadius = 6;
                chartConfig.data.datasets[0].pointHoverRadius = 8;
            } else if (chartType === 'radar') {
                chartConfig.options.scales = {
                    r: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'عدد التكرارات',
                            font: {
                                size: 14,
                                weight: 'bold'
                            }
                        },
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        },
                        angleLines: {
                            color: 'rgba(0,0,0,0.1)'
                        },
                        pointLabels: {
                            font: {
                                size: 12,
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            font: {
                                size: 11
                            }
                        }
                    }
                };
                // Enhanced radar styling
                chartConfig.data.datasets[0].pointRadius = 5;
                chartConfig.data.datasets[0].pointHoverRadius = 7;
            } else if (chartType === 'doughnut' || chartType === 'pie') {
                // Enhanced pie/doughnut for large layout
                if (chartType === 'doughnut') {
                    chartConfig.options.cutout = '60%';
                }
                // Improve legend and appearance
                chartConfig.options.plugins.legend.position = 'right';
                chartConfig.options.plugins.legend.labels.font.size = 13;
            }

            // Create chart
            new Chart(ctx, chartConfig);
        }
    </script>

    <script>
        // Table sorting functionality
        let sortDirection = {};

        function sortTable(columnIndex) {
            const table = document.getElementById('columnsTable');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Toggle sort direction
            if (!sortDirection[columnIndex]) {
                sortDirection[columnIndex] = 'asc';
            } else {
                sortDirection[columnIndex] = sortDirection[columnIndex] === 'asc' ? 'desc' : 'asc';
            }

            // Update sort icons
            const headers = table.querySelectorAll('th.sortable');
            headers.forEach((header, index) => {
                const icon = header.querySelector('.sort-icon');
                if (index === columnIndex) {
                    icon.className = `fas fa-sort-${sortDirection[columnIndex] === 'asc' ? 'up' : 'down'} sort-icon`;
                } else {
                    icon.className = 'fas fa-sort sort-icon';
                }
            });

            // Sort rows
            rows.sort((a, b) => {
                const aValue = getCellValue(a, columnIndex);
                const bValue = getCellValue(b, columnIndex);

                if (sortDirection[columnIndex] === 'asc') {
                    return aValue > bValue ? 1 : -1;
                } else {
                    return aValue < bValue ? 1 : -1;
                }
            });

            // Re-append sorted rows
            rows.forEach(row => tbody.appendChild(row));
        }

        function getCellValue(row, columnIndex) {
            const cell = row.cells[columnIndex];
            if (!cell) return '';

            // Handle different cell types
            const badge = cell.querySelector('.badge');
            if (badge) {
                return badge.textContent.trim();
            }

            const progress = cell.querySelector('.progress-bar');
            if (progress) {
                return parseFloat(progress.style.width);
            }

            return cell.textContent.trim().replace(/,/g, '').replace('%', '');
        }

        // Export functions
        function exportToCSV() {
            const table = document.getElementById('columnsTable');
            let csv = [];

            // Get headers
            const headers = [];
            table.querySelectorAll('thead th').forEach(th => {
                headers.push(th.textContent.replace('⇅', '').trim());
            });
            csv.push(headers.join(','));

            // Get data
            table.querySelectorAll('tbody tr').forEach(row => {
                const rowData = [];
                row.querySelectorAll('td').forEach(cell => {
                    let value = cell.textContent.trim();
                    // Remove extra whitespace and special characters
                    value = value.replace(/\s+/g, ' ').replace(/[,]/g, '');
                    rowData.push(`"${value}"`);
                });
                csv.push(rowData.join(','));
            });

            // Download CSV
            const csvContent = csv.join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `column_analysis_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();
        }

        function exportToExcel() {
            // For Excel export, we'll use CSV format with Excel-compatible encoding
            exportToCSV();
            // In a real implementation, you might want to use a library like SheetJS
            alert('تم تصدير البيانات بتنسيق Excel متوافق');
        }

        function exportToPDF() {
            // For PDF export, we'll create a simple HTML-based PDF
            const table = document.getElementById('columnsTable');
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head>
                    <title>تحليل الأعمدة - تقرير PDF</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; }
                        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                        th { background-color: #f2f2f2; }
                        .badge { padding: 4px 8px; border-radius: 4px; }
                        .bg-primary { background-color: #007bff; color: white; }
                        .bg-secondary { background-color: #6c757d; color: white; }
                        .bg-success { background-color: #28a745; color: white; }
                        .bg-warning { background-color: #ffc107; color: black; }
                        .bg-info { background-color: #17a2b8; color: white; }
                        .bg-danger { background-color: #dc3545; color: white; }
                    </style>
                </head>
                <body>
                    <h1>تقرير تحليل الأعمدة</h1>
                    <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
                    ${table.outerHTML}
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        // Auto-refresh functionality (optional)
        function checkStatus() {
            fetch('/progress/{{ session_id }}')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'processing') {
                        // Still processing, check again in 2 seconds
                        setTimeout(checkStatus, 2000);
                    }
                })
                .catch(error => console.error('Error checking status:', error));
        }

        // Export charts functionality
        function exportCharts() {
            // Import jsPDF dynamically
            if (typeof jspdf === 'undefined') {
                const script = document.createElement('script');
                script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
                script.onload = () => exportChartsPDF();
                document.head.appendChild(script);
            } else {
                exportChartsPDF();
            }
        }

        function exportChartsPDF() {
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF();

            // Find all chart canvases
            const canvases = document.querySelectorAll('[id^="chart-"]');

            if (canvases.length === 0) {
            if (canvases.length === 0) {
                alert('لا توجد رسوم بيانية للتصدير');
                return;
            }

            canvases.forEach((canvas, index) => {
                try {
                    // Convert canvas to image
                    const imgData = canvas.toDataURL('image/png');

                    // Add new page for each chart (except first)
                    if (index > 0) {
                        pdf.addPage();
                    }

                    // Add chart title
                    const chartTitle = canvas.closest('.card').querySelector('.card-header h5');
                    const title = chartTitle ? chartTitle.textContent.trim() : `الرسم البياني ${index + 1}`;

                    pdf.setFontSize(16);
                    pdf.text(title, 20, 20);

                    // Add image (resize to fit page)
                    const imgWidth = 170;
                    const imgHeight = (canvas.height / canvas.width) * imgWidth;

                    if (imgHeight > 250) {
                        // If too tall, scale down
                        const scale = 250 / imgHeight;
                        pdf.addImage(imgData, 'PNG', 20, 30, imgWidth * scale, 250);
                    } else {
                        pdf.addImage(imgData, 'PNG', 20, 30, imgWidth, imgHeight);
                    }

                    processedCharts++;

                } catch (error) {
                    console.error('خطأ في تصدير الرسم البياني:', error);
                }
            });

            // Save PDF
            const fileName = `charts_export_${new Date().toISOString().split('T')[0]}.pdf`;
            pdf.save(fileName);

            alert(`تم تصدير ${processedCharts} رسم بياني إلى ملف PDF`);
        }

        // Alternative: Export individual chart images
        function exportChartImages() {
            const canvases = document.querySelectorAll('[id^="chart-"]');

            canvases.forEach((canvas, index) => {
                try {
                    // Convert canvas to blob
                    canvas.toBlob((blob) => {
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `chart_${index + 1}.png`;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);
                    });
                } catch (error) {
                    console.error('خطأ في تصدير صورة الرسم البياني:', error);
                }
            });
        }

        // Merge Analysis Table Functions
        let mergeTableSortDirection = 'asc';
        let mergeTableSortColumn = -1;

        function sortMergeTable(columnIndex) {
            mergeTableSortColumn = columnIndex;
            mergeTableSortDirection = mergeTableSortDirection === 'asc' ? 'desc' : 'asc';

            const tbody = document.querySelector("#personTable tbody");
            if (!tbody) return;

            const rows = Array.from(tbody.querySelectorAll('tr'));

            rows.sort((a, b) => {
                const aValue = getMergeTableCellValue(a, columnIndex);
                const bValue = getMergeTableCellValue(b, columnIndex);

                if (mergeTableSortDirection === 'asc') {
                    return aValue > bValue ? 1 : -1;
                } else {
                    return aValue < bValue ? 1 : -1;
                }
            });

            // Re-append sorted rows
            rows.forEach(row => tbody.appendChild(row));

            // Update sort icons
            updateMergeTableSortIcons(columnIndex);
        }

        function getMergeTableCellValue(row, columnIndex) {
            const cells = row.querySelectorAll('td');
            if (cells.length > columnIndex) {
                let text = cells[columnIndex].textContent.trim();

                // Clean up text for sorting
                text = text.replace(/[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F1E0}-\u{1F1FF}]/gu, ''); // Remove emojis
                text = text.replace(/^\d+\.\s*/, ''); // Remove numbering
                text = text.replace(/\s+/g, ' ').trim(); // Normalize spaces

                return text || '';
            }
            return '';
        }

        function updateMergeTableSortIcons(activeColumn) {
            // Reset all sort icons
            const headers = document.querySelectorAll('#personTable thead th');
            headers.forEach((header, index) => {
                const icon = header.querySelector('.sort-icon');
                if (icon) {
                    icon.style.opacity = '0.5';
                    header.classList.remove('sort-asc', 'sort-desc');
                }
            });

            // Set active sort icon
            if (activeColumn >= 0) {
                const activeHeader = headers[activeColumn];
                const icon = activeHeader.querySelector('.sort-icon');
                if (icon) {
                    icon.style.opacity = '1';
                    activeHeader.classList.add(mergeTableSortDirection === 'asc' ? 'sort-asc' : 'sort-desc');
                }
            }
        }

        // Global variables for pagination
        let currentPage = 1;
        let pageSize = 50;
        let totalRecords = 0;
        let filteredData = [];

        function populatePersonRecordTable(data) {
            console.log('Populating table with data:', data.length, 'records');

            try {
                // Store data globally for pagination
                filteredData = data;
                totalRecords = data.length;

                // Update pagination info
                updatePaginationInfo();

                // Display current page
                displayCurrentPage();

                console.log('Table populated successfully with pagination');
            } catch (error) {
                console.error('Error in populatePersonRecordTable:', error);
                // Show error in table
                const tbody = document.querySelector("#personTable tbody");
                if (tbody) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="8" class="text-center">
                                <div class="alert alert-danger mb-0">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>خطأ في عرض البيانات</strong>
                                    <br>
                                    <small class="text-muted">حدث خطأ أثناء عرض البيانات في الجدول</small>
                                </div>
                            </td>
                        </tr>
                    `;
                }
            }
        }

        function updatePaginationInfo() {
            const totalPages = Math.ceil(totalRecords / pageSize);
            const startRecord = (currentPage - 1) * pageSize + 1;
            const endRecord = Math.min(currentPage * pageSize, totalRecords);

            // Update total count display
            const totalCountElement = document.getElementById('mergeTotalCount');
            if (totalCountElement) {
                totalCountElement.textContent = `من أصل ${totalRecords}`;
            }

            // Update current page and total pages display
            const currentPageElement = document.getElementById('currentPageNum');
            const totalPagesElement = document.getElementById('totalPagesNum');
            if (currentPageElement) currentPageElement.textContent = currentPage;
            if (totalPagesElement) totalPagesElement.textContent = totalPages;

            // Update page jumper max value
            const pageJumper = document.getElementById('pageJumper');
            if (pageJumper) {
                pageJumper.max = totalPages;
                pageJumper.value = currentPage;
            }

            // Update info div
            const tbody = document.querySelector("#personTable tbody");
            if (tbody) {
                // Remove any existing info div
                const existingInfo = tbody.parentNode.querySelector('.table-info');
                if (existingInfo) {
                    existingInfo.remove();
                }

                const infoDiv = document.createElement("div");
                infoDiv.className = 'table-info';
                infoDiv.innerHTML = `<p style="text-align: center; margin-bottom: 20px; font-weight: bold; color: #007bff;">عرض ${startRecord} - ${endRecord} من ${totalRecords} سجل (صفحة ${currentPage} من ${totalPages})</p>`;
                tbody.parentNode.insertBefore(infoDiv, tbody);
            }

            // Update pagination controls
            updatePaginationControls();
        }

        function displayCurrentPage() {
            const tbody = document.querySelector("#personTable tbody");
            if (!tbody) {
                console.error('Table body not found');
                return;
            }

            try {
                tbody.innerHTML = '';

                const startIndex = (currentPage - 1) * pageSize;
                const endIndex = Math.min(startIndex + pageSize, totalRecords);
                const pageData = filteredData.slice(startIndex, endIndex);

                // Process records in chunks to prevent UI blocking
                const chunkSize = 10;
                let currentChunk = 0;

                function processChunk() {
                    const chunkStart = currentChunk * chunkSize;
                    const chunkEnd = Math.min(chunkStart + chunkSize, pageData.length);
                    const chunk = pageData.slice(chunkStart, chunkEnd);

                    chunk.forEach((candidate, index) => {
                        try {
                            const globalIndex = startIndex + chunkStart + index;

                            // Use the correct data structure from merge_candidates
                            const insuranceNumbers = candidate.insurance_numbers || [];
                            const manualNumbers = candidate.manual_numbers || [];
                            const allBasicNumbers = [...insuranceNumbers];

                            // Extract birth date from additional_info (simplified)
                            let birthDate = 'غير محدد';
                            let personName = candidate.person_name || 'غير محدد';

                            // Simplified date extraction
                            if (candidate.person_name) {
                                const dateMatch = candidate.person_name.match(/\d{4}-\d{2}-\d{2}|\d{2}\/\d{2}\/\d{4}|\d{2}-\d{2}-\d{4}/);
                                if (dateMatch) {
                                    birthDate = dateMatch[0];
                                }
                            }

                            if (birthDate === 'غير محدد' && candidate.additional_info && Array.isArray(candidate.additional_info)) {
                                for (const info of candidate.additional_info) {
                                    if (info && typeof info === 'string' && info !== 'غير محدد' && info.trim() !== '') {
                                        const dateMatch = info.match(/\d{4}-\d{2}-\d{2}|\d{2}\/\d{2}\/\d{4}|\d{2}-\d{2}-\d{4}/);
                                        if (dateMatch) {
                                            birthDate = dateMatch[0];
                                            break;
                                        }
                                    }
                                }
                            }

                            // Determine note (simplified logic)
                            let note = '';
                            let noteClass = 'note-review';
                            if (insuranceNumbers.length > 1) {
                                note = '🚨 تضارب أرقام أساسية';
                                noteClass = 'note-conflict';
                            } else if (manualNumbers.length > 0 && allBasicNumbers.length > 0) {
                                note = '⚠️ يحتاج دمج';
                                noteClass = 'note-merge';
                            } else if (allBasicNumbers.length === 1 && manualNumbers.length === 1) {
                                note = '✅ طبيعي';
                                noteClass = 'note-normal';
                            } else {
                                note = '❓ يحتاج مراجعة';
                                noteClass = 'note-review';
                            }

                            const row = document.createElement("tr");
                            row.innerHTML = `
                                <td class="person-name">${personName || '<span class="text-muted">غير محدد</span>'}</td>
                                <td>${birthDate !== 'غير محدد' ? birthDate : '<span class="text-muted">غير محدد</span>'}</td>
                                <td class="insurance-numbers">${allBasicNumbers.length > 0 ? allBasicNumbers.join(' • ') : '<span class="text-muted">لا يوجد</span>'}</td>
                                <td class="manual-numbers">${manualNumbers.length > 0 ? manualNumbers.join(' • ') : '<span class="text-muted">لا يوجد</span>'}</td>
                                <td class="document-number">${candidate.document_number || '<span class="text-muted">غير محدد</span>'}</td>
                                <td><span class="count-info">${allBasicNumbers.length}</span></td>
                                <td><span class="count-info">${manualNumbers.length}</span></td>
                                <td><span class="merge-reason">${note}</span></td>
                            `;
                            tbody.appendChild(row);

                        } catch (error) {
                            console.error('Error processing candidate record:', error, candidate);
                            // Add error row
                            const errorRow = document.createElement("tr");
                            errorRow.innerHTML = `
                                <td colspan="8" class="text-center text-danger">
                                    <small>خطأ في معالجة السجل</small>
                                </td>
                            `;
                            tbody.appendChild(errorRow);
                        }
                    });

                    currentChunk++;

                    // Process next chunk or finish
                    if (currentChunk * chunkSize < pageData.length) {
                        setTimeout(processChunk, 10); // Small delay to prevent UI blocking
                    } else {
                        console.log('Page', currentPage, 'displayed with', pageData.length, 'records');
                    }
                }

                // Start processing chunks
                processChunk();

            } catch (error) {
                console.error('Error in displayCurrentPage:', error);
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="text-center">
                            <div class="alert alert-danger mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>خطأ في عرض الصفحة</strong>
                                <br>
                                <small class="text-muted">حدث خطأ أثناء عرض البيانات</small>
                            </div>
                        </td>
                    </tr>
                `;
            }
        }

        function updatePaginationControls() {
            const paginationElement = document.getElementById('mergePagination');
            if (!paginationElement) return;

            const totalPages = Math.ceil(totalRecords / pageSize);
            let paginationHtml = '';

            // Previous button
            paginationHtml += `
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${currentPage - 1})" aria-label="السابق">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            `;

            // Page numbers
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(1)">1</a></li>`;
                if (startPage > 2) {
                    paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                paginationHtml += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                    </li>
                `;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${totalPages})">${totalPages}</a></li>`;
            }

            // Next button
            paginationHtml += `
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${currentPage + 1})" aria-label="التالي">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            `;

            paginationElement.innerHTML = paginationHtml;
        }

        function changePage(page) {
            const totalPages = Math.ceil(totalRecords / pageSize);
            if (page < 1 || page > totalPages) return;

            // Save current scroll position
            const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;

            currentPage = page;
            displayCurrentPage();
            updatePaginationInfo();

            // Restore scroll position after a short delay to ensure DOM is updated
            setTimeout(() => {
                window.scrollTo(0, scrollPosition);
            }, 10);
        }

        function changeMergePageSize() {
            const pageSizeSelect = document.getElementById('mergePageSize');
            if (pageSizeSelect) {
                pageSize = parseInt(pageSizeSelect.value);
                currentPage = 1; // Reset to first page
                displayCurrentPage();
                updatePaginationInfo();
            }
        }

        function jumpToPage(pageNum) {
            const page = parseInt(pageNum);
            if (page && page > 0) {
                changePage(page);
            }
        }

        function filterMergeTable() {
            const searchInput = document.getElementById('mergeSearchInput');
            const noteFilter = document.getElementById('mergeNoteFilter');

            const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
            const noteValue = noteFilter ? noteFilter.value : '';

            // Filter data
            filteredData = window.mergeData.filter(candidate => {
                // Search filter
                const personName = (candidate.person_name || '').toLowerCase();
                const insuranceNumbers = (candidate.insurance_numbers || []).join(' ').toLowerCase();
                const manualNumbers = (candidate.manual_numbers || []).join(' ').toLowerCase();

                const matchesSearch = !searchTerm ||
                    personName.includes(searchTerm) ||
                    insuranceNumbers.includes(searchTerm) ||
                    manualNumbers.includes(searchTerm);

                // Note filter
                let matchesNote = true;
                if (noteValue) {
                    const insuranceNumbers = candidate.insurance_numbers || [];
                    const manualNumbers = candidate.manual_numbers || [];

                    switch(noteValue) {
                        case 'تضارب':
                            matchesNote = insuranceNumbers.length > 1;
                            break;
                        case 'يحتاج دمج':
                            matchesNote = manualNumbers.length > 0 && insuranceNumbers.length > 0;
                            break;
                        case 'طبيعي':
                            matchesNote = insuranceNumbers.length === 1 && manualNumbers.length === 1;
                            break;
                        case 'مراجعة':
                            matchesNote = insuranceNumbers.length === 0 || (insuranceNumbers.length === 1 && manualNumbers.length !== 1);
                            break;
                    }
                }

                return matchesSearch && matchesNote;
            });

            totalRecords = filteredData.length;
            currentPage = 1; // Reset to first page
            displayCurrentPage();
            updatePaginationInfo();
        }

        function clearMergeSearch() {
            const searchInput = document.getElementById('mergeSearchInput');
            if (searchInput) {
                searchInput.value = '';
                filterMergeTable();
            }
        }

        function resetMergeFilters() {
            const searchInput = document.getElementById('mergeSearchInput');
            const noteFilter = document.getElementById('mergeNoteFilter');

            if (searchInput) searchInput.value = '';
            if (noteFilter) noteFilter.value = '';

            filterMergeTable();
        }

        // Export functions for merge analysis table
        function exportMergeAnalysisToCSV() {
            exportTableAsCSV('personTable', 'merge_analysis.csv');
        }

        function exportMergeAnalysisToExcel() {
            exportTableAsCSV('personTable', 'merge_analysis.xlsx');
        }

        function exportMergeAnalysisToPDF() {
            try {
                // Get session ID from the page
                const sessionId = document.querySelector('input[name="session_id"]')?.value ||
                                  document.getElementById('sessionId')?.value ||
                                  window.location.pathname.split('/').pop();

                if (!sessionId) {
                    alert('لم يتم العثور على معرف الجلسة');
                    return;
                }

                // Show loading message
                const loadingMessage = document.createElement('div');
                loadingMessage.className = 'alert alert-info';
                loadingMessage.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري إنشاء ملف PDF...';
                document.querySelector('.container').insertBefore(loadingMessage, document.querySelector('.container').firstChild);

                // Create the export URL
                const exportUrl = `/export/merge-analysis/pdf/${sessionId}`;

                // Create a temporary link and trigger download
                const link = document.createElement('a');
                link.href = exportUrl;
                link.style.display = 'none';
                document.body.appendChild(link);

                // Remove loading message after a short delay
                setTimeout(() => {
                    if (loadingMessage && loadingMessage.parentNode) {
                        loadingMessage.remove();
                    }
                    link.click();
                    document.body.removeChild(link);
                }, 1000);

            } catch (error) {
                console.error('Error exporting PDF:', error);
                alert('حدث خطأ أثناء تصدير ملف PDF');
            }
        }

        function exportMergeAnalysisToJSON() {
            const table = document.getElementById('personTable');
            if (!table) {
                alert('الجدول غير موجود');
                return;
            }
            const data = [];
            const headers = [];
            const headerCells = table.querySelectorAll('thead th');
            headerCells.forEach(cell => headers.push(cell.innerText.trim()));

            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const rowData = {};
                const cells = row.querySelectorAll('td');
                cells.forEach((cell, index) => {
                    if (headers[index]) {
                        rowData[headers[index]] = cell.innerText.trim();
                    }
                });
                if (Object.keys(rowData).length > 0) {
                    data.push(rowData);
                }
            });

            const jsonContent = JSON.stringify(data, null, 2);
            const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
            const link = document.createElement('a');
            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', 'merge_analysis.json');
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }

        // PersonRecord Table Functions
        function initializePersonRecordTable() {
            console.log('Initializing PersonRecord table...');

            try {
                // Get data from global analysisData first (more reliable)
                let mergeData = null;

                if (window.analysisData && window.analysisData.analysis && window.analysisData.analysis.sector_specific && window.analysisData.analysis.sector_specific.merge_analysis) {
                    mergeData = window.analysisData.analysis.sector_specific.merge_analysis.merge_candidates || [];
                    console.log('Merge data from global analysisData:', mergeData.length);
                }

                // If no data from global, try script tag as fallback
                if ((!mergeData || mergeData.length === 0)) {
                    const mergeDataElement = document.getElementById('mergeAnalysisData');
                    if (mergeDataElement) {
                        try {
                            const scriptData = JSON.parse(mergeDataElement.textContent);
                            if (scriptData && scriptData.length > 0) {
                                mergeData = scriptData;
                                console.log('Merge data from script tag:', mergeData.length);
                            }
                        } catch (e) {
                            console.error('Error parsing merge data from script tag:', e);
                        }
                    }
                }

                console.log('Final merge data length:', mergeData ? mergeData.length : 'null');

                if (!mergeData || mergeData.length === 0) {
                    console.log('No merge data available');
                    // Show message in table
                    const tbody = document.querySelector("#personTable tbody");
                    if (tbody) {
                        tbody.innerHTML = `
                            <tr>
                                <td colspan="8" class="text-center">
                                    <div class="alert alert-warning mb-0">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <strong>لا توجد بيانات مرشحين للدمج متاحة</strong>
                                        <br>
                                        <small class="text-muted">تحقق من السجلات للتأكد من وجود بيانات الدمج</small>
                                    </div>
                                </td>
                            </tr>
                        `;
                    }
                    return;
                }

                // Limit data size to prevent performance issues
                const maxRecords = 1000; // Limit to 1000 records for performance
                if (mergeData.length > maxRecords) {
                    console.warn(`Limiting merge data from ${mergeData.length} to ${maxRecords} records for performance`);
                    mergeData = mergeData.slice(0, maxRecords);
                }

                // Store globally for filtering
                window.mergeData = mergeData;

                populatePersonRecordTable(mergeData);
                console.log('PersonRecord table populated with', mergeData.length, 'records');

                // Show success message
                const successMessage = document.createElement('div');
                successMessage.className = 'alert alert-success mt-3';
                successMessage.innerHTML = `
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>تم تحميل البيانات بنجاح!</strong>
                    تم العثور على ${mergeData.length} مرشح للدمج
                `;
                const tableContainer = document.querySelector("#personTable").parentNode;
                if (tableContainer) {
                    tableContainer.insertBefore(successMessage, document.querySelector("#personTable"));
                }

                // Remove success message after 3 seconds
                setTimeout(() => {
                    if (successMessage && successMessage.parentNode) {
                        successMessage.remove();
                    }
                }, 3000);

            } catch (error) {
                console.error('Critical error in initializePersonRecordTable:', error);

                // Show error message in table
                const tbody = document.querySelector("#personTable tbody");
                if (tbody) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="8" class="text-center">
                                <div class="alert alert-danger mb-0">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>خطأ في تحميل بيانات الجدول</strong>
                                    <br>
                                    <small class="text-muted">حدث خطأ أثناء معالجة البيانات. يرجى إعادة تحميل الصفحة.</small>
                                </div>
                            </td>
                        </tr>
                    `;
                }
            }
        }

        // Initialize table sorting
        document.addEventListener('DOMContentLoaded', function() {
            // Add CSS for sortable headers
            const style = document.createElement('style');
            style.textContent = `
                .sortable {
                    cursor: pointer;
                    user-select: none;
                    position: relative;
                }
                .sortable:hover {
                    background-color: rgba(255, 255, 255, 0.1);
                }
                .sort-icon {
                    margin-left: 5px;
                    opacity: 0.5;
                }
                .sortable:hover .sort-icon {
                    opacity: 1;
                }
            `;
            document.head.appendChild(style);

            // Check status on page load
            checkStatus();

            // Initialize merge analysis table if data exists
            initializePersonRecordTable();
        });
    </script>
</body>
</html>