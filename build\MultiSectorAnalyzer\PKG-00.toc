('E:\\\u200f\u200fتحليل حميع القطاعات - نسخه محدثه  19-9 مساءاً - '
 'نسخة\\build\\MultiSectorAnalyzer\\MultiSectorAnalyzer.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'E:\\\u200f\u200fتحليل حميع القطاعات - نسخه محدثه  19-9 مساءاً - '
   'نسخة\\build\\MultiSectorAnalyzer\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'E:\\\u200f\u200fتحليل حميع القطاعات - نسخه محدثه  19-9 مساءاً - '
   'نسخة\\build\\MultiSectorAnalyzer\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\\u200f\u200fتحليل حميع القطاعات - نسخه محدثه  19-9 مساءاً - '
   'نسخة\\build\\MultiSectorAnalyzer\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\\u200f\u200fتحليل حميع القطاعات - نسخه محدثه  19-9 مساءاً - '
   'نسخة\\build\\MultiSectorAnalyzer\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\\u200f\u200fتحليل حميع القطاعات - نسخه محدثه  19-9 مساءاً - '
   'نسخة\\build\\MultiSectorAnalyzer\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\\u200f\u200fتحليل حميع القطاعات - نسخه محدثه  19-9 مساءاً - '
   'نسخة\\build\\MultiSectorAnalyzer\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'E:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'E:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'E:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'E:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_mplconfig',
   'E:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_mplconfig.py',
   'PYSOURCE'),
  ('run',
   'E:\\\u200f\u200fتحليل حميع القطاعات - نسخه محدثه  19-9 مساءاً - '
   'نسخة\\run.py',
   'PYSOURCE'),
  ('sklearn\\.libs\\msvcp140.dll',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\.libs\\msvcp140.dll',
   'BINARY'),
  ('sklearn\\.libs\\vcomp140.dll',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\.libs\\vcomp140.dll',
   'BINARY'),
  ('python313.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\python313.dll',
   'BINARY'),
  ('scipy.libs\\libscipy_openblas-48c358d105077551cc9cc3ba79387ed5.dll',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy.libs\\libscipy_openblas-48c358d105077551cc9cc3ba79387ed5.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-a4c2229bdc2a2a630acdc095b4d86008.dll',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\numpy.libs\\msvcp140-a4c2229bdc2a2a630acdc095b4d86008.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-860d95b1c38e637ce4509f5fa24fbf2a.dll',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-860d95b1c38e637ce4509f5fa24fbf2a.dll',
   'BINARY'),
  ('_decimal.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_ccallback_c.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\_lib\\_ccallback_c.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\numpy\\random\\_philox.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\numpy\\random\\_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\numpy\\random\\_common.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_fblas.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\linalg\\_fblas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_flapack.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\linalg\\_flapack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_lapack.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\linalg\\cython_lapack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_blas.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\linalg\\cython_blas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_update.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\linalg\\_decomp_update.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ellip_harm_2.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\special\\_ellip_harm_2.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highspy\\_core.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\optimize\\_highspy\\_core.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highspy\\_highs_options.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\optimize\\_highspy\\_highs_options.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_direct.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\optimize\\_direct.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_qmc_cy.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\stats\\_qmc_cy.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_sobol.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\stats\\_sobol.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_wrap.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\spatial\\_distance_wrap.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_pybind.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\spatial\\_distance_pybind.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_hausdorff.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\spatial\\_hausdorff.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_tools.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_tools.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_reordering.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_reordering.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_matching.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_matching.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_flow.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_flow.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_min_spanning_tree.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_min_spanning_tree.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_traversal.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_traversal.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_shortest_path.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_shortest_path.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_rank_filter_1d.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\ndimage\\_rank_filter_1d.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_nd_image.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\ndimage\\_nd_image.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_ni_label.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\ndimage\\_ni_label.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_path.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\_path.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\ft2font.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\ft2font.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\backends\\_backend_agg.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\backends\\_backend_agg.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('contourpy\\_contourpy.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\contourpy\\_contourpy.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\PIL\\_webp.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\PIL\\_imagingtk.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_avif.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\PIL\\_avif.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\PIL\\_imagingcms.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\PIL\\_imagingmath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\PIL\\_imaging.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_image.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\_image.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_tri.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\_tri.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_qhull.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\_qhull.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('kiwisolver\\_cext.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\kiwisolver\\_cext.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_c_internal_utils.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\_c_internal_utils.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats_pythran.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\stats\\_stats_pythran.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\fft\\_pocketfft\\pypocketfft.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\pypocketfft.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_uarray\\_uarray.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\_lib\\_uarray\\_uarray.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_rcont\\rcont.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\stats\\_rcont\\rcont.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_qmvnt_cy.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\stats\\_qmvnt_cy.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_ansari_swilk_statistics.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\stats\\_ansari_swilk_statistics.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_levy_stable\\levyst.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\stats\\_levy_stable\\levyst.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_biasedurn.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\stats\\_biasedurn.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\stats\\_stats.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\cython_special.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\special\\cython_special.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\transform\\_rotation.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\spatial\\transform\\_rotation.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\transform\\_rigid_transform.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\spatial\\transform\\_rigid_transform.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_voronoi.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\spatial\\_voronoi.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_qhull.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\spatial\\_qhull.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_ckdtree.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\spatial\\_ckdtree.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_cyutility.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\_cyutility.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_pava_pybind.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\optimize\\_pava_pybind.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsq\\givens_elimination.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\optimize\\_lsq\\givens_elimination.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsap.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\optimize\\_lsap.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_interpolative.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\linalg\\_decomp_interpolative.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_bglu_dense.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\optimize\\_bglu_dense.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_slsqplib.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\optimize\\_slsqplib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_zeros.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\optimize\\_zeros.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_minpack.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\optimize\\_minpack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_trlib\\_trlib.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\optimize\\_trlib\\_trlib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_moduleTNC.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\optimize\\_moduleTNC.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lbfgsb.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\optimize\\_lbfgsb.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_group_columns.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\optimize\\_group_columns.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_lsoda.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\integrate\\_lsoda.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_dop.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\integrate\\_dop.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_vode.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\integrate\\_vode.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_quadpack.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\integrate\\_quadpack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_odepack.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\integrate\\_odepack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_gufuncs.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\special\\_gufuncs.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_special_ufuncs.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\special\\_special_ufuncs.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_comb.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\special\\_comb.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_specfun.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\special\\_specfun.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\special\\_ufuncs.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs_cxx.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\special\\_ufuncs_cxx.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_sqrtm_triu.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_sqrtm_triu.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_linalg_pythran.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\linalg\\_linalg_pythran.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_expm.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_expm.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_schur_sqrtm.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_schur_sqrtm.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_lu_cython.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\linalg\\_decomp_lu_cython.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_solve_toeplitz.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\linalg\\_solve_toeplitz.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_cythonized_array_utils.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\linalg\\_cythonized_array_utils.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_csparsetools.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\sparse\\_csparsetools.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_sparsetools.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\sparse\\_sparsetools.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_zpropack.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_zpropack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_cpropack.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_cpropack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_dpropack.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_dpropack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_spropack.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_spropack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_dsolve\\_superlu.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\_superlu.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rgi_cython.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_rgi_cython.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_interpnd.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_interpnd.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rbfinterp_pythran.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_rbfinterp_pythran.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_ppoly.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_ppoly.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_dierckx.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_dierckx.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_dfitpack.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_dfitpack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_fitpack.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_fitpack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_fpumode.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\_lib\\_fpumode.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\messagestream.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\_lib\\messagestream.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\interval.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\ops.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\hashtable.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\index.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\markupsafe\\_speedups.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\parsers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\window\\indexers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\internals.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\indexing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\groupby.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\hashing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\arrays.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\sparse.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\algos.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\json.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\join.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\reshape.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\properties.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\writers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\missing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_datetime.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\pandas_datetime.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_parser.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\pandas_parser.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\lib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\sparsefuncs_fast.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\sparsefuncs_fast.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\murmurhash.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\murmurhash.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\arrayfuncs.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\arrayfuncs.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_weight_vector.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_weight_vector.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_vector_sentinel.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_vector_sentinel.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_typedefs.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_typedefs.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_sorting.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_sorting.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_seq_dataset.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_seq_dataset.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_random.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_random.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_openmp_helpers.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_openmp_helpers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_isfinite.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_isfinite.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_heap.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_heap.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_fast_dict.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_fast_dict.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_cython_blas.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_cython_blas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\tree\\_utils.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\_utils.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\tree\\_tree.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\_tree.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\tree\\_splitter.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\_splitter.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\tree\\_partitioner.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\_partitioner.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\tree\\_criterion.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\_criterion.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\svm\\_newrand.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\_newrand.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\svm\\_libsvm_sparse.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\_libsvm_sparse.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\svm\\_libsvm.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\_libsvm.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\svm\\_liblinear.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\_liblinear.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\preprocessing\\_target_encoder_fast.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_target_encoder_fast.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\preprocessing\\_csr_polynomial_expansion.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_csr_polynomial_expansion.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\neighbors\\_quad_tree.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_quad_tree.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\neighbors\\_partition_nodes.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_partition_nodes.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\neighbors\\_kd_tree.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_kd_tree.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\neighbors\\_ball_tree.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_ball_tree.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\cluster\\_expected_mutual_info_fast.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\cluster\\_expected_mutual_info_fast.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_fast.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_fast.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors_classmode.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors_classmode.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_base.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_base.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin_classmode.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin_classmode.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_dist_metrics.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_dist_metrics.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\manifold\\_utils.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\manifold\\_utils.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\manifold\\_barnes_hut_tsne.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\manifold\\_barnes_hut_tsne.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\linear_model\\_sgd_fast.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_sgd_fast.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\linear_model\\_sag_fast.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_sag_fast.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\linear_model\\_cd_fast.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_cd_fast.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\feature_extraction\\_hashing_fast.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_extraction\\_hashing_fast.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\splitting.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\splitting.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\histogram.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\histogram.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\common.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\common.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_predictor.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_predictor.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_gradient_boosting.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_gradient_boosting.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_bitset.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_bitset.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_binning.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_binning.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\ensemble\\_gradient_boosting.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_gradient_boosting.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\decomposition\\_online_lda_fast.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_online_lda_fast.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\decomposition\\_cdnmf_fast.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_cdnmf_fast.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\datasets\\_svmlight_format_fast.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\_svmlight_format_fast.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\io\\_fast_matrix_market\\_fmm_core.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\io\\_fast_matrix_market\\_fmm_core.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\io\\matlab\\_streams.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\io\\matlab\\_streams.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\io\\matlab\\_mio5_utils.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\io\\matlab\\_mio5_utils.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\io\\matlab\\_mio_utils.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\io\\matlab\\_mio_utils.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\cluster\\_optimal_leaf_ordering.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\cluster\\_optimal_leaf_ordering.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\cluster\\_hierarchy.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\cluster\\_hierarchy.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\cluster\\_vq.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scipy\\cluster\\_vq.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_k_means_minibatch.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_minibatch.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_k_means_lloyd.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_lloyd.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_k_means_elkan.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_elkan.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_k_means_common.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_common.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_hierarchical_fast.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hierarchical_fast.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_hdbscan\\_tree.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_tree.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_hdbscan\\_reachability.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_reachability.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_hdbscan\\_linkage.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_linkage.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_dbscan_inner.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_dbscan_inner.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\_loss\\_loss.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\_loss\\_loss.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\_isotonic.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\_isotonic.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\_cyutility.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\_cyutility.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\__check_build\\_check_build.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\__check_build\\_check_build.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\testing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sas.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\sas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\byteswap.cp313-win_amd64.pyd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\_libs\\byteswap.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\python3.dll',
   'BINARY'),
  ('pandas.libs\\msvcp140-a4c2229bdc2a2a630acdc095b4d86008.dll',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas.libs\\msvcp140-a4c2229bdc2a2a630acdc095b4d86008.dll',
   'BINARY'),
  ('MSVCP140.dll',
   'C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin\\MSVCP140.dll',
   'BINARY'),
  ('VCOMP140.DLL', 'C:\\Windows\\system32\\VCOMP140.DLL', 'BINARY'),
  ('sqlite3.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.2032.0_x64__qbz5n2kfra8p0\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('database\\multi_sector_analyzer.db',
   'E:\\\u200f\u200fتحليل حميع القطاعات - نسخه محدثه  19-9 مساءاً - '
   'نسخة\\database\\multi_sector_analyzer.db',
   'DATA'),
  ('scikit_learn-1.7.2.dist-info\\INSTALLER',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scikit_learn-1.7.2.dist-info\\INSTALLER',
   'DATA'),
  ('scikit_learn-1.7.2.dist-info\\METADATA',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scikit_learn-1.7.2.dist-info\\METADATA',
   'DATA'),
  ('scikit_learn-1.7.2.dist-info\\RECORD',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scikit_learn-1.7.2.dist-info\\RECORD',
   'DATA'),
  ('scikit_learn-1.7.2.dist-info\\REQUESTED',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scikit_learn-1.7.2.dist-info\\REQUESTED',
   'DATA'),
  ('scikit_learn-1.7.2.dist-info\\WHEEL',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scikit_learn-1.7.2.dist-info\\WHEEL',
   'DATA'),
  ('scikit_learn-1.7.2.dist-info\\licenses\\COPYING',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\scikit_learn-1.7.2.dist-info\\licenses\\COPYING',
   'DATA'),
  ('sklearn\\__check_build\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\__check_build\\__init__.py',
   'DATA'),
  ('sklearn\\__check_build\\_check_build.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\__check_build\\_check_build.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\__check_build\\_check_build.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\__check_build\\_check_build.pyx',
   'DATA'),
  ('sklearn\\__check_build\\meson.build',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\__check_build\\meson.build',
   'DATA'),
  ('sklearn\\__init__.py',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\sklearn\\__init__.py',
   'DATA'),
  ('sklearn\\_build_utils\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\_build_utils\\__init__.py',
   'DATA'),
  ('sklearn\\_build_utils\\tempita.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\_build_utils\\tempita.py',
   'DATA'),
  ('sklearn\\_build_utils\\version.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\_build_utils\\version.py',
   'DATA'),
  ('sklearn\\_built_with_meson.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\_built_with_meson.py',
   'DATA'),
  ('sklearn\\_config.py',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\sklearn\\_config.py',
   'DATA'),
  ('sklearn\\_cyutility.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\_cyutility.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\_distributor_init.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\_distributor_init.py',
   'DATA'),
  ('sklearn\\_isotonic.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\_isotonic.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\_isotonic.pyx',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\sklearn\\_isotonic.pyx',
   'DATA'),
  ('sklearn\\_loss\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\_loss\\__init__.py',
   'DATA'),
  ('sklearn\\_loss\\_loss.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\_loss\\_loss.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\_loss\\_loss.pxd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\_loss\\_loss.pxd',
   'DATA'),
  ('sklearn\\_loss\\_loss.pyx.tp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\_loss\\_loss.pyx.tp',
   'DATA'),
  ('sklearn\\_loss\\link.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\_loss\\link.py',
   'DATA'),
  ('sklearn\\_loss\\loss.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\_loss\\loss.py',
   'DATA'),
  ('sklearn\\_loss\\meson.build',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\_loss\\meson.build',
   'DATA'),
  ('sklearn\\_loss\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\_loss\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\_loss\\tests\\test_link.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\_loss\\tests\\test_link.py',
   'DATA'),
  ('sklearn\\_loss\\tests\\test_loss.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\_loss\\tests\\test_loss.py',
   'DATA'),
  ('sklearn\\_min_dependencies.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\_min_dependencies.py',
   'DATA'),
  ('sklearn\\base.py',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\sklearn\\base.py',
   'DATA'),
  ('sklearn\\calibration.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\calibration.py',
   'DATA'),
  ('sklearn\\cluster\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\__init__.py',
   'DATA'),
  ('sklearn\\cluster\\_affinity_propagation.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_affinity_propagation.py',
   'DATA'),
  ('sklearn\\cluster\\_agglomerative.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_agglomerative.py',
   'DATA'),
  ('sklearn\\cluster\\_bicluster.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_bicluster.py',
   'DATA'),
  ('sklearn\\cluster\\_birch.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_birch.py',
   'DATA'),
  ('sklearn\\cluster\\_bisect_k_means.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_bisect_k_means.py',
   'DATA'),
  ('sklearn\\cluster\\_dbscan.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_dbscan.py',
   'DATA'),
  ('sklearn\\cluster\\_dbscan_inner.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_dbscan_inner.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\cluster\\_dbscan_inner.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_dbscan_inner.pyx',
   'DATA'),
  ('sklearn\\cluster\\_feature_agglomeration.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_feature_agglomeration.py',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\__init__.py',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\_linkage.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_linkage.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\_linkage.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_linkage.pyx',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\_reachability.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_reachability.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\_reachability.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_reachability.pyx',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\_tree.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_tree.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\_tree.pxd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_tree.pxd',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\_tree.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_tree.pyx',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\hdbscan.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\hdbscan.py',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\meson.build',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\meson.build',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\tests\\test_reachibility.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\tests\\test_reachibility.py',
   'DATA'),
  ('sklearn\\cluster\\_hierarchical_fast.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hierarchical_fast.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\cluster\\_hierarchical_fast.pxd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hierarchical_fast.pxd',
   'DATA'),
  ('sklearn\\cluster\\_hierarchical_fast.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hierarchical_fast.pyx',
   'DATA'),
  ('sklearn\\cluster\\_k_means_common.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_common.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\cluster\\_k_means_common.pxd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_common.pxd',
   'DATA'),
  ('sklearn\\cluster\\_k_means_common.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_common.pyx',
   'DATA'),
  ('sklearn\\cluster\\_k_means_elkan.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_elkan.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\cluster\\_k_means_elkan.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_elkan.pyx',
   'DATA'),
  ('sklearn\\cluster\\_k_means_lloyd.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_lloyd.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\cluster\\_k_means_lloyd.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_lloyd.pyx',
   'DATA'),
  ('sklearn\\cluster\\_k_means_minibatch.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_minibatch.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\cluster\\_k_means_minibatch.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_minibatch.pyx',
   'DATA'),
  ('sklearn\\cluster\\_kmeans.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py',
   'DATA'),
  ('sklearn\\cluster\\_mean_shift.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_mean_shift.py',
   'DATA'),
  ('sklearn\\cluster\\_optics.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_optics.py',
   'DATA'),
  ('sklearn\\cluster\\_spectral.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_spectral.py',
   'DATA'),
  ('sklearn\\cluster\\meson.build',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\meson.build',
   'DATA'),
  ('sklearn\\cluster\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\cluster\\tests\\common.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\tests\\common.py',
   'DATA'),
  ('sklearn\\cluster\\tests\\test_affinity_propagation.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\tests\\test_affinity_propagation.py',
   'DATA'),
  ('sklearn\\cluster\\tests\\test_bicluster.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\tests\\test_bicluster.py',
   'DATA'),
  ('sklearn\\cluster\\tests\\test_birch.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\tests\\test_birch.py',
   'DATA'),
  ('sklearn\\cluster\\tests\\test_bisect_k_means.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\tests\\test_bisect_k_means.py',
   'DATA'),
  ('sklearn\\cluster\\tests\\test_dbscan.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\tests\\test_dbscan.py',
   'DATA'),
  ('sklearn\\cluster\\tests\\test_feature_agglomeration.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\tests\\test_feature_agglomeration.py',
   'DATA'),
  ('sklearn\\cluster\\tests\\test_hdbscan.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\tests\\test_hdbscan.py',
   'DATA'),
  ('sklearn\\cluster\\tests\\test_hierarchical.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\tests\\test_hierarchical.py',
   'DATA'),
  ('sklearn\\cluster\\tests\\test_k_means.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\tests\\test_k_means.py',
   'DATA'),
  ('sklearn\\cluster\\tests\\test_mean_shift.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\tests\\test_mean_shift.py',
   'DATA'),
  ('sklearn\\cluster\\tests\\test_optics.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\tests\\test_optics.py',
   'DATA'),
  ('sklearn\\cluster\\tests\\test_spectral.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cluster\\tests\\test_spectral.py',
   'DATA'),
  ('sklearn\\compose\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\compose\\__init__.py',
   'DATA'),
  ('sklearn\\compose\\_column_transformer.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\compose\\_column_transformer.py',
   'DATA'),
  ('sklearn\\compose\\_target.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\compose\\_target.py',
   'DATA'),
  ('sklearn\\compose\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\compose\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\compose\\tests\\test_column_transformer.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\compose\\tests\\test_column_transformer.py',
   'DATA'),
  ('sklearn\\compose\\tests\\test_target.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\compose\\tests\\test_target.py',
   'DATA'),
  ('sklearn\\conftest.py',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\sklearn\\conftest.py',
   'DATA'),
  ('sklearn\\covariance\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\covariance\\__init__.py',
   'DATA'),
  ('sklearn\\covariance\\_elliptic_envelope.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\covariance\\_elliptic_envelope.py',
   'DATA'),
  ('sklearn\\covariance\\_empirical_covariance.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\covariance\\_empirical_covariance.py',
   'DATA'),
  ('sklearn\\covariance\\_graph_lasso.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\covariance\\_graph_lasso.py',
   'DATA'),
  ('sklearn\\covariance\\_robust_covariance.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\covariance\\_robust_covariance.py',
   'DATA'),
  ('sklearn\\covariance\\_shrunk_covariance.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\covariance\\_shrunk_covariance.py',
   'DATA'),
  ('sklearn\\covariance\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\covariance\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\covariance\\tests\\test_covariance.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\covariance\\tests\\test_covariance.py',
   'DATA'),
  ('sklearn\\covariance\\tests\\test_elliptic_envelope.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\covariance\\tests\\test_elliptic_envelope.py',
   'DATA'),
  ('sklearn\\covariance\\tests\\test_graphical_lasso.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\covariance\\tests\\test_graphical_lasso.py',
   'DATA'),
  ('sklearn\\covariance\\tests\\test_robust_covariance.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\covariance\\tests\\test_robust_covariance.py',
   'DATA'),
  ('sklearn\\cross_decomposition\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cross_decomposition\\__init__.py',
   'DATA'),
  ('sklearn\\cross_decomposition\\_pls.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cross_decomposition\\_pls.py',
   'DATA'),
  ('sklearn\\cross_decomposition\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cross_decomposition\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\cross_decomposition\\tests\\test_pls.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\cross_decomposition\\tests\\test_pls.py',
   'DATA'),
  ('sklearn\\datasets\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\__init__.py',
   'DATA'),
  ('sklearn\\datasets\\_arff_parser.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\_arff_parser.py',
   'DATA'),
  ('sklearn\\datasets\\_base.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\_base.py',
   'DATA'),
  ('sklearn\\datasets\\_california_housing.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\_california_housing.py',
   'DATA'),
  ('sklearn\\datasets\\_covtype.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\_covtype.py',
   'DATA'),
  ('sklearn\\datasets\\_kddcup99.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\_kddcup99.py',
   'DATA'),
  ('sklearn\\datasets\\_lfw.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\_lfw.py',
   'DATA'),
  ('sklearn\\datasets\\_olivetti_faces.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\_olivetti_faces.py',
   'DATA'),
  ('sklearn\\datasets\\_openml.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\_openml.py',
   'DATA'),
  ('sklearn\\datasets\\_rcv1.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\_rcv1.py',
   'DATA'),
  ('sklearn\\datasets\\_samples_generator.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\_samples_generator.py',
   'DATA'),
  ('sklearn\\datasets\\_species_distributions.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\_species_distributions.py',
   'DATA'),
  ('sklearn\\datasets\\_svmlight_format_fast.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\_svmlight_format_fast.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\_svmlight_format_fast.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\_svmlight_format_fast.pyx',
   'DATA'),
  ('sklearn\\datasets\\_svmlight_format_io.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\_svmlight_format_io.py',
   'DATA'),
  ('sklearn\\datasets\\_twenty_newsgroups.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\_twenty_newsgroups.py',
   'DATA'),
  ('sklearn\\datasets\\data\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\data\\__init__.py',
   'DATA'),
  ('sklearn\\datasets\\data\\breast_cancer.csv',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\data\\breast_cancer.csv',
   'DATA'),
  ('sklearn\\datasets\\data\\diabetes_data_raw.csv.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\data\\diabetes_data_raw.csv.gz',
   'DATA'),
  ('sklearn\\datasets\\data\\diabetes_target.csv.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\data\\diabetes_target.csv.gz',
   'DATA'),
  ('sklearn\\datasets\\data\\digits.csv.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\data\\digits.csv.gz',
   'DATA'),
  ('sklearn\\datasets\\data\\iris.csv',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\data\\iris.csv',
   'DATA'),
  ('sklearn\\datasets\\data\\linnerud_exercise.csv',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\data\\linnerud_exercise.csv',
   'DATA'),
  ('sklearn\\datasets\\data\\linnerud_physiological.csv',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\data\\linnerud_physiological.csv',
   'DATA'),
  ('sklearn\\datasets\\data\\wine_data.csv',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\data\\wine_data.csv',
   'DATA'),
  ('sklearn\\datasets\\descr\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\__init__.py',
   'DATA'),
  ('sklearn\\datasets\\descr\\breast_cancer.rst',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\breast_cancer.rst',
   'DATA'),
  ('sklearn\\datasets\\descr\\california_housing.rst',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\california_housing.rst',
   'DATA'),
  ('sklearn\\datasets\\descr\\covtype.rst',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\covtype.rst',
   'DATA'),
  ('sklearn\\datasets\\descr\\diabetes.rst',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\diabetes.rst',
   'DATA'),
  ('sklearn\\datasets\\descr\\digits.rst',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\digits.rst',
   'DATA'),
  ('sklearn\\datasets\\descr\\iris.rst',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\iris.rst',
   'DATA'),
  ('sklearn\\datasets\\descr\\kddcup99.rst',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\kddcup99.rst',
   'DATA'),
  ('sklearn\\datasets\\descr\\lfw.rst',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\lfw.rst',
   'DATA'),
  ('sklearn\\datasets\\descr\\linnerud.rst',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\linnerud.rst',
   'DATA'),
  ('sklearn\\datasets\\descr\\olivetti_faces.rst',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\olivetti_faces.rst',
   'DATA'),
  ('sklearn\\datasets\\descr\\rcv1.rst',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\rcv1.rst',
   'DATA'),
  ('sklearn\\datasets\\descr\\species_distributions.rst',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\species_distributions.rst',
   'DATA'),
  ('sklearn\\datasets\\descr\\twenty_newsgroups.rst',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\twenty_newsgroups.rst',
   'DATA'),
  ('sklearn\\datasets\\descr\\wine_data.rst',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\wine_data.rst',
   'DATA'),
  ('sklearn\\datasets\\images\\README.txt',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\images\\README.txt',
   'DATA'),
  ('sklearn\\datasets\\images\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\images\\__init__.py',
   'DATA'),
  ('sklearn\\datasets\\images\\china.jpg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\images\\china.jpg',
   'DATA'),
  ('sklearn\\datasets\\images\\flower.jpg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\images\\flower.jpg',
   'DATA'),
  ('sklearn\\datasets\\meson.build',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\meson.build',
   'DATA'),
  ('sklearn\\datasets\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\__init__.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\__init__.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1119\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119\\__init__.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jd-1119.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jd-1119.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdf-1119.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdf-1119.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdl-dn-adult-census-l-2-dv-1.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdl-dn-adult-census-l-2-dv-1.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdl-dn-adult-census-l-2-s-act-.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdl-dn-adult-census-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdq-1119.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdq-1119.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1119\\data-v1-dl-54002.arff.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119\\data-v1-dl-54002.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1590\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1590\\__init__.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1590\\api-v1-jd-1590.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1590\\api-v1-jd-1590.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1590\\api-v1-jdf-1590.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1590\\api-v1-jdf-1590.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1590\\api-v1-jdq-1590.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1590\\api-v1-jdq-1590.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1590\\data-v1-dl-1595261.arff.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1590\\data-v1-dl-1595261.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1\\__init__.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1\\api-v1-jd-1.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1\\api-v1-jd-1.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1\\api-v1-jdf-1.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1\\api-v1-jdf-1.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1\\api-v1-jdq-1.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1\\api-v1-jdq-1.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1\\data-v1-dl-1.arff.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1\\data-v1-dl-1.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\__init__.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jd-292.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jd-292.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jd-40981.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jd-40981.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdf-292.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdf-292.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdf-40981.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdf-40981.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdl-dn-australian-l-2-dv-1-s-dact.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdl-dn-australian-l-2-dv-1-s-dact.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdl-dn-australian-l-2-dv-1.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdl-dn-australian-l-2-dv-1.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdl-dn-australian-l-2-s-act-.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdl-dn-australian-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\data-v1-dl-49822.arff.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\data-v1-dl-49822.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_2\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2\\__init__.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jd-2.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jd-2.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdf-2.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdf-2.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdl-dn-anneal-l-2-dv-1.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdl-dn-anneal-l-2-dv-1.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdl-dn-anneal-l-2-s-act-.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdl-dn-anneal-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdq-2.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdq-2.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_2\\data-v1-dl-1666876.arff.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2\\data-v1-dl-1666876.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_3\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_3\\__init__.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_3\\api-v1-jd-3.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_3\\api-v1-jd-3.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_3\\api-v1-jdf-3.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_3\\api-v1-jdf-3.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_3\\api-v1-jdq-3.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_3\\api-v1-jdq-3.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_3\\data-v1-dl-3.arff.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_3\\data-v1-dl-3.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40589\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589\\__init__.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jd-40589.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jd-40589.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdf-40589.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdf-40589.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdl-dn-emotions-l-2-dv-3.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdl-dn-emotions-l-2-dv-3.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdl-dn-emotions-l-2-s-act-.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdl-dn-emotions-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdq-40589.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdq-40589.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40589\\data-v1-dl-4644182.arff.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589\\data-v1-dl-4644182.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\__init__.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jd-40675.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jd-40675.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdf-40675.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdf-40675.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdl-dn-glass2-l-2-dv-1-s-dact.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdl-dn-glass2-l-2-dv-1-s-dact.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdl-dn-glass2-l-2-dv-1.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdl-dn-glass2-l-2-dv-1.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdl-dn-glass2-l-2-s-act-.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdl-dn-glass2-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdq-40675.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdq-40675.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\data-v1-dl-4965250.arff.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\data-v1-dl-4965250.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40945\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40945\\__init__.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40945\\api-v1-jd-40945.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40945\\api-v1-jd-40945.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40945\\api-v1-jdf-40945.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40945\\api-v1-jdf-40945.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40945\\api-v1-jdq-40945.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40945\\api-v1-jdq-40945.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40945\\data-v1-dl-16826755.arff.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40945\\data-v1-dl-16826755.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40966\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966\\__init__.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jd-40966.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jd-40966.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdf-40966.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdf-40966.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdl-dn-miceprotein-l-2-dv-4.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdl-dn-miceprotein-l-2-dv-4.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdl-dn-miceprotein-l-2-s-act-.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdl-dn-miceprotein-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdq-40966.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdq-40966.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40966\\data-v1-dl-17928620.arff.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966\\data-v1-dl-17928620.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42074\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42074\\__init__.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42074\\api-v1-jd-42074.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42074\\api-v1-jd-42074.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42074\\api-v1-jdf-42074.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42074\\api-v1-jdf-42074.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42074\\api-v1-jdq-42074.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42074\\api-v1-jdq-42074.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42074\\data-v1-dl-21552912.arff.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42074\\data-v1-dl-21552912.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42585\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42585\\__init__.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42585\\api-v1-jd-42585.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42585\\api-v1-jd-42585.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42585\\api-v1-jdf-42585.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42585\\api-v1-jdf-42585.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42585\\api-v1-jdq-42585.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42585\\api-v1-jdq-42585.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42585\\data-v1-dl-21854866.arff.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42585\\data-v1-dl-21854866.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_561\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561\\__init__.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jd-561.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jd-561.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdf-561.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdf-561.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdl-dn-cpu-l-2-dv-1.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdl-dn-cpu-l-2-dv-1.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdl-dn-cpu-l-2-s-act-.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdl-dn-cpu-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdq-561.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdq-561.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_561\\data-v1-dl-52739.arff.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561\\data-v1-dl-52739.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_61\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61\\__init__.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jd-61.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jd-61.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdf-61.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdf-61.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdl-dn-iris-l-2-dv-1.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdl-dn-iris-l-2-dv-1.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdl-dn-iris-l-2-s-act-.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdl-dn-iris-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdq-61.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdq-61.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_61\\data-v1-dl-61.arff.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61\\data-v1-dl-61.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_62\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_62\\__init__.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_62\\api-v1-jd-62.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_62\\api-v1-jd-62.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_62\\api-v1-jdf-62.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_62\\api-v1-jdf-62.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_62\\api-v1-jdq-62.json.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_62\\api-v1-jdq-62.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_62\\data-v1-dl-52352.arff.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_62\\data-v1-dl-52352.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\svmlight_classification.txt',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\svmlight_classification.txt',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\svmlight_invalid.txt',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\svmlight_invalid.txt',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\svmlight_invalid_order.txt',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\svmlight_invalid_order.txt',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\svmlight_multilabel.txt',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\svmlight_multilabel.txt',
   'DATA'),
  ('sklearn\\datasets\\tests\\test_20news.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_20news.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\test_arff_parser.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_arff_parser.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\test_base.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_base.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\test_california_housing.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_california_housing.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\test_common.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_common.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\test_covtype.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_covtype.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\test_kddcup99.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_kddcup99.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\test_lfw.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_lfw.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\test_olivetti_faces.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_olivetti_faces.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\test_openml.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_openml.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\test_rcv1.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_rcv1.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\test_samples_generator.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_samples_generator.py',
   'DATA'),
  ('sklearn\\datasets\\tests\\test_svmlight_format.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_svmlight_format.py',
   'DATA'),
  ('sklearn\\decomposition\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\__init__.py',
   'DATA'),
  ('sklearn\\decomposition\\_base.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_base.py',
   'DATA'),
  ('sklearn\\decomposition\\_cdnmf_fast.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_cdnmf_fast.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\decomposition\\_cdnmf_fast.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_cdnmf_fast.pyx',
   'DATA'),
  ('sklearn\\decomposition\\_dict_learning.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_dict_learning.py',
   'DATA'),
  ('sklearn\\decomposition\\_factor_analysis.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_factor_analysis.py',
   'DATA'),
  ('sklearn\\decomposition\\_fastica.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_fastica.py',
   'DATA'),
  ('sklearn\\decomposition\\_incremental_pca.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_incremental_pca.py',
   'DATA'),
  ('sklearn\\decomposition\\_kernel_pca.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_kernel_pca.py',
   'DATA'),
  ('sklearn\\decomposition\\_lda.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_lda.py',
   'DATA'),
  ('sklearn\\decomposition\\_nmf.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_nmf.py',
   'DATA'),
  ('sklearn\\decomposition\\_online_lda_fast.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_online_lda_fast.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\decomposition\\_online_lda_fast.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_online_lda_fast.pyx',
   'DATA'),
  ('sklearn\\decomposition\\_pca.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_pca.py',
   'DATA'),
  ('sklearn\\decomposition\\_sparse_pca.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_sparse_pca.py',
   'DATA'),
  ('sklearn\\decomposition\\_truncated_svd.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_truncated_svd.py',
   'DATA'),
  ('sklearn\\decomposition\\meson.build',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\meson.build',
   'DATA'),
  ('sklearn\\decomposition\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\decomposition\\tests\\test_dict_learning.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\tests\\test_dict_learning.py',
   'DATA'),
  ('sklearn\\decomposition\\tests\\test_factor_analysis.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\tests\\test_factor_analysis.py',
   'DATA'),
  ('sklearn\\decomposition\\tests\\test_fastica.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\tests\\test_fastica.py',
   'DATA'),
  ('sklearn\\decomposition\\tests\\test_incremental_pca.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\tests\\test_incremental_pca.py',
   'DATA'),
  ('sklearn\\decomposition\\tests\\test_kernel_pca.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\tests\\test_kernel_pca.py',
   'DATA'),
  ('sklearn\\decomposition\\tests\\test_nmf.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\tests\\test_nmf.py',
   'DATA'),
  ('sklearn\\decomposition\\tests\\test_online_lda.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\tests\\test_online_lda.py',
   'DATA'),
  ('sklearn\\decomposition\\tests\\test_pca.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\tests\\test_pca.py',
   'DATA'),
  ('sklearn\\decomposition\\tests\\test_sparse_pca.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\tests\\test_sparse_pca.py',
   'DATA'),
  ('sklearn\\decomposition\\tests\\test_truncated_svd.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\tests\\test_truncated_svd.py',
   'DATA'),
  ('sklearn\\discriminant_analysis.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\discriminant_analysis.py',
   'DATA'),
  ('sklearn\\dummy.py',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\sklearn\\dummy.py',
   'DATA'),
  ('sklearn\\ensemble\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\__init__.py',
   'DATA'),
  ('sklearn\\ensemble\\_bagging.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_bagging.py',
   'DATA'),
  ('sklearn\\ensemble\\_base.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_base.py',
   'DATA'),
  ('sklearn\\ensemble\\_forest.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_forest.py',
   'DATA'),
  ('sklearn\\ensemble\\_gb.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_gb.py',
   'DATA'),
  ('sklearn\\ensemble\\_gradient_boosting.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_gradient_boosting.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\ensemble\\_gradient_boosting.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_gradient_boosting.pyx',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\__init__.py',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_binning.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_binning.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_binning.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_binning.pyx',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_bitset.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_bitset.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_bitset.pxd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_bitset.pxd',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_bitset.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_bitset.pyx',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_gradient_boosting.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_gradient_boosting.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_gradient_boosting.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_gradient_boosting.pyx',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_predictor.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_predictor.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_predictor.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_predictor.pyx',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\binning.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\binning.py',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\common.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\common.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\common.pxd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\common.pxd',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\common.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\common.pyx',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\gradient_boosting.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\gradient_boosting.py',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\grower.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\grower.py',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\histogram.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\histogram.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\histogram.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\histogram.pyx',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\meson.build',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\meson.build',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\predictor.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\predictor.py',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\splitting.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\splitting.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\splitting.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\splitting.pyx',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_binning.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_binning.py',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_bitset.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_bitset.py',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_compare_lightgbm.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_compare_lightgbm.py',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_gradient_boosting.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_gradient_boosting.py',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_grower.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_grower.py',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_histogram.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_histogram.py',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_monotonic_constraints.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_monotonic_constraints.py',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_predictor.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_predictor.py',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_splitting.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_splitting.py',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_warm_start.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_warm_start.py',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\utils.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\utils.py',
   'DATA'),
  ('sklearn\\ensemble\\_iforest.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_iforest.py',
   'DATA'),
  ('sklearn\\ensemble\\_stacking.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_stacking.py',
   'DATA'),
  ('sklearn\\ensemble\\_voting.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_voting.py',
   'DATA'),
  ('sklearn\\ensemble\\_weight_boosting.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_weight_boosting.py',
   'DATA'),
  ('sklearn\\ensemble\\meson.build',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\meson.build',
   'DATA'),
  ('sklearn\\ensemble\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\ensemble\\tests\\test_bagging.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\tests\\test_bagging.py',
   'DATA'),
  ('sklearn\\ensemble\\tests\\test_base.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\tests\\test_base.py',
   'DATA'),
  ('sklearn\\ensemble\\tests\\test_common.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\tests\\test_common.py',
   'DATA'),
  ('sklearn\\ensemble\\tests\\test_forest.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\tests\\test_forest.py',
   'DATA'),
  ('sklearn\\ensemble\\tests\\test_gradient_boosting.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\tests\\test_gradient_boosting.py',
   'DATA'),
  ('sklearn\\ensemble\\tests\\test_iforest.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\tests\\test_iforest.py',
   'DATA'),
  ('sklearn\\ensemble\\tests\\test_stacking.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\tests\\test_stacking.py',
   'DATA'),
  ('sklearn\\ensemble\\tests\\test_voting.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\tests\\test_voting.py',
   'DATA'),
  ('sklearn\\ensemble\\tests\\test_weight_boosting.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\tests\\test_weight_boosting.py',
   'DATA'),
  ('sklearn\\exceptions.py',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\sklearn\\exceptions.py',
   'DATA'),
  ('sklearn\\experimental\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\experimental\\__init__.py',
   'DATA'),
  ('sklearn\\experimental\\enable_halving_search_cv.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\experimental\\enable_halving_search_cv.py',
   'DATA'),
  ('sklearn\\experimental\\enable_hist_gradient_boosting.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\experimental\\enable_hist_gradient_boosting.py',
   'DATA'),
  ('sklearn\\experimental\\enable_iterative_imputer.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\experimental\\enable_iterative_imputer.py',
   'DATA'),
  ('sklearn\\experimental\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\experimental\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\experimental\\tests\\test_enable_hist_gradient_boosting.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\experimental\\tests\\test_enable_hist_gradient_boosting.py',
   'DATA'),
  ('sklearn\\experimental\\tests\\test_enable_iterative_imputer.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\experimental\\tests\\test_enable_iterative_imputer.py',
   'DATA'),
  ('sklearn\\experimental\\tests\\test_enable_successive_halving.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\experimental\\tests\\test_enable_successive_halving.py',
   'DATA'),
  ('sklearn\\externals\\README',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\README',
   'DATA'),
  ('sklearn\\externals\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\__init__.py',
   'DATA'),
  ('sklearn\\externals\\_arff.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\_arff.py',
   'DATA'),
  ('sklearn\\externals\\_array_api_compat_vendor.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\_array_api_compat_vendor.py',
   'DATA'),
  ('sklearn\\externals\\_packaging\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\_packaging\\__init__.py',
   'DATA'),
  ('sklearn\\externals\\_packaging\\_structures.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\_packaging\\_structures.py',
   'DATA'),
  ('sklearn\\externals\\_packaging\\version.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\_packaging\\version.py',
   'DATA'),
  ('sklearn\\externals\\_scipy\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\_scipy\\__init__.py',
   'DATA'),
  ('sklearn\\externals\\_scipy\\sparse\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\_scipy\\sparse\\__init__.py',
   'DATA'),
  ('sklearn\\externals\\_scipy\\sparse\\csgraph\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\_scipy\\sparse\\csgraph\\__init__.py',
   'DATA'),
  ('sklearn\\externals\\_scipy\\sparse\\csgraph\\_laplacian.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\_scipy\\sparse\\csgraph\\_laplacian.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\LICENSE',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\LICENSE',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\README.md',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\README.md',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\__init__.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\_internal.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\_internal.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\common\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\common\\__init__.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\common\\_aliases.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\common\\_aliases.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\common\\_fft.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\common\\_fft.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\common\\_helpers.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\common\\_helpers.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\common\\_linalg.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\common\\_linalg.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\common\\_typing.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\common\\_typing.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\cupy\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\cupy\\__init__.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\cupy\\_aliases.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\cupy\\_aliases.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\cupy\\_info.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\cupy\\_info.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\cupy\\_typing.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\cupy\\_typing.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\cupy\\fft.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\cupy\\fft.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\cupy\\linalg.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\cupy\\linalg.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\dask\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\dask\\__init__.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\dask\\array\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\dask\\array\\__init__.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\dask\\array\\_aliases.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\dask\\array\\_aliases.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\dask\\array\\_info.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\dask\\array\\_info.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\dask\\array\\fft.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\dask\\array\\fft.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\dask\\array\\linalg.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\dask\\array\\linalg.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\numpy\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\numpy\\__init__.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\numpy\\_aliases.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\numpy\\_aliases.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\numpy\\_info.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\numpy\\_info.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\numpy\\_typing.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\numpy\\_typing.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\numpy\\fft.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\numpy\\fft.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\numpy\\linalg.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\numpy\\linalg.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\py.typed',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\py.typed',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\torch\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\torch\\__init__.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\torch\\_aliases.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\torch\\_aliases.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\torch\\_info.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\torch\\_info.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\torch\\_typing.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\torch\\_typing.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\torch\\fft.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\torch\\fft.py',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\torch\\linalg.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\torch\\linalg.py',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\LICENSE',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\LICENSE',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\README.md',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\README.md',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\__init__.py',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\_delegation.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\_delegation.py',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\_lib\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\_lib\\__init__.py',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\_lib\\_at.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\_lib\\_at.py',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\_lib\\_backends.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\_lib\\_backends.py',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\_lib\\_funcs.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\_lib\\_funcs.py',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\_lib\\_lazy.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\_lib\\_lazy.py',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\_lib\\_testing.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\_lib\\_testing.py',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\_lib\\_utils\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\_lib\\_utils\\__init__.py',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\_lib\\_utils\\_compat.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\_lib\\_utils\\_compat.py',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\_lib\\_utils\\_compat.pyi',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\_lib\\_utils\\_compat.pyi',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\_lib\\_utils\\_helpers.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\_lib\\_utils\\_helpers.py',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\_lib\\_utils\\_typing.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\_lib\\_utils\\_typing.py',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\_lib\\_utils\\_typing.pyi',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\_lib\\_utils\\_typing.pyi',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\py.typed',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\py.typed',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\testing.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\testing.py',
   'DATA'),
  ('sklearn\\externals\\conftest.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\externals\\conftest.py',
   'DATA'),
  ('sklearn\\feature_extraction\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_extraction\\__init__.py',
   'DATA'),
  ('sklearn\\feature_extraction\\_dict_vectorizer.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_extraction\\_dict_vectorizer.py',
   'DATA'),
  ('sklearn\\feature_extraction\\_hash.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_extraction\\_hash.py',
   'DATA'),
  ('sklearn\\feature_extraction\\_hashing_fast.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_extraction\\_hashing_fast.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\feature_extraction\\_hashing_fast.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_extraction\\_hashing_fast.pyx',
   'DATA'),
  ('sklearn\\feature_extraction\\_stop_words.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_extraction\\_stop_words.py',
   'DATA'),
  ('sklearn\\feature_extraction\\image.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_extraction\\image.py',
   'DATA'),
  ('sklearn\\feature_extraction\\meson.build',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_extraction\\meson.build',
   'DATA'),
  ('sklearn\\feature_extraction\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_extraction\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\feature_extraction\\tests\\test_dict_vectorizer.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_extraction\\tests\\test_dict_vectorizer.py',
   'DATA'),
  ('sklearn\\feature_extraction\\tests\\test_feature_hasher.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_extraction\\tests\\test_feature_hasher.py',
   'DATA'),
  ('sklearn\\feature_extraction\\tests\\test_image.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_extraction\\tests\\test_image.py',
   'DATA'),
  ('sklearn\\feature_extraction\\tests\\test_text.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_extraction\\tests\\test_text.py',
   'DATA'),
  ('sklearn\\feature_extraction\\text.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_extraction\\text.py',
   'DATA'),
  ('sklearn\\feature_selection\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_selection\\__init__.py',
   'DATA'),
  ('sklearn\\feature_selection\\_base.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_selection\\_base.py',
   'DATA'),
  ('sklearn\\feature_selection\\_from_model.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_selection\\_from_model.py',
   'DATA'),
  ('sklearn\\feature_selection\\_mutual_info.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_selection\\_mutual_info.py',
   'DATA'),
  ('sklearn\\feature_selection\\_rfe.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_selection\\_rfe.py',
   'DATA'),
  ('sklearn\\feature_selection\\_sequential.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_selection\\_sequential.py',
   'DATA'),
  ('sklearn\\feature_selection\\_univariate_selection.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_selection\\_univariate_selection.py',
   'DATA'),
  ('sklearn\\feature_selection\\_variance_threshold.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_selection\\_variance_threshold.py',
   'DATA'),
  ('sklearn\\feature_selection\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_selection\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\feature_selection\\tests\\test_base.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_selection\\tests\\test_base.py',
   'DATA'),
  ('sklearn\\feature_selection\\tests\\test_chi2.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_selection\\tests\\test_chi2.py',
   'DATA'),
  ('sklearn\\feature_selection\\tests\\test_feature_select.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_selection\\tests\\test_feature_select.py',
   'DATA'),
  ('sklearn\\feature_selection\\tests\\test_from_model.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_selection\\tests\\test_from_model.py',
   'DATA'),
  ('sklearn\\feature_selection\\tests\\test_mutual_info.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_selection\\tests\\test_mutual_info.py',
   'DATA'),
  ('sklearn\\feature_selection\\tests\\test_rfe.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_selection\\tests\\test_rfe.py',
   'DATA'),
  ('sklearn\\feature_selection\\tests\\test_sequential.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_selection\\tests\\test_sequential.py',
   'DATA'),
  ('sklearn\\feature_selection\\tests\\test_variance_threshold.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\feature_selection\\tests\\test_variance_threshold.py',
   'DATA'),
  ('sklearn\\frozen\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\frozen\\__init__.py',
   'DATA'),
  ('sklearn\\frozen\\_frozen.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\frozen\\_frozen.py',
   'DATA'),
  ('sklearn\\frozen\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\frozen\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\frozen\\tests\\test_frozen.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\frozen\\tests\\test_frozen.py',
   'DATA'),
  ('sklearn\\gaussian_process\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\gaussian_process\\__init__.py',
   'DATA'),
  ('sklearn\\gaussian_process\\_gpc.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\gaussian_process\\_gpc.py',
   'DATA'),
  ('sklearn\\gaussian_process\\_gpr.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\gaussian_process\\_gpr.py',
   'DATA'),
  ('sklearn\\gaussian_process\\kernels.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\gaussian_process\\kernels.py',
   'DATA'),
  ('sklearn\\gaussian_process\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\gaussian_process\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\gaussian_process\\tests\\_mini_sequence_kernel.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\gaussian_process\\tests\\_mini_sequence_kernel.py',
   'DATA'),
  ('sklearn\\gaussian_process\\tests\\test_gpc.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\gaussian_process\\tests\\test_gpc.py',
   'DATA'),
  ('sklearn\\gaussian_process\\tests\\test_gpr.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\gaussian_process\\tests\\test_gpr.py',
   'DATA'),
  ('sklearn\\gaussian_process\\tests\\test_kernels.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\gaussian_process\\tests\\test_kernels.py',
   'DATA'),
  ('sklearn\\impute\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\impute\\__init__.py',
   'DATA'),
  ('sklearn\\impute\\_base.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\impute\\_base.py',
   'DATA'),
  ('sklearn\\impute\\_iterative.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\impute\\_iterative.py',
   'DATA'),
  ('sklearn\\impute\\_knn.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\impute\\_knn.py',
   'DATA'),
  ('sklearn\\impute\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\impute\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\impute\\tests\\test_base.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\impute\\tests\\test_base.py',
   'DATA'),
  ('sklearn\\impute\\tests\\test_common.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\impute\\tests\\test_common.py',
   'DATA'),
  ('sklearn\\impute\\tests\\test_impute.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\impute\\tests\\test_impute.py',
   'DATA'),
  ('sklearn\\impute\\tests\\test_knn.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\impute\\tests\\test_knn.py',
   'DATA'),
  ('sklearn\\inspection\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\inspection\\__init__.py',
   'DATA'),
  ('sklearn\\inspection\\_partial_dependence.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\inspection\\_partial_dependence.py',
   'DATA'),
  ('sklearn\\inspection\\_pd_utils.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\inspection\\_pd_utils.py',
   'DATA'),
  ('sklearn\\inspection\\_permutation_importance.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\inspection\\_permutation_importance.py',
   'DATA'),
  ('sklearn\\inspection\\_plot\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\inspection\\_plot\\__init__.py',
   'DATA'),
  ('sklearn\\inspection\\_plot\\decision_boundary.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\inspection\\_plot\\decision_boundary.py',
   'DATA'),
  ('sklearn\\inspection\\_plot\\partial_dependence.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\inspection\\_plot\\partial_dependence.py',
   'DATA'),
  ('sklearn\\inspection\\_plot\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\inspection\\_plot\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\inspection\\_plot\\tests\\test_boundary_decision_display.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\inspection\\_plot\\tests\\test_boundary_decision_display.py',
   'DATA'),
  ('sklearn\\inspection\\_plot\\tests\\test_plot_partial_dependence.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\inspection\\_plot\\tests\\test_plot_partial_dependence.py',
   'DATA'),
  ('sklearn\\inspection\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\inspection\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\inspection\\tests\\test_partial_dependence.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\inspection\\tests\\test_partial_dependence.py',
   'DATA'),
  ('sklearn\\inspection\\tests\\test_pd_utils.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\inspection\\tests\\test_pd_utils.py',
   'DATA'),
  ('sklearn\\inspection\\tests\\test_permutation_importance.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\inspection\\tests\\test_permutation_importance.py',
   'DATA'),
  ('sklearn\\isotonic.py',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\sklearn\\isotonic.py',
   'DATA'),
  ('sklearn\\kernel_approximation.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\kernel_approximation.py',
   'DATA'),
  ('sklearn\\kernel_ridge.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\kernel_ridge.py',
   'DATA'),
  ('sklearn\\linear_model\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\__init__.py',
   'DATA'),
  ('sklearn\\linear_model\\_base.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_base.py',
   'DATA'),
  ('sklearn\\linear_model\\_bayes.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_bayes.py',
   'DATA'),
  ('sklearn\\linear_model\\_cd_fast.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_cd_fast.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\linear_model\\_cd_fast.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_cd_fast.pyx',
   'DATA'),
  ('sklearn\\linear_model\\_coordinate_descent.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_coordinate_descent.py',
   'DATA'),
  ('sklearn\\linear_model\\_glm\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_glm\\__init__.py',
   'DATA'),
  ('sklearn\\linear_model\\_glm\\_newton_solver.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_glm\\_newton_solver.py',
   'DATA'),
  ('sklearn\\linear_model\\_glm\\glm.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_glm\\glm.py',
   'DATA'),
  ('sklearn\\linear_model\\_glm\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_glm\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\linear_model\\_glm\\tests\\test_glm.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_glm\\tests\\test_glm.py',
   'DATA'),
  ('sklearn\\linear_model\\_huber.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_huber.py',
   'DATA'),
  ('sklearn\\linear_model\\_least_angle.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_least_angle.py',
   'DATA'),
  ('sklearn\\linear_model\\_linear_loss.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_linear_loss.py',
   'DATA'),
  ('sklearn\\linear_model\\_logistic.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_logistic.py',
   'DATA'),
  ('sklearn\\linear_model\\_omp.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_omp.py',
   'DATA'),
  ('sklearn\\linear_model\\_passive_aggressive.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_passive_aggressive.py',
   'DATA'),
  ('sklearn\\linear_model\\_perceptron.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_perceptron.py',
   'DATA'),
  ('sklearn\\linear_model\\_quantile.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_quantile.py',
   'DATA'),
  ('sklearn\\linear_model\\_ransac.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_ransac.py',
   'DATA'),
  ('sklearn\\linear_model\\_ridge.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_ridge.py',
   'DATA'),
  ('sklearn\\linear_model\\_sag.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_sag.py',
   'DATA'),
  ('sklearn\\linear_model\\_sag_fast.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_sag_fast.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\linear_model\\_sag_fast.pyx.tp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_sag_fast.pyx.tp',
   'DATA'),
  ('sklearn\\linear_model\\_sgd_fast.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_sgd_fast.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\linear_model\\_sgd_fast.pyx.tp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_sgd_fast.pyx.tp',
   'DATA'),
  ('sklearn\\linear_model\\_stochastic_gradient.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_stochastic_gradient.py',
   'DATA'),
  ('sklearn\\linear_model\\_theil_sen.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_theil_sen.py',
   'DATA'),
  ('sklearn\\linear_model\\meson.build',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\meson.build',
   'DATA'),
  ('sklearn\\linear_model\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\linear_model\\tests\\test_base.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_base.py',
   'DATA'),
  ('sklearn\\linear_model\\tests\\test_bayes.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_bayes.py',
   'DATA'),
  ('sklearn\\linear_model\\tests\\test_common.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_common.py',
   'DATA'),
  ('sklearn\\linear_model\\tests\\test_coordinate_descent.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_coordinate_descent.py',
   'DATA'),
  ('sklearn\\linear_model\\tests\\test_huber.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_huber.py',
   'DATA'),
  ('sklearn\\linear_model\\tests\\test_least_angle.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_least_angle.py',
   'DATA'),
  ('sklearn\\linear_model\\tests\\test_linear_loss.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_linear_loss.py',
   'DATA'),
  ('sklearn\\linear_model\\tests\\test_logistic.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_logistic.py',
   'DATA'),
  ('sklearn\\linear_model\\tests\\test_omp.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_omp.py',
   'DATA'),
  ('sklearn\\linear_model\\tests\\test_passive_aggressive.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_passive_aggressive.py',
   'DATA'),
  ('sklearn\\linear_model\\tests\\test_perceptron.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_perceptron.py',
   'DATA'),
  ('sklearn\\linear_model\\tests\\test_quantile.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_quantile.py',
   'DATA'),
  ('sklearn\\linear_model\\tests\\test_ransac.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_ransac.py',
   'DATA'),
  ('sklearn\\linear_model\\tests\\test_ridge.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_ridge.py',
   'DATA'),
  ('sklearn\\linear_model\\tests\\test_sag.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_sag.py',
   'DATA'),
  ('sklearn\\linear_model\\tests\\test_sgd.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_sgd.py',
   'DATA'),
  ('sklearn\\linear_model\\tests\\test_sparse_coordinate_descent.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_sparse_coordinate_descent.py',
   'DATA'),
  ('sklearn\\linear_model\\tests\\test_theil_sen.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_theil_sen.py',
   'DATA'),
  ('sklearn\\manifold\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\manifold\\__init__.py',
   'DATA'),
  ('sklearn\\manifold\\_barnes_hut_tsne.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\manifold\\_barnes_hut_tsne.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\manifold\\_barnes_hut_tsne.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\manifold\\_barnes_hut_tsne.pyx',
   'DATA'),
  ('sklearn\\manifold\\_isomap.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\manifold\\_isomap.py',
   'DATA'),
  ('sklearn\\manifold\\_locally_linear.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\manifold\\_locally_linear.py',
   'DATA'),
  ('sklearn\\manifold\\_mds.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\manifold\\_mds.py',
   'DATA'),
  ('sklearn\\manifold\\_spectral_embedding.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\manifold\\_spectral_embedding.py',
   'DATA'),
  ('sklearn\\manifold\\_t_sne.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\manifold\\_t_sne.py',
   'DATA'),
  ('sklearn\\manifold\\_utils.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\manifold\\_utils.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\manifold\\_utils.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\manifold\\_utils.pyx',
   'DATA'),
  ('sklearn\\manifold\\meson.build',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\manifold\\meson.build',
   'DATA'),
  ('sklearn\\manifold\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\manifold\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\manifold\\tests\\test_isomap.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\manifold\\tests\\test_isomap.py',
   'DATA'),
  ('sklearn\\manifold\\tests\\test_locally_linear.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\manifold\\tests\\test_locally_linear.py',
   'DATA'),
  ('sklearn\\manifold\\tests\\test_mds.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\manifold\\tests\\test_mds.py',
   'DATA'),
  ('sklearn\\manifold\\tests\\test_spectral_embedding.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\manifold\\tests\\test_spectral_embedding.py',
   'DATA'),
  ('sklearn\\manifold\\tests\\test_t_sne.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\manifold\\tests\\test_t_sne.py',
   'DATA'),
  ('sklearn\\meson.build',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\sklearn\\meson.build',
   'DATA'),
  ('sklearn\\metrics\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\__init__.py',
   'DATA'),
  ('sklearn\\metrics\\_base.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_base.py',
   'DATA'),
  ('sklearn\\metrics\\_classification.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_classification.py',
   'DATA'),
  ('sklearn\\metrics\\_dist_metrics.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_dist_metrics.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\metrics\\_dist_metrics.pxd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_dist_metrics.pxd',
   'DATA'),
  ('sklearn\\metrics\\_dist_metrics.pxd.tp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_dist_metrics.pxd.tp',
   'DATA'),
  ('sklearn\\metrics\\_dist_metrics.pyx.tp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_dist_metrics.pyx.tp',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\__init__.py',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.pxd.tp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.pxd.tp',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.pyx.tp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.pyx.tp',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin_classmode.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin_classmode.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin_classmode.pyx.tp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin_classmode.pyx.tp',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_base.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_base.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_base.pxd.tp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_base.pxd.tp',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_base.pyx.tp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_base.pyx.tp',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_classmode.pxd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_classmode.pxd',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.pxd.tp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.pxd.tp',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.pyx.tp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.pyx.tp',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_dispatcher.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_dispatcher.py',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.pxd.tp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.pxd.tp',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.pyx.tp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.pyx.tp',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.pxd.tp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.pxd.tp',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.pyx.tp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.pyx.tp',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors_classmode.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors_classmode.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors_classmode.pyx.tp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors_classmode.pyx.tp',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\meson.build',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\meson.build',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_fast.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_fast.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_fast.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_fast.pyx',
   'DATA'),
  ('sklearn\\metrics\\_plot\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_plot\\__init__.py',
   'DATA'),
  ('sklearn\\metrics\\_plot\\confusion_matrix.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_plot\\confusion_matrix.py',
   'DATA'),
  ('sklearn\\metrics\\_plot\\det_curve.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_plot\\det_curve.py',
   'DATA'),
  ('sklearn\\metrics\\_plot\\precision_recall_curve.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_plot\\precision_recall_curve.py',
   'DATA'),
  ('sklearn\\metrics\\_plot\\regression.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_plot\\regression.py',
   'DATA'),
  ('sklearn\\metrics\\_plot\\roc_curve.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_plot\\roc_curve.py',
   'DATA'),
  ('sklearn\\metrics\\_plot\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_plot\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\metrics\\_plot\\tests\\test_common_curve_display.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_plot\\tests\\test_common_curve_display.py',
   'DATA'),
  ('sklearn\\metrics\\_plot\\tests\\test_confusion_matrix_display.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_plot\\tests\\test_confusion_matrix_display.py',
   'DATA'),
  ('sklearn\\metrics\\_plot\\tests\\test_det_curve_display.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_plot\\tests\\test_det_curve_display.py',
   'DATA'),
  ('sklearn\\metrics\\_plot\\tests\\test_precision_recall_display.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_plot\\tests\\test_precision_recall_display.py',
   'DATA'),
  ('sklearn\\metrics\\_plot\\tests\\test_predict_error_display.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_plot\\tests\\test_predict_error_display.py',
   'DATA'),
  ('sklearn\\metrics\\_plot\\tests\\test_roc_curve_display.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_plot\\tests\\test_roc_curve_display.py',
   'DATA'),
  ('sklearn\\metrics\\_ranking.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_ranking.py',
   'DATA'),
  ('sklearn\\metrics\\_regression.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_regression.py',
   'DATA'),
  ('sklearn\\metrics\\_scorer.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_scorer.py',
   'DATA'),
  ('sklearn\\metrics\\cluster\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\cluster\\__init__.py',
   'DATA'),
  ('sklearn\\metrics\\cluster\\_bicluster.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\cluster\\_bicluster.py',
   'DATA'),
  ('sklearn\\metrics\\cluster\\_expected_mutual_info_fast.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\cluster\\_expected_mutual_info_fast.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\metrics\\cluster\\_expected_mutual_info_fast.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\cluster\\_expected_mutual_info_fast.pyx',
   'DATA'),
  ('sklearn\\metrics\\cluster\\_supervised.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\cluster\\_supervised.py',
   'DATA'),
  ('sklearn\\metrics\\cluster\\_unsupervised.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\cluster\\_unsupervised.py',
   'DATA'),
  ('sklearn\\metrics\\cluster\\meson.build',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\cluster\\meson.build',
   'DATA'),
  ('sklearn\\metrics\\cluster\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\cluster\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\metrics\\cluster\\tests\\test_bicluster.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\cluster\\tests\\test_bicluster.py',
   'DATA'),
  ('sklearn\\metrics\\cluster\\tests\\test_common.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\cluster\\tests\\test_common.py',
   'DATA'),
  ('sklearn\\metrics\\cluster\\tests\\test_supervised.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\cluster\\tests\\test_supervised.py',
   'DATA'),
  ('sklearn\\metrics\\cluster\\tests\\test_unsupervised.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\cluster\\tests\\test_unsupervised.py',
   'DATA'),
  ('sklearn\\metrics\\meson.build',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\meson.build',
   'DATA'),
  ('sklearn\\metrics\\pairwise.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\pairwise.py',
   'DATA'),
  ('sklearn\\metrics\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\metrics\\tests\\test_classification.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\tests\\test_classification.py',
   'DATA'),
  ('sklearn\\metrics\\tests\\test_common.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\tests\\test_common.py',
   'DATA'),
  ('sklearn\\metrics\\tests\\test_dist_metrics.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\tests\\test_dist_metrics.py',
   'DATA'),
  ('sklearn\\metrics\\tests\\test_pairwise.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\tests\\test_pairwise.py',
   'DATA'),
  ('sklearn\\metrics\\tests\\test_pairwise_distances_reduction.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\tests\\test_pairwise_distances_reduction.py',
   'DATA'),
  ('sklearn\\metrics\\tests\\test_ranking.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\tests\\test_ranking.py',
   'DATA'),
  ('sklearn\\metrics\\tests\\test_regression.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\tests\\test_regression.py',
   'DATA'),
  ('sklearn\\metrics\\tests\\test_score_objects.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\metrics\\tests\\test_score_objects.py',
   'DATA'),
  ('sklearn\\mixture\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\mixture\\__init__.py',
   'DATA'),
  ('sklearn\\mixture\\_base.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\mixture\\_base.py',
   'DATA'),
  ('sklearn\\mixture\\_bayesian_mixture.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\mixture\\_bayesian_mixture.py',
   'DATA'),
  ('sklearn\\mixture\\_gaussian_mixture.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\mixture\\_gaussian_mixture.py',
   'DATA'),
  ('sklearn\\mixture\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\mixture\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\mixture\\tests\\test_bayesian_mixture.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\mixture\\tests\\test_bayesian_mixture.py',
   'DATA'),
  ('sklearn\\mixture\\tests\\test_gaussian_mixture.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\mixture\\tests\\test_gaussian_mixture.py',
   'DATA'),
  ('sklearn\\mixture\\tests\\test_mixture.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\mixture\\tests\\test_mixture.py',
   'DATA'),
  ('sklearn\\model_selection\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\model_selection\\__init__.py',
   'DATA'),
  ('sklearn\\model_selection\\_classification_threshold.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\model_selection\\_classification_threshold.py',
   'DATA'),
  ('sklearn\\model_selection\\_plot.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\model_selection\\_plot.py',
   'DATA'),
  ('sklearn\\model_selection\\_search.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\model_selection\\_search.py',
   'DATA'),
  ('sklearn\\model_selection\\_search_successive_halving.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\model_selection\\_search_successive_halving.py',
   'DATA'),
  ('sklearn\\model_selection\\_split.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\model_selection\\_split.py',
   'DATA'),
  ('sklearn\\model_selection\\_validation.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\model_selection\\_validation.py',
   'DATA'),
  ('sklearn\\model_selection\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\model_selection\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\model_selection\\tests\\common.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\model_selection\\tests\\common.py',
   'DATA'),
  ('sklearn\\model_selection\\tests\\test_classification_threshold.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\model_selection\\tests\\test_classification_threshold.py',
   'DATA'),
  ('sklearn\\model_selection\\tests\\test_plot.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\model_selection\\tests\\test_plot.py',
   'DATA'),
  ('sklearn\\model_selection\\tests\\test_search.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\model_selection\\tests\\test_search.py',
   'DATA'),
  ('sklearn\\model_selection\\tests\\test_split.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\model_selection\\tests\\test_split.py',
   'DATA'),
  ('sklearn\\model_selection\\tests\\test_successive_halving.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\model_selection\\tests\\test_successive_halving.py',
   'DATA'),
  ('sklearn\\model_selection\\tests\\test_validation.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\model_selection\\tests\\test_validation.py',
   'DATA'),
  ('sklearn\\multiclass.py',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\sklearn\\multiclass.py',
   'DATA'),
  ('sklearn\\multioutput.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\multioutput.py',
   'DATA'),
  ('sklearn\\naive_bayes.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\naive_bayes.py',
   'DATA'),
  ('sklearn\\neighbors\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\__init__.py',
   'DATA'),
  ('sklearn\\neighbors\\_ball_tree.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_ball_tree.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\neighbors\\_ball_tree.pyx.tp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_ball_tree.pyx.tp',
   'DATA'),
  ('sklearn\\neighbors\\_base.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_base.py',
   'DATA'),
  ('sklearn\\neighbors\\_binary_tree.pxi.tp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_binary_tree.pxi.tp',
   'DATA'),
  ('sklearn\\neighbors\\_classification.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_classification.py',
   'DATA'),
  ('sklearn\\neighbors\\_graph.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_graph.py',
   'DATA'),
  ('sklearn\\neighbors\\_kd_tree.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_kd_tree.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\neighbors\\_kd_tree.pyx.tp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_kd_tree.pyx.tp',
   'DATA'),
  ('sklearn\\neighbors\\_kde.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_kde.py',
   'DATA'),
  ('sklearn\\neighbors\\_lof.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_lof.py',
   'DATA'),
  ('sklearn\\neighbors\\_nca.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_nca.py',
   'DATA'),
  ('sklearn\\neighbors\\_nearest_centroid.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_nearest_centroid.py',
   'DATA'),
  ('sklearn\\neighbors\\_partition_nodes.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_partition_nodes.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\neighbors\\_partition_nodes.pxd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_partition_nodes.pxd',
   'DATA'),
  ('sklearn\\neighbors\\_partition_nodes.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_partition_nodes.pyx',
   'DATA'),
  ('sklearn\\neighbors\\_quad_tree.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_quad_tree.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\neighbors\\_quad_tree.pxd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_quad_tree.pxd',
   'DATA'),
  ('sklearn\\neighbors\\_quad_tree.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_quad_tree.pyx',
   'DATA'),
  ('sklearn\\neighbors\\_regression.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_regression.py',
   'DATA'),
  ('sklearn\\neighbors\\_unsupervised.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_unsupervised.py',
   'DATA'),
  ('sklearn\\neighbors\\meson.build',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\meson.build',
   'DATA'),
  ('sklearn\\neighbors\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\neighbors\\tests\\test_ball_tree.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\tests\\test_ball_tree.py',
   'DATA'),
  ('sklearn\\neighbors\\tests\\test_graph.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\tests\\test_graph.py',
   'DATA'),
  ('sklearn\\neighbors\\tests\\test_kd_tree.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\tests\\test_kd_tree.py',
   'DATA'),
  ('sklearn\\neighbors\\tests\\test_kde.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\tests\\test_kde.py',
   'DATA'),
  ('sklearn\\neighbors\\tests\\test_lof.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\tests\\test_lof.py',
   'DATA'),
  ('sklearn\\neighbors\\tests\\test_nca.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\tests\\test_nca.py',
   'DATA'),
  ('sklearn\\neighbors\\tests\\test_nearest_centroid.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\tests\\test_nearest_centroid.py',
   'DATA'),
  ('sklearn\\neighbors\\tests\\test_neighbors.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\tests\\test_neighbors.py',
   'DATA'),
  ('sklearn\\neighbors\\tests\\test_neighbors_pipeline.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\tests\\test_neighbors_pipeline.py',
   'DATA'),
  ('sklearn\\neighbors\\tests\\test_neighbors_tree.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\tests\\test_neighbors_tree.py',
   'DATA'),
  ('sklearn\\neighbors\\tests\\test_quad_tree.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\tests\\test_quad_tree.py',
   'DATA'),
  ('sklearn\\neural_network\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neural_network\\__init__.py',
   'DATA'),
  ('sklearn\\neural_network\\_base.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neural_network\\_base.py',
   'DATA'),
  ('sklearn\\neural_network\\_multilayer_perceptron.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neural_network\\_multilayer_perceptron.py',
   'DATA'),
  ('sklearn\\neural_network\\_rbm.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neural_network\\_rbm.py',
   'DATA'),
  ('sklearn\\neural_network\\_stochastic_optimizers.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neural_network\\_stochastic_optimizers.py',
   'DATA'),
  ('sklearn\\neural_network\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neural_network\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\neural_network\\tests\\test_base.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neural_network\\tests\\test_base.py',
   'DATA'),
  ('sklearn\\neural_network\\tests\\test_mlp.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neural_network\\tests\\test_mlp.py',
   'DATA'),
  ('sklearn\\neural_network\\tests\\test_rbm.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neural_network\\tests\\test_rbm.py',
   'DATA'),
  ('sklearn\\neural_network\\tests\\test_stochastic_optimizers.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\neural_network\\tests\\test_stochastic_optimizers.py',
   'DATA'),
  ('sklearn\\pipeline.py',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\sklearn\\pipeline.py',
   'DATA'),
  ('sklearn\\preprocessing\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\__init__.py',
   'DATA'),
  ('sklearn\\preprocessing\\_csr_polynomial_expansion.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_csr_polynomial_expansion.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\preprocessing\\_csr_polynomial_expansion.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_csr_polynomial_expansion.pyx',
   'DATA'),
  ('sklearn\\preprocessing\\_data.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_data.py',
   'DATA'),
  ('sklearn\\preprocessing\\_discretization.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_discretization.py',
   'DATA'),
  ('sklearn\\preprocessing\\_encoders.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_encoders.py',
   'DATA'),
  ('sklearn\\preprocessing\\_function_transformer.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_function_transformer.py',
   'DATA'),
  ('sklearn\\preprocessing\\_label.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_label.py',
   'DATA'),
  ('sklearn\\preprocessing\\_polynomial.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_polynomial.py',
   'DATA'),
  ('sklearn\\preprocessing\\_target_encoder.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_target_encoder.py',
   'DATA'),
  ('sklearn\\preprocessing\\_target_encoder_fast.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_target_encoder_fast.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\preprocessing\\_target_encoder_fast.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_target_encoder_fast.pyx',
   'DATA'),
  ('sklearn\\preprocessing\\meson.build',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\meson.build',
   'DATA'),
  ('sklearn\\preprocessing\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\preprocessing\\tests\\test_common.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\tests\\test_common.py',
   'DATA'),
  ('sklearn\\preprocessing\\tests\\test_data.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\tests\\test_data.py',
   'DATA'),
  ('sklearn\\preprocessing\\tests\\test_discretization.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\tests\\test_discretization.py',
   'DATA'),
  ('sklearn\\preprocessing\\tests\\test_encoders.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\tests\\test_encoders.py',
   'DATA'),
  ('sklearn\\preprocessing\\tests\\test_function_transformer.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\tests\\test_function_transformer.py',
   'DATA'),
  ('sklearn\\preprocessing\\tests\\test_label.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\tests\\test_label.py',
   'DATA'),
  ('sklearn\\preprocessing\\tests\\test_polynomial.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\tests\\test_polynomial.py',
   'DATA'),
  ('sklearn\\preprocessing\\tests\\test_target_encoder.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\tests\\test_target_encoder.py',
   'DATA'),
  ('sklearn\\random_projection.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\random_projection.py',
   'DATA'),
  ('sklearn\\semi_supervised\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\semi_supervised\\__init__.py',
   'DATA'),
  ('sklearn\\semi_supervised\\_label_propagation.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\semi_supervised\\_label_propagation.py',
   'DATA'),
  ('sklearn\\semi_supervised\\_self_training.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\semi_supervised\\_self_training.py',
   'DATA'),
  ('sklearn\\semi_supervised\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\semi_supervised\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\semi_supervised\\tests\\test_label_propagation.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\semi_supervised\\tests\\test_label_propagation.py',
   'DATA'),
  ('sklearn\\semi_supervised\\tests\\test_self_training.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\semi_supervised\\tests\\test_self_training.py',
   'DATA'),
  ('sklearn\\svm\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\__init__.py',
   'DATA'),
  ('sklearn\\svm\\_base.py',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\_base.py',
   'DATA'),
  ('sklearn\\svm\\_bounds.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\_bounds.py',
   'DATA'),
  ('sklearn\\svm\\_classes.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\_classes.py',
   'DATA'),
  ('sklearn\\svm\\_liblinear.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\_liblinear.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\svm\\_liblinear.pxi',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\_liblinear.pxi',
   'DATA'),
  ('sklearn\\svm\\_liblinear.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\_liblinear.pyx',
   'DATA'),
  ('sklearn\\svm\\_libsvm.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\_libsvm.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\svm\\_libsvm.pxi',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\_libsvm.pxi',
   'DATA'),
  ('sklearn\\svm\\_libsvm.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\_libsvm.pyx',
   'DATA'),
  ('sklearn\\svm\\_libsvm_sparse.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\_libsvm_sparse.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\svm\\_libsvm_sparse.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\_libsvm_sparse.pyx',
   'DATA'),
  ('sklearn\\svm\\_newrand.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\_newrand.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\svm\\_newrand.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\_newrand.pyx',
   'DATA'),
  ('sklearn\\svm\\meson.build',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\meson.build',
   'DATA'),
  ('sklearn\\svm\\src\\liblinear\\COPYRIGHT',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\liblinear\\COPYRIGHT',
   'DATA'),
  ('sklearn\\svm\\src\\liblinear\\_cython_blas_helpers.h',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\liblinear\\_cython_blas_helpers.h',
   'DATA'),
  ('sklearn\\svm\\src\\liblinear\\liblinear_helper.c',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\liblinear\\liblinear_helper.c',
   'DATA'),
  ('sklearn\\svm\\src\\liblinear\\linear.cpp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\liblinear\\linear.cpp',
   'DATA'),
  ('sklearn\\svm\\src\\liblinear\\linear.h',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\liblinear\\linear.h',
   'DATA'),
  ('sklearn\\svm\\src\\liblinear\\tron.cpp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\liblinear\\tron.cpp',
   'DATA'),
  ('sklearn\\svm\\src\\liblinear\\tron.h',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\liblinear\\tron.h',
   'DATA'),
  ('sklearn\\svm\\src\\libsvm\\LIBSVM_CHANGES',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\libsvm\\LIBSVM_CHANGES',
   'DATA'),
  ('sklearn\\svm\\src\\libsvm\\_svm_cython_blas_helpers.h',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\libsvm\\_svm_cython_blas_helpers.h',
   'DATA'),
  ('sklearn\\svm\\src\\libsvm\\libsvm_helper.c',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\libsvm\\libsvm_helper.c',
   'DATA'),
  ('sklearn\\svm\\src\\libsvm\\libsvm_sparse_helper.c',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\libsvm\\libsvm_sparse_helper.c',
   'DATA'),
  ('sklearn\\svm\\src\\libsvm\\libsvm_template.cpp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\libsvm\\libsvm_template.cpp',
   'DATA'),
  ('sklearn\\svm\\src\\libsvm\\svm.cpp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\libsvm\\svm.cpp',
   'DATA'),
  ('sklearn\\svm\\src\\libsvm\\svm.h',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\libsvm\\svm.h',
   'DATA'),
  ('sklearn\\svm\\src\\newrand\\newrand.h',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\newrand\\newrand.h',
   'DATA'),
  ('sklearn\\svm\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\svm\\tests\\test_bounds.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\tests\\test_bounds.py',
   'DATA'),
  ('sklearn\\svm\\tests\\test_sparse.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\tests\\test_sparse.py',
   'DATA'),
  ('sklearn\\svm\\tests\\test_svm.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\svm\\tests\\test_svm.py',
   'DATA'),
  ('sklearn\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\tests\\metadata_routing_common.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\metadata_routing_common.py',
   'DATA'),
  ('sklearn\\tests\\test_base.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\test_base.py',
   'DATA'),
  ('sklearn\\tests\\test_build.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\test_build.py',
   'DATA'),
  ('sklearn\\tests\\test_calibration.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\test_calibration.py',
   'DATA'),
  ('sklearn\\tests\\test_check_build.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\test_check_build.py',
   'DATA'),
  ('sklearn\\tests\\test_common.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\test_common.py',
   'DATA'),
  ('sklearn\\tests\\test_config.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\test_config.py',
   'DATA'),
  ('sklearn\\tests\\test_discriminant_analysis.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\test_discriminant_analysis.py',
   'DATA'),
  ('sklearn\\tests\\test_docstring_parameters.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\test_docstring_parameters.py',
   'DATA'),
  ('sklearn\\tests\\test_docstring_parameters_consistency.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\test_docstring_parameters_consistency.py',
   'DATA'),
  ('sklearn\\tests\\test_docstrings.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\test_docstrings.py',
   'DATA'),
  ('sklearn\\tests\\test_dummy.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\test_dummy.py',
   'DATA'),
  ('sklearn\\tests\\test_init.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\test_init.py',
   'DATA'),
  ('sklearn\\tests\\test_isotonic.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\test_isotonic.py',
   'DATA'),
  ('sklearn\\tests\\test_kernel_approximation.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\test_kernel_approximation.py',
   'DATA'),
  ('sklearn\\tests\\test_kernel_ridge.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\test_kernel_ridge.py',
   'DATA'),
  ('sklearn\\tests\\test_metadata_routing.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\test_metadata_routing.py',
   'DATA'),
  ('sklearn\\tests\\test_metaestimators.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\test_metaestimators.py',
   'DATA'),
  ('sklearn\\tests\\test_metaestimators_metadata_routing.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\test_metaestimators_metadata_routing.py',
   'DATA'),
  ('sklearn\\tests\\test_min_dependencies_readme.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\test_min_dependencies_readme.py',
   'DATA'),
  ('sklearn\\tests\\test_multiclass.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\test_multiclass.py',
   'DATA'),
  ('sklearn\\tests\\test_multioutput.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\test_multioutput.py',
   'DATA'),
  ('sklearn\\tests\\test_naive_bayes.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\test_naive_bayes.py',
   'DATA'),
  ('sklearn\\tests\\test_pipeline.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\test_pipeline.py',
   'DATA'),
  ('sklearn\\tests\\test_public_functions.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\test_public_functions.py',
   'DATA'),
  ('sklearn\\tests\\test_random_projection.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tests\\test_random_projection.py',
   'DATA'),
  ('sklearn\\tree\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\__init__.py',
   'DATA'),
  ('sklearn\\tree\\_classes.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\_classes.py',
   'DATA'),
  ('sklearn\\tree\\_criterion.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\_criterion.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\tree\\_criterion.pxd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\_criterion.pxd',
   'DATA'),
  ('sklearn\\tree\\_criterion.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\_criterion.pyx',
   'DATA'),
  ('sklearn\\tree\\_export.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\_export.py',
   'DATA'),
  ('sklearn\\tree\\_partitioner.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\_partitioner.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\tree\\_partitioner.pxd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\_partitioner.pxd',
   'DATA'),
  ('sklearn\\tree\\_partitioner.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\_partitioner.pyx',
   'DATA'),
  ('sklearn\\tree\\_reingold_tilford.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\_reingold_tilford.py',
   'DATA'),
  ('sklearn\\tree\\_splitter.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\_splitter.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\tree\\_splitter.pxd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\_splitter.pxd',
   'DATA'),
  ('sklearn\\tree\\_splitter.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\_splitter.pyx',
   'DATA'),
  ('sklearn\\tree\\_tree.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\_tree.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\tree\\_tree.pxd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\_tree.pxd',
   'DATA'),
  ('sklearn\\tree\\_tree.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\_tree.pyx',
   'DATA'),
  ('sklearn\\tree\\_utils.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\_utils.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\tree\\_utils.pxd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\_utils.pxd',
   'DATA'),
  ('sklearn\\tree\\_utils.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\_utils.pyx',
   'DATA'),
  ('sklearn\\tree\\meson.build',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\meson.build',
   'DATA'),
  ('sklearn\\tree\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\tree\\tests\\test_export.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\tests\\test_export.py',
   'DATA'),
  ('sklearn\\tree\\tests\\test_monotonic_tree.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\tests\\test_monotonic_tree.py',
   'DATA'),
  ('sklearn\\tree\\tests\\test_reingold_tilford.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\tests\\test_reingold_tilford.py',
   'DATA'),
  ('sklearn\\tree\\tests\\test_tree.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\tree\\tests\\test_tree.py',
   'DATA'),
  ('sklearn\\utils\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\__init__.py',
   'DATA'),
  ('sklearn\\utils\\_arpack.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_arpack.py',
   'DATA'),
  ('sklearn\\utils\\_array_api.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_array_api.py',
   'DATA'),
  ('sklearn\\utils\\_available_if.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_available_if.py',
   'DATA'),
  ('sklearn\\utils\\_bunch.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_bunch.py',
   'DATA'),
  ('sklearn\\utils\\_chunking.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_chunking.py',
   'DATA'),
  ('sklearn\\utils\\_cython_blas.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_cython_blas.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\_cython_blas.pxd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_cython_blas.pxd',
   'DATA'),
  ('sklearn\\utils\\_cython_blas.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_cython_blas.pyx',
   'DATA'),
  ('sklearn\\utils\\_encode.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_encode.py',
   'DATA'),
  ('sklearn\\utils\\_estimator_html_repr.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_estimator_html_repr.py',
   'DATA'),
  ('sklearn\\utils\\_fast_dict.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_fast_dict.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\_fast_dict.pxd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_fast_dict.pxd',
   'DATA'),
  ('sklearn\\utils\\_fast_dict.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_fast_dict.pyx',
   'DATA'),
  ('sklearn\\utils\\_heap.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_heap.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\_heap.pxd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_heap.pxd',
   'DATA'),
  ('sklearn\\utils\\_heap.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_heap.pyx',
   'DATA'),
  ('sklearn\\utils\\_indexing.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_indexing.py',
   'DATA'),
  ('sklearn\\utils\\_isfinite.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_isfinite.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\_isfinite.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_isfinite.pyx',
   'DATA'),
  ('sklearn\\utils\\_mask.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_mask.py',
   'DATA'),
  ('sklearn\\utils\\_metadata_requests.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_metadata_requests.py',
   'DATA'),
  ('sklearn\\utils\\_missing.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_missing.py',
   'DATA'),
  ('sklearn\\utils\\_mocking.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_mocking.py',
   'DATA'),
  ('sklearn\\utils\\_openmp_helpers.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_openmp_helpers.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\_openmp_helpers.pxd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_openmp_helpers.pxd',
   'DATA'),
  ('sklearn\\utils\\_openmp_helpers.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_openmp_helpers.pyx',
   'DATA'),
  ('sklearn\\utils\\_optional_dependencies.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_optional_dependencies.py',
   'DATA'),
  ('sklearn\\utils\\_param_validation.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_param_validation.py',
   'DATA'),
  ('sklearn\\utils\\_plotting.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_plotting.py',
   'DATA'),
  ('sklearn\\utils\\_pprint.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_pprint.py',
   'DATA'),
  ('sklearn\\utils\\_random.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_random.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\_random.pxd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_random.pxd',
   'DATA'),
  ('sklearn\\utils\\_random.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_random.pyx',
   'DATA'),
  ('sklearn\\utils\\_repr_html\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_repr_html\\__init__.py',
   'DATA'),
  ('sklearn\\utils\\_repr_html\\base.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_repr_html\\base.py',
   'DATA'),
  ('sklearn\\utils\\_repr_html\\estimator.css',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_repr_html\\estimator.css',
   'DATA'),
  ('sklearn\\utils\\_repr_html\\estimator.js',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_repr_html\\estimator.js',
   'DATA'),
  ('sklearn\\utils\\_repr_html\\estimator.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_repr_html\\estimator.py',
   'DATA'),
  ('sklearn\\utils\\_repr_html\\params.css',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_repr_html\\params.css',
   'DATA'),
  ('sklearn\\utils\\_repr_html\\params.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_repr_html\\params.py',
   'DATA'),
  ('sklearn\\utils\\_repr_html\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_repr_html\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\utils\\_repr_html\\tests\\test_estimator.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_repr_html\\tests\\test_estimator.py',
   'DATA'),
  ('sklearn\\utils\\_repr_html\\tests\\test_params.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_repr_html\\tests\\test_params.py',
   'DATA'),
  ('sklearn\\utils\\_response.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_response.py',
   'DATA'),
  ('sklearn\\utils\\_seq_dataset.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_seq_dataset.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\_seq_dataset.pxd.tp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_seq_dataset.pxd.tp',
   'DATA'),
  ('sklearn\\utils\\_seq_dataset.pyx.tp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_seq_dataset.pyx.tp',
   'DATA'),
  ('sklearn\\utils\\_set_output.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_set_output.py',
   'DATA'),
  ('sklearn\\utils\\_show_versions.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_show_versions.py',
   'DATA'),
  ('sklearn\\utils\\_sorting.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_sorting.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\_sorting.pxd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_sorting.pxd',
   'DATA'),
  ('sklearn\\utils\\_sorting.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_sorting.pyx',
   'DATA'),
  ('sklearn\\utils\\_tags.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_tags.py',
   'DATA'),
  ('sklearn\\utils\\_test_common\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_test_common\\__init__.py',
   'DATA'),
  ('sklearn\\utils\\_test_common\\instance_generator.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_test_common\\instance_generator.py',
   'DATA'),
  ('sklearn\\utils\\_testing.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_testing.py',
   'DATA'),
  ('sklearn\\utils\\_typedefs.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_typedefs.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\_typedefs.pxd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_typedefs.pxd',
   'DATA'),
  ('sklearn\\utils\\_typedefs.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_typedefs.pyx',
   'DATA'),
  ('sklearn\\utils\\_unique.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_unique.py',
   'DATA'),
  ('sklearn\\utils\\_user_interface.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_user_interface.py',
   'DATA'),
  ('sklearn\\utils\\_vector_sentinel.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_vector_sentinel.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\_vector_sentinel.pxd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_vector_sentinel.pxd',
   'DATA'),
  ('sklearn\\utils\\_vector_sentinel.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_vector_sentinel.pyx',
   'DATA'),
  ('sklearn\\utils\\_weight_vector.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_weight_vector.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\_weight_vector.pxd.tp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_weight_vector.pxd.tp',
   'DATA'),
  ('sklearn\\utils\\_weight_vector.pyx.tp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\_weight_vector.pyx.tp',
   'DATA'),
  ('sklearn\\utils\\arrayfuncs.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\arrayfuncs.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\arrayfuncs.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\arrayfuncs.pyx',
   'DATA'),
  ('sklearn\\utils\\class_weight.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\class_weight.py',
   'DATA'),
  ('sklearn\\utils\\deprecation.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\deprecation.py',
   'DATA'),
  ('sklearn\\utils\\discovery.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\discovery.py',
   'DATA'),
  ('sklearn\\utils\\estimator_checks.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\estimator_checks.py',
   'DATA'),
  ('sklearn\\utils\\extmath.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\extmath.py',
   'DATA'),
  ('sklearn\\utils\\fixes.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\fixes.py',
   'DATA'),
  ('sklearn\\utils\\graph.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\graph.py',
   'DATA'),
  ('sklearn\\utils\\meson.build',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\meson.build',
   'DATA'),
  ('sklearn\\utils\\metadata_routing.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\metadata_routing.py',
   'DATA'),
  ('sklearn\\utils\\metaestimators.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\metaestimators.py',
   'DATA'),
  ('sklearn\\utils\\multiclass.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\multiclass.py',
   'DATA'),
  ('sklearn\\utils\\murmurhash.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\murmurhash.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\murmurhash.pxd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\murmurhash.pxd',
   'DATA'),
  ('sklearn\\utils\\murmurhash.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\murmurhash.pyx',
   'DATA'),
  ('sklearn\\utils\\optimize.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\optimize.py',
   'DATA'),
  ('sklearn\\utils\\parallel.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\parallel.py',
   'DATA'),
  ('sklearn\\utils\\random.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\random.py',
   'DATA'),
  ('sklearn\\utils\\sparsefuncs.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\sparsefuncs.py',
   'DATA'),
  ('sklearn\\utils\\sparsefuncs_fast.cp313-win_amd64.lib',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\sparsefuncs_fast.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\sparsefuncs_fast.pyx',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\sparsefuncs_fast.pyx',
   'DATA'),
  ('sklearn\\utils\\src\\MurmurHash3.cpp',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\src\\MurmurHash3.cpp',
   'DATA'),
  ('sklearn\\utils\\src\\MurmurHash3.h',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\src\\MurmurHash3.h',
   'DATA'),
  ('sklearn\\utils\\stats.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\stats.py',
   'DATA'),
  ('sklearn\\utils\\tests\\__init__.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\__init__.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_arpack.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_arpack.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_array_api.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_array_api.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_arrayfuncs.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_arrayfuncs.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_bunch.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_bunch.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_chunking.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_chunking.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_class_weight.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_class_weight.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_cython_blas.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_cython_blas.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_deprecation.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_deprecation.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_encode.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_encode.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_estimator_checks.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_estimator_checks.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_estimator_html_repr.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_estimator_html_repr.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_extmath.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_extmath.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_fast_dict.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_fast_dict.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_fixes.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_fixes.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_graph.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_graph.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_indexing.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_indexing.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_mask.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_mask.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_metaestimators.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_metaestimators.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_missing.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_missing.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_mocking.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_mocking.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_multiclass.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_multiclass.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_murmurhash.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_murmurhash.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_optimize.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_optimize.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_parallel.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_parallel.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_param_validation.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_param_validation.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_plotting.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_plotting.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_pprint.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_pprint.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_random.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_random.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_response.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_response.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_seq_dataset.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_seq_dataset.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_set_output.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_set_output.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_shortest_path.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_shortest_path.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_show_versions.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_show_versions.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_sparsefuncs.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_sparsefuncs.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_stats.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_stats.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_tags.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_tags.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_testing.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_testing.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_typedefs.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_typedefs.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_unique.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_unique.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_user_interface.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_user_interface.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_validation.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_validation.py',
   'DATA'),
  ('sklearn\\utils\\tests\\test_weight_vector.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\tests\\test_weight_vector.py',
   'DATA'),
  ('sklearn\\utils\\validation.py',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\sklearn\\utils\\validation.py',
   'DATA'),
  ('templates\\advanced_charts.html',
   'E:\\\u200f\u200fتحليل حميع القطاعات - نسخه محدثه  19-9 مساءاً - '
   'نسخة\\templates\\advanced_charts.html',
   'DATA'),
  ('templates\\backup.html',
   'E:\\\u200f\u200fتحليل حميع القطاعات - نسخه محدثه  19-9 مساءاً - '
   'نسخة\\templates\\backup.html',
   'DATA'),
  ('templates\\custom_criteria.html',
   'E:\\\u200f\u200fتحليل حميع القطاعات - نسخه محدثه  19-9 مساءاً - '
   'نسخة\\templates\\custom_criteria.html',
   'DATA'),
  ('templates\\dashboard.html',
   'E:\\\u200f\u200fتحليل حميع القطاعات - نسخه محدثه  19-9 مساءاً - '
   'نسخة\\templates\\dashboard.html',
   'DATA'),
  ('templates\\error.html',
   'E:\\\u200f\u200fتحليل حميع القطاعات - نسخه محدثه  19-9 مساءاً - '
   'نسخة\\templates\\error.html',
   'DATA'),
  ('templates\\index.html',
   'E:\\\u200f\u200fتحليل حميع القطاعات - نسخه محدثه  19-9 مساءاً - '
   'نسخة\\templates\\index.html',
   'DATA'),
  ('templates\\interactive_charts.html',
   'E:\\\u200f\u200fتحليل حميع القطاعات - نسخه محدثه  19-9 مساءاً - '
   'نسخة\\templates\\interactive_charts.html',
   'DATA'),
  ('templates\\login.html',
   'E:\\\u200f\u200fتحليل حميع القطاعات - نسخه محدثه  19-9 مساءاً - '
   'نسخة\\templates\\login.html',
   'DATA'),
  ('templates\\performance.html',
   'E:\\\u200f\u200fتحليل حميع القطاعات - نسخه محدثه  19-9 مساءاً - '
   'نسخة\\templates\\performance.html',
   'DATA'),
  ('templates\\results.html',
   'E:\\\u200f\u200fتحليل حميع القطاعات - نسخه محدثه  19-9 مساءاً - '
   'نسخة\\templates\\results.html',
   'DATA'),
  ('templates\\results_clean.html',
   'E:\\\u200f\u200fتحليل حميع القطاعات - نسخه محدثه  19-9 مساءاً - '
   'نسخة\\templates\\results_clean.html',
   'DATA'),
  ('templates\\results_new.html',
   'E:\\\u200f\u200fتحليل حميع القطاعات - نسخه محدثه  19-9 مساءاً - '
   'نسخة\\templates\\results_new.html',
   'DATA'),
  ('templates\\results_with_charts.html',
   'E:\\\u200f\u200fتحليل حميع القطاعات - نسخه محدثه  19-9 مساءاً - '
   'نسخة\\templates\\results_with_charts.html',
   'DATA'),
  ('templates\\settings.html',
   'E:\\\u200f\u200fتحليل حميع القطاعات - نسخه محدثه  19-9 مساءاً - '
   'نسخة\\templates\\settings.html',
   'DATA'),
  ('templates\\simple_charts.html',
   'E:\\\u200f\u200fتحليل حميع القطاعات - نسخه محدثه  19-9 مساءاً - '
   'نسخة\\templates\\simple_charts.html',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('plotly\\package_data\\datasets\\medals.csv.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly\\package_data\\datasets\\medals.csv.gz',
   'DATA'),
  ('plotly\\package_data\\templates\\plotly_white.json',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly\\package_data\\templates\\plotly_white.json',
   'DATA'),
  ('plotly\\validators\\_validators.json',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly\\validators\\_validators.json',
   'DATA'),
  ('plotly\\package_data\\datasets\\tips.csv.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly\\package_data\\datasets\\tips.csv.gz',
   'DATA'),
  ('plotly\\package_data\\templates\\gridon.json',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly\\package_data\\templates\\gridon.json',
   'DATA'),
  ('plotly\\package_data\\datasets\\wind.csv.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly\\package_data\\datasets\\wind.csv.gz',
   'DATA'),
  ('plotly\\package_data\\datasets\\election.geojson.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly\\package_data\\datasets\\election.geojson.gz',
   'DATA'),
  ('plotly\\package_data\\datasets\\carshare.csv.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly\\package_data\\datasets\\carshare.csv.gz',
   'DATA'),
  ('plotly\\package_data\\widgetbundle.js',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly\\package_data\\widgetbundle.js',
   'DATA'),
  ('plotly\\package_data\\templates\\xgridoff.json',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly\\package_data\\templates\\xgridoff.json',
   'DATA'),
  ('plotly\\package_data\\templates\\presentation.json',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly\\package_data\\templates\\presentation.json',
   'DATA'),
  ('plotly\\package_data\\templates\\ygridoff.json',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly\\package_data\\templates\\ygridoff.json',
   'DATA'),
  ('plotly\\package_data\\datasets\\experiment.csv.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly\\package_data\\datasets\\experiment.csv.gz',
   'DATA'),
  ('plotly\\package_data\\datasets\\iris.csv.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly\\package_data\\datasets\\iris.csv.gz',
   'DATA'),
  ('plotly\\package_data\\datasets\\gapminder.csv.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly\\package_data\\datasets\\gapminder.csv.gz',
   'DATA'),
  ('plotly\\package_data\\templates\\ggplot2.json',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly\\package_data\\templates\\ggplot2.json',
   'DATA'),
  ('plotly\\package_data\\templates\\plotly.json',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly\\package_data\\templates\\plotly.json',
   'DATA'),
  ('plotly\\package_data\\templates\\simple_white.json',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly\\package_data\\templates\\simple_white.json',
   'DATA'),
  ('plotly\\package_data\\templates\\plotly_dark.json',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly\\package_data\\templates\\plotly_dark.json',
   'DATA'),
  ('plotly\\package_data\\datasets\\election.csv.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly\\package_data\\datasets\\election.csv.gz',
   'DATA'),
  ('plotly\\package_data\\datasets\\stocks.csv.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly\\package_data\\datasets\\stocks.csv.gz',
   'DATA'),
  ('plotly\\package_data\\templates\\seaborn.json',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly\\package_data\\templates\\seaborn.json',
   'DATA'),
  ('plotly\\package_data\\plotly.min.js',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly\\package_data\\plotly.min.js',
   'DATA'),
  ('certifi\\cacert.pem',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'e:\\تحليل جميع القطاعات\\.venv\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.svg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.png',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.svg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.pdf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.pdf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\matplotlibrc',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\matplotlibrc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.svg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.svg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.svg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.pdf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\kpsewhich.lua',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\kpsewhich.lua',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.svg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help_large.png',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.png',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back_large.png',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\logo2.png',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\logo2.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home_large.png',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward_large.png',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.svg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move_large.png',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\msft.csv',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\msft.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.png',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.png',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.pdf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.pdf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots_large.png',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.svg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.pdf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave_large.png',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\petroff10.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\petroff10.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.pdf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\README.txt',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\README.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.png',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.png',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.png',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\goog.npz',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\goog.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.svg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.png',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.pdf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.png',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'DATA'),
  ('flask-3.1.2.dist-info\\RECORD',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\flask-3.1.2.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('plotly-6.3.0.dist-info\\REQUESTED',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly-6.3.0.dist-info\\REQUESTED',
   'DATA'),
  ('plotly-6.3.0.dist-info\\RECORD',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly-6.3.0.dist-info\\RECORD',
   'DATA'),
  ('flask-3.1.2.dist-info\\licenses\\LICENSE.txt',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\flask-3.1.2.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('flask-3.1.2.dist-info\\METADATA',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\flask-3.1.2.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.3.3.dist-info\\entry_points.txt',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\numpy-2.3.3.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.3.3.dist-info\\RECORD',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\numpy-2.3.3.dist-info\\RECORD',
   'DATA'),
  ('click-8.3.0.dist-info\\INSTALLER',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\click-8.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('flask-3.1.2.dist-info\\entry_points.txt',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\flask-3.1.2.dist-info\\entry_points.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\WHEEL',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('plotly-6.3.0.dist-info\\entry_points.txt',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly-6.3.0.dist-info\\entry_points.txt',
   'DATA'),
  ('flask-3.1.2.dist-info\\REQUESTED',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\flask-3.1.2.dist-info\\REQUESTED',
   'DATA'),
  ('numpy-2.3.3.dist-info\\LICENSE.txt',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\numpy-2.3.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('plotly-6.3.0.dist-info\\WHEEL',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly-6.3.0.dist-info\\WHEEL',
   'DATA'),
  ('flask-3.1.2.dist-info\\INSTALLER',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\flask-3.1.2.dist-info\\INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\METADATA',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('click-8.3.0.dist-info\\RECORD',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\click-8.3.0.dist-info\\RECORD',
   'DATA'),
  ('click-8.3.0.dist-info\\licenses\\LICENSE.txt',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\click-8.3.0.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('click-8.3.0.dist-info\\METADATA',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\click-8.3.0.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.3.3.dist-info\\WHEEL',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\numpy-2.3.3.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.3.3.dist-info\\REQUESTED',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\numpy-2.3.3.dist-info\\REQUESTED',
   'DATA'),
  ('numpy-2.3.3.dist-info\\METADATA',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\numpy-2.3.3.dist-info\\METADATA',
   'DATA'),
  ('plotly-6.3.0.dist-info\\top_level.txt',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly-6.3.0.dist-info\\top_level.txt',
   'DATA'),
  ('numpy-2.3.3.dist-info\\INSTALLER',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\numpy-2.3.3.dist-info\\INSTALLER',
   'DATA'),
  ('plotly-6.3.0.dist-info\\METADATA',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly-6.3.0.dist-info\\METADATA',
   'DATA'),
  ('flask-3.1.2.dist-info\\WHEEL',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\flask-3.1.2.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.3.3.dist-info\\DELVEWHEEL',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\numpy-2.3.3.dist-info\\DELVEWHEEL',
   'DATA'),
  ('click-8.3.0.dist-info\\WHEEL',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\click-8.3.0.dist-info\\WHEEL',
   'DATA'),
  ('plotly-6.3.0.dist-info\\licenses\\LICENSE.txt',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly-6.3.0.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\RECORD',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('plotly-6.3.0.dist-info\\INSTALLER',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\plotly-6.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\INSTALLER',
   'e:\\تحليل جميع '
   'القطاعات\\.venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'DATA'),
  ('base_library.zip',
   'E:\\\u200f\u200fتحليل حميع القطاعات - نسخه محدثه  19-9 مساءاً - '
   'نسخة\\build\\MultiSectorAnalyzer\\base_library.zip',
   'DATA')],
 'python313.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
