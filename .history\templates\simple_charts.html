<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <title>الرسم البياني التفاعلي</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: "Tahoma", sans-serif;
      margin: 20px;
      background: #f8f9fa;
      text-align: center;
    }
    h2 {
      margin: 15px 0;
    }
    canvas {
      max-width: 800px;
      margin: 20px auto;
      display: block;
    }
    button {
      margin: 5px;
      padding: 10px 20px;
      border: none;
      border-radius: 8px;
      background: #007bff;
      color: white;
      cursor: pointer;
      font-size: 14px;
    }
    button:hover {
      background: #0056b3;
    }
  </style>
</head>
<body>

  <div style="background: #e9ecef; padding: 15px; border-radius: 8px; margin: 20px auto; max-width: 800px;">
    <h3 style="margin: 0; color: #495057;">📊 الرسوم البيانية للبيانات المحللة</h3>
    <p style="margin: 5px 0; color: #6c757d;">
      البيانات من: <strong id="dataSource">جاري التحميل...</strong>
    </p>
    <div style="margin-top: 10px;">
      <label for="columnSelect" style="font-weight: bold; color: #495057;">اختر العمود للعرض:</label>
      <select id="columnSelect" style="margin-left: 10px; padding: 5px; border-radius: 4px; border: 1px solid #ccc;">
        <option value="">جاري التحميل...</option>
      </select>
      <button onclick="refreshCharts()" style="margin-left: 10px; padding: 5px 15px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">تحديث</button>
    </div>
  </div>

  <h2>الرسم الدائري (النسب المئوية)</h2>
  <canvas id="pieChart"></canvas>
  <button onclick="downloadChart(pieChart, 'pie_chart.png')">تحميل الرسم الدائري</button>

  <h2>الرسم العمودي (عدد الحالات)</h2>
  <canvas id="barChart"></canvas>
  <button onclick="downloadChart(barChart, 'bar_chart.png')">تحميل الرسم العمودي</button>

  <script id="chartData" type="application/json">
    {{ chart_data | tojson }}
  </script>

  <script>
    // قراءة البيانات من Flask
    const chartData = JSON.parse(document.getElementById('chartData').textContent.trim());
    const values = chartData.values || [];
    const labels = chartData.labels || [];
    const percentages = chartData.percentages || [];
    const columnName = chartData.column_name || 'البيانات';

    console.log('Chart data loaded:', chartData);
    console.log('Values:', values);
    console.log('Labels:', labels);
    console.log('Percentages:', percentages);

    // تحديث عناوين الرسوم
    document.querySelector('h2:first-of-type').textContent = `الرسم الدائري (النسب المئوية) - ${columnName}`;
    document.querySelector('h2:nth-of-type(2)').textContent = `الرسم العمودي (عدد الحالات) - ${columnName}`;

    // تحديث معلومات مصدر البيانات
    const dataSource = chartData.session_id ? `جلسة التحليل ${chartData.session_id}` : 'البيانات الافتراضية';
    document.getElementById('dataSource').textContent = dataSource;

    // التحقق من وجود البيانات
    if (!values.length || !labels.length) {
      console.error('No data available for charts');
      alert('لا توجد بيانات متاحة لعرض الرسوم البيانية');
    }

    // تحميل قائمة الأعمدة المتاحة
    async function loadAvailableColumns() {
      try {
        const response = await fetch('/api/simple-charts/columns');
        const data = await response.json();

        if (data.columns && data.columns.length > 0) {
          const columnSelect = document.getElementById('columnSelect');
          columnSelect.innerHTML = '<option value="">اختر عمود...</option>';

          data.columns.forEach(column => {
            const option = document.createElement('option');
            option.value = column.name;
            option.textContent = `${column.name} (${column.unique_values} قيم فريدة)`;
            if (column.name === columnName) {
              option.selected = true;
            }
            columnSelect.appendChild(option);
          });
        }
      } catch (error) {
        console.error('خطأ في تحميل قائمة الأعمدة:', error);
      }
    }

    // تحديث الرسوم البيانية لعمود محدد
    async function updateChartsForColumn(columnName) {
      try {
        const response = await fetch(`/api/simple-charts/${encodeURIComponent(columnName)}`);
        const data = await response.json();

        if (data.values && data.labels) {
          // تحديث البيانات العامة
          window.currentValues = data.values;
          window.currentLabels = data.labels;
          window.currentPercentages = data.percentages;
          window.currentColumnName = data.column_name;

          // تحديث عناوين الرسوم
          document.querySelector('h2:first-of-type').textContent = `الرسم الدائري (النسب المئوية) - ${data.column_name}`;
          document.querySelector('h2:nth-of-type(2)').textContent = `الرسم العمودي (عدد الحالات) - ${data.column_name}`;

          // تحديث معلومات مصدر البيانات
          const dataSource = data.session_id ? `جلسة التحليل ${data.session_id}` : 'البيانات الافتراضية';
          document.getElementById('dataSource').textContent = dataSource;

          // إعادة رسم الرسوم البيانية
          updatePieChart(data.values, data.labels, data.percentages);
          updateBarChart(data.values, data.labels);

          console.log('تم تحديث الرسوم البيانية للعمود:', columnName);
        } else {
          console.error('بيانات غير صالحة للعمود:', columnName);
        }
      } catch (error) {
        console.error('خطأ في تحديث الرسوم البيانية:', error);
      }
    }

    // وظيفة لتحديث الرسم الدائري
    function updatePieChart(values, labels, percentages) {
      if (window.pieChart) {
        window.pieChart.data.labels = labels;
        window.pieChart.data.datasets[0].data = percentages;
        window.pieChart.update();
      }
    }

    // وظيفة لتحديث الرسم العمودي
    function updateBarChart(values, labels) {
      if (window.barChart) {
        window.barChart.data.labels = labels;
        window.barChart.data.datasets[0].data = values;
        window.barChart.update();
      }
    }

    // وظيفة لتحديث الرسوم البيانية
    function refreshCharts() {
      const selectedColumn = document.getElementById('columnSelect').value;
      if (selectedColumn) {
        updateChartsForColumn(selectedColumn);
      }
    }

    // تحميل قائمة الأعمدة عند تحميل الصفحة
    loadAvailableColumns();

    // إضافة event listener للـ select
    document.getElementById('columnSelect').addEventListener('change', function() {
      const selectedColumn = this.value;
      if (selectedColumn) {
        updateChartsForColumn(selectedColumn);
      }
    });

    // Pie Chart
     const ctxPie = document.getElementById('pieChart').getContext('2d');
     window.pieChart = new Chart(ctxPie, {
      type: 'pie',
      data: {
        labels: labels,
        datasets: [{
          data: percentages,
          backgroundColor: ["#007bff", "#28a745", "#ffc107", "#dc3545", "#17a2b8"]
        }]
      },
      options: {
        responsive: true,
        plugins: {
          legend: { position: 'bottom' },
          tooltip: { enabled: true }
        },
        onClick: (e, elements) => {
          if (elements.length > 0) {
            const i = elements[0].index;
            alert(`📊 تفاصيل البيانات:\nالقيمة: ${values[i]}\nالنسبة: ${percentages[i]}%`);
          }
        }
      }
    });

    // Bar Chart
     const ctxBar = document.getElementById('barChart').getContext('2d');
     window.barChart = new Chart(ctxBar, {
      type: 'bar',
      data: {
        labels: labels,
        datasets: [{
          label: 'عدد الحالات',
          data: values,
          backgroundColor: "#007bff"
        }]
      },
      options: {
        responsive: true,
        plugins: {
          legend: { display: false },
          tooltip: { enabled: true }
        },
        scales: {
          y: { beginAtZero: true }
        },
        onClick: (e, elements) => {
          if (elements.length > 0) {
            const i = elements[0].index;
            alert(`📊 تفاصيل البيانات:\nالقيمة: ${values[i]}\nالنسبة: ${percentages[i]}%`);
          }
        }
      }
    });

    // Download with high resolution
    function downloadChart(chart, filename) {
      const link = document.createElement('a');
      link.href = chart.toBase64Image('image/png', 3);
      link.download = filename;
      link.click();
    }
  </script>
</body>
</html>