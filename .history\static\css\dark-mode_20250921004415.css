
/* Dark Mode Styles */
:root {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-card: #3a3a3a;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --accent-color: #667eea;
    --border-color: #4a4a4a;
}

[data-theme="dark"] {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: var(--text-primary);
}

[data-theme="dark"] .dashboard-header {
    background: rgba(45, 45, 45, 0.95);
    border-bottom: 1px solid var(--border-color);
}

[data-theme="dark"] .chart-card {
    background: rgba(58, 58, 58, 0.95);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .welcome-section {
    background: rgba(58, 58, 58, 0.95);
    color: var(--text-primary);
}

[data-theme="dark"] .statistics-section {
    background: rgba(58, 58, 58, 0.95);
    color: var(--text-primary);
}

[data-theme="dark"] .alerts-section {
    background: rgba(58, 58, 58, 0.95);
    color: var(--text-primary);
}

/* Results Page Dark Mode Styles */
[data-theme="dark"] body {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: var(--text-primary);
}

[data-theme="dark"] .card {
    background: rgba(58, 58, 58, 0.95);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

[data-theme="dark"] .table {
    color: var(--text-primary);
}

[data-theme="dark"] .table th {
    background-color: rgba(102, 126, 234, 0.1);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .table td {
    background-color: transparent;
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .table-hover tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.1);
}

[data-theme="dark"] .alert {
    background: rgba(58, 58, 58, 0.95);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .alert-info {
    background: rgba(23, 162, 184, 0.1);
    border-color: rgba(23, 162, 184, 0.3);
    color: #d1ecf1;
}

[data-theme="dark"] .alert-success {
    background: rgba(40, 167, 69, 0.1);
    border-color: rgba(40, 167, 69, 0.3);
    color: #d4edda;
}

[data-theme="dark"] .alert-warning {
    background: rgba(255, 193, 7, 0.1);
    border-color: rgba(255, 193, 7, 0.3);
    color: #fff3cd;
}

[data-theme="dark"] .alert-danger {
    background: rgba(220, 53, 69, 0.1);
    border-color: rgba(220, 53, 69, 0.3);
    color: #f8d7da;
}

[data-theme="dark"] .badge {
    background-color: rgba(102, 126, 234, 0.2);
    color: var(--text-primary);
}

[data-theme="dark"] .badge.bg-primary {
    background-color: rgba(0, 123, 255, 0.2) !important;
    color: #cce5ff !important;
}

[data-theme="dark"] .badge.bg-success {
    background-color: rgba(40, 167, 69, 0.2) !important;
    color: #d4edda !important;
}

[data-theme="dark"] .badge.bg-info {
    background-color: rgba(23, 162, 184, 0.2) !important;
    color: #d1ecf1 !important;
}

[data-theme="dark"] .badge.bg-warning {
    background-color: rgba(255, 193, 7, 0.2) !important;
    color: #fff3cd !important;
}

[data-theme="dark"] .badge.bg-danger {
    background-color: rgba(220, 53, 69, 0.2) !important;
    color: #f8d7da !important;
}

[data-theme="dark"] .progress {
    background-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .progress-bar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

[data-theme="dark"] .navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

[data-theme="dark"] .stats-summary {
    background-color: rgba(58, 58, 58, 0.95);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .distribution-table {
    color: var(--text-primary);
}

[data-theme="dark"] .distribution-table th {
    background-color: rgba(102, 126, 234, 0.1);
    border-color: var(--border-color);
}

[data-theme="dark"] .distribution-table td {
    border-color: var(--border-color);
}

[data-theme="dark"] .stats-summary-large {
    background-color: rgba(58, 58, 58, 0.95);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .bg-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

[data-theme="dark"] .text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Theme Toggle Button */
.theme-toggle {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1001;
    background: var(--accent-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.theme-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}
