<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - نظام تحليل البيانات</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }

        .navbar-brand {
            font-weight: 600;
        }

        .settings-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .settings-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
            margin-bottom: 20px;
        }

        .settings-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 20px;
        }

        .settings-body {
            padding: 30px;
        }

        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #ddd;
            padding: 12px 15px;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #667eea;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .btn-save {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-save:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .alert {
            border-radius: 10px;
            border: none;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #667eea 0%, #9765c9 100%);">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">
                <i class="fas fa-chart-line me-2"></i>
                نظام تحليل البيانات المتقدم
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard">
                    <i class="fas fa-home me-2"></i>
                    لوحة التحكم
                </a>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-2"></i>
                        {{ user.full_name }}
                    </a>
                    <ul class="dropdown-menu">
                        {% if user.role == 'admin' %}
                        <li><a class="dropdown-item" href="/backup">
                            <i class="fas fa-shield-alt me-2"></i>
                            النسخ الاحتياطية
                        </a></li>
                        {% endif %}
                        <li><a class="dropdown-item" href="/settings">
                            <i class="fas fa-cog me-2"></i>
                            الإعدادات
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/logout">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="settings-container">
            <div class="settings-card">
                <div class="settings-header">
                    <h4 class="mb-0">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات النظام
                    </h4>
                </div>
                <div class="settings-body">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    <i class="fas fa-{{ 'exclamation-triangle' if category == 'danger' else 'info-circle' if category == 'info' else 'check-circle' }}"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <form method="POST">
                        <!-- المظهر واللغة -->
                        <div class="mb-4">
                            <h5 class="mb-3">
                                <i class="fas fa-palette me-2"></i>
                                المظهر واللغة
                            </h5>

                            <div class="row">
                                <div class="col-md-6">
                                    <label for="theme" class="form-label">
                                        <i class="fas fa-moon me-2"></i>
                                        المظهر
                                    </label>
                                    <select class="form-select" id="theme" name="theme">
                                        <option value="light" {{ 'selected' if user_settings.theme == 'light' else '' }}>فاتح</option>
                                        <option value="dark" {{ 'selected' if user_settings.theme == 'dark' else '' }}>داكن</option>
                                        <option value="auto" {{ 'selected' if user_settings.theme == 'auto' else '' }}>تلقائي</option>
                                    </select>
                                </div>

                                <div class="col-md-6">
                                    <label for="language" class="form-label">
                                        <i class="fas fa-language me-2"></i>
                                        اللغة
                                    </label>
                                    <select class="form-select" id="language" name="language">
                                        <option value="ar" {{ 'selected' if user_settings.language == 'ar' else '' }}>العربية</option>
                                        <option value="en" {{ 'selected' if user_settings.language == 'en' else '' }}>English</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات العرض -->
                        <div class="mb-4">
                            <h5 class="mb-3">
                                <i class="fas fa-eye me-2"></i>
                                إعدادات العرض
                            </h5>

                            <div class="setting-item">
                                <div>
                                    <label class="form-label mb-0">
                                        <i class="fas fa-chart-bar me-2"></i>
                                        عرض الرسوم البيانية
                                    </label>
                                    <small class="text-muted d-block">إظهار أو إخفاء الرسوم البيانية في النتائج</small>
                                </div>
                                <label class="switch">
                                    <input type="checkbox" name="show_charts" {{ 'checked' if user_settings.show_charts else '' }}>
                                    <span class="slider"></span>
                                </label>
                            </div>

                            <div class="setting-item">
                                <div>
                                    <label class="form-label mb-0">
                                        <i class="fas fa-table me-2"></i>
                                        عرض الجداول
                                    </label>
                                    <small class="text-muted d-block">إظهار أو إخفاء جداول البيانات</small>
                                </div>
                                <label class="switch">
                                    <input type="checkbox" name="show_tables" {{ 'checked' if user_settings.show_tables else '' }}>
                                    <span class="slider"></span>
                                </label>
                            </div>

                            <div class="setting-item">
                                <div>
                                    <label class="form-label mb-0">
                                        <i class="fas fa-sync me-2"></i>
                                        التحديث التلقائي
                                    </label>
                                    <small class="text-muted d-block">تحديث البيانات تلقائياً كل 30 ثانية</small>
                                </div>
                                <label class="switch">
                                    <input type="checkbox" name="auto_refresh" {{ 'checked' if user_settings.auto_refresh else '' }}>
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>

                        <!-- إعدادات التصدير -->
                        <div class="mb-4">
                            <h5 class="mb-3">
                                <i class="fas fa-file-export me-2"></i>
                                إعدادات التصدير
                            </h5>

                            <div class="row">
                                <div class="col-md-6">
                                    <label for="export_format" class="form-label">
                                        <i class="fas fa-file me-2"></i>
                                        تنسيق التصدير الافتراضي
                                    </label>
                                    <select class="form-select" id="export_format" name="export_format">
                                        <option value="excel" {{ 'selected' if user_settings.export_format == 'excel' else '' }}>Excel</option>
                                        <option value="csv" {{ 'selected' if user_settings.export_format == 'csv' else '' }}>CSV</option>
                                        <option value="pdf" {{ 'selected' if user_settings.export_format == 'pdf' else '' }}>PDF</option>
                                        <option value="json" {{ 'selected' if user_settings.export_format == 'json' else '' }}>JSON</option>
                                    </select>
                                </div>

                                <div class="col-md-6">
                                    <label for="items_per_page" class="form-label">
                                        <i class="fas fa-list me-2"></i>
                                        عدد العناصر في الصفحة
                                    </label>
                                    <select class="form-select" id="items_per_page" name="items_per_page">
                                        <option value="25" {{ 'selected' if user_settings.items_per_page == 25 else '' }}>25</option>
                                        <option value="50" {{ 'selected' if user_settings.items_per_page == 50 else '' }}>50</option>
                                        <option value="100" {{ 'selected' if user_settings.items_per_page == 100 else '' }}>100</option>
                                        <option value="200" {{ 'selected' if user_settings.items_per_page == 200 else '' }}>200</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- معايير التحليل حسب القطاع -->
                        <div class="mb-4">
                            <h5 class="mb-3">
                                <i class="fas fa-list-check me-2"></i>
                                معايير التحليل حسب القطاع
                            </h5>

                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>معلومات:</strong> يمكنك تفعيل أو إلغاء تفعيل معايير التحليل المختلفة لكل قطاع، وتحديد ما إذا كنت تريد عرض رسوم بيانية أو جداول للبيانات.
                            </div>

                            <!-- القطاع الجمركي -->
                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">
                                        <i class="fas fa-ship me-2"></i>
                                        القطاع الجمركي (Customs)
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6 class="mb-3">معايير التحليل:</h6>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="customs_data_quality" name="customs_criteria[]" value="data_quality" {{ 'checked' if 'data_quality' in user_settings.customs_criteria_list else '' }}>
                                                <label class="form-check-label" for="customs_data_quality">
                                                    <strong>جودة البيانات</strong>
                                                    <br><small class="text-muted">الدقة، الاكتمال، الاتساق، الحداثة، الفريدة</small>
                                                </label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="customs_analysis" name="customs_criteria[]" value="customs_analysis" {{ 'checked' if 'customs_analysis' in user_settings.customs_criteria_list else '' }}>
                                                <label class="form-check-label" for="customs_analysis">
                                                    <strong>التحليل الجمركي والإحصائي</strong>
                                                    <br><small class="text-muted">توزيع البيانات، الأنماط الزمنية، المخاطر، المقارنة المرجعية</small>
                                                </label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="customs_legal" name="customs_criteria[]" value="customs_legal" {{ 'checked' if 'customs_legal' in user_settings.customs_criteria_list else '' }}>
                                                <label class="form-check-label" for="customs_legal">
                                                    <strong>الجانب الجمركي والقانوني</strong>
                                                    <br><small class="text-muted">تصنيف HS، القيمة الجمركية، الضرائب، المنشأ، الضوابط الأمنية</small>
                                                </label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="customs_intelligent" name="customs_criteria[]" value="intelligent_prediction" {{ 'checked' if 'intelligent_prediction' in user_settings.customs_criteria_list else '' }}>
                                                <label class="form-check-label" for="customs_intelligent">
                                                    <strong>التحليل الذكي والتنبؤ</strong>
                                                    <br><small class="text-muted">مؤشرات الأداء، التحليل التنبؤي، تحليل الشبكات، الذكاء الاصطناعي</small>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <h6 class="mb-3">خيارات العرض:</h6>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="customs_show_charts" name="customs_display[]" value="charts" {{ 'checked' if 'charts' in user_settings.customs_display_list else '' }}>
                                                <label class="form-check-label" for="customs_show_charts">
                                                    <i class="fas fa-chart-bar me-2"></i>
                                                    عرض الرسوم البيانية
                                                </label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="customs_show_tables" name="customs_display[]" value="tables" {{ 'checked' if 'tables' in user_settings.customs_display_list else '' }}>
                                                <label class="form-check-label" for="customs_show_tables">
                                                    <i class="fas fa-table me-2"></i>
                                                    عرض الجداول
                                                </label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="customs_show_kpis" name="customs_display[]" value="kpis" {{ 'checked' if 'kpis' in user_settings.customs_display_list else '' }}>
                                                <label class="form-check-label" for="customs_show_kpis">
                                                    <i class="fas fa-tachometer-alt me-2"></i>
                                                    عرض مؤشرات الأداء (KPIs)
                                                </label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="customs_show_alerts" name="customs_display[]" value="alerts" {{ 'checked' if 'alerts' in user_settings.customs_display_list else '' }}>
                                                <label class="form-check-label" for="customs_show_alerts">
                                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                                    عرض التنبيهات والمخاطر
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- قطاعات أخرى (قابلة للتوسع) -->
                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">
                                        <i class="fas fa-plus-circle me-2"></i>
                                        قطاعات أخرى
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted mb-0">
                                        يمكن إضافة معايير تحليل مخصصة للقطاعات الأخرى (التأمين، التعليم، الصحة، المالية، الموارد البشرية) في التحديثات المستقبلية.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات النسخ الاحتياطي -->
                        {% if user.role == 'admin' %}
                        <div class="mb-4">
                            <h5 class="mb-3">
                                <i class="fas fa-shield-alt me-2"></i>
                                إعدادات النسخ الاحتياطي
                            </h5>

                            <div class="setting-item">
                                <div>
                                    <label class="form-label mb-0">
                                        <i class="fas fa-clock me-2"></i>
                                        النسخ التلقائي
                                    </label>
                                    <small class="text-muted d-block">تفعيل النسخ الاحتياطي التلقائي اليومي والأسبوعي</small>
                                </div>
                                <label class="switch">
                                    <input type="checkbox" id="auto_backup" name="auto_backup" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <label for="backup_retention_days" class="form-label">
                                        <i class="fas fa-calendar me-2"></i>
                                        الاحتفاظ بالنسخ اليومية (أيام)
                                    </label>
                                    <select class="form-select" id="backup_retention_days" name="backup_retention_days">
                                        <option value="3">3 أيام</option>
                                        <option value="7" selected>7 أيام</option>
                                        <option value="14">14 يوم</option>
                                        <option value="30">30 يوم</option>
                                    </select>
                                </div>

                                <div class="col-md-6">
                                    <label for="backup_retention_weeks" class="form-label">
                                        <i class="fas fa-calendar-week me-2"></i>
                                        الاحتفاظ بالنسخ الأسبوعية (أسابيع)
                                    </label>
                                    <select class="form-select" id="backup_retention_weeks" name="backup_retention_weeks">
                                        <option value="2">2 أسابيع</option>
                                        <option value="4" selected>4 أسابيع</option>
                                        <option value="8">8 أسابيع</option>
                                        <option value="12">12 أسبوع</option>
                                    </select>
                                </div>
                            </div>

                            <div class="mt-3">
                                <a href="/backup" class="btn btn-outline-primary">
                                    <i class="fas fa-external-link-alt me-2"></i>
                                    إدارة النسخ الاحتياطية
                                </a>
                            </div>
                        </div>
                        {% endif %}

                        <!-- إعدادات كلمة المرور -->
                        <div class="mb-4">
                            <h5 class="mb-3">
                                <i class="fas fa-key me-2"></i>
                                تغيير كلمة المرور
                            </h5>

                            <div class="row">
                                <div class="col-md-6">
                                    <label for="current_password" class="form-label">
                                        كلمة المرور الحالية
                                    </label>
                                    <input type="password" class="form-control" id="current_password" name="current_password">
                                </div>

                                <div class="col-md-6">
                                    <label for="new_password" class="form-label">
                                        كلمة المرور الجديدة
                                    </label>
                                    <input type="password" class="form-control" id="new_password" name="new_password">
                                </div>
                            </div>

                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <label for="confirm_password" class="form-label">
                                        تأكيد كلمة المرور الجديدة
                                    </label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                                </div>
                            </div>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-save">
                                <i class="fas fa-save me-2"></i>
                                حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // التحقق من تطابق كلمة المرور
        document.getElementById('confirm_password').addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = this.value;

            if (newPassword !== confirmPassword) {
                this.setCustomValidity('كلمة المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });

        // تحميل إعدادات النسخ الاحتياطي
        async function loadBackupSettings() {
            try {
                const response = await fetch('/api/backup/settings');
                const data = await response.json();

                if (data.success) {
                    document.getElementById('auto_backup').checked = data.settings.auto_backup;
                    document.getElementById('backup_retention_days').value = data.settings.retention_days;
                    document.getElementById('backup_retention_weeks').value = data.settings.retention_weeks;
                }
            } catch (error) {
                console.error('خطأ في تحميل إعدادات النسخ الاحتياطي:', error);
            }
        }

        // حفظ إعدادات النسخ الاحتياطي
        async function saveBackupSettings() {
            const settings = {
                auto_backup: document.getElementById('auto_backup').checked,
                retention_days: document.getElementById('backup_retention_days').value,
                retention_weeks: document.getElementById('backup_retention_weeks').value
            };

            try {
                const response = await fetch('/api/backup/settings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(settings)
                });

                const data = await response.json();

                if (data.success) {
                    alert('تم حفظ إعدادات النسخ الاحتياطي بنجاح');
                } else {
                    alert('فشل في حفظ إعدادات النسخ الاحتياطي: ' + data.message);
                }
            } catch (error) {
                console.error('خطأ في حفظ إعدادات النسخ الاحتياطي:', error);
                alert('خطأ في حفظ إعدادات النسخ الاحتياطي');
            }
        }

        // تحميل الإعدادات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تحميل إعدادات النسخ الاحتياطي إذا كان المستخدم مديراً
            const backupSettings = document.getElementById('auto_backup');
            if (backupSettings) {
                loadBackupSettings();
            }
        });

        // التحقق من صحة النموذج
        document.querySelector('form').addEventListener('submit', function(e) {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            const currentPassword = document.getElementById('current_password').value;

            // إذا تم إدخال كلمة مرور جديدة، تأكد من إدخال كلمة المرور الحالية
            if (newPassword && !currentPassword) {
                e.preventDefault();
                alert('يرجى إدخال كلمة المرور الحالية');
                document.getElementById('current_password').focus();
                return false;
            }

            // إذا تم إدخال كلمة مرور جديدة، تأكد من التطابق
            if (newPassword && newPassword !== confirmPassword) {
                e.preventDefault();
                alert('كلمة المرور الجديدة غير متطابقة');
                document.getElementById('confirm_password').focus();
                return false;
            }

            // حفظ إعدادات النسخ الاحتياطي إذا كانت موجودة
            const backupSettings = document.getElementById('auto_backup');
            if (backupSettings) {
                e.preventDefault(); // منع الإرسال العادي
                saveBackupSettings().then(() => {
                    // إرسال النموذج بعد حفظ إعدادات النسخ
                    this.submit();
                });
                return false;
            }
        });
    </script>
</body>
</html>