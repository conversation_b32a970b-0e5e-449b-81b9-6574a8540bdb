
/* Dark Mode Styles */
:root {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-card: #3a3a3a;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --accent-color: #667eea;
    --border-color: #4a4a4a;
}

[data-theme="dark"] {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: var(--text-primary);
}

[data-theme="dark"] .dashboard-header {
    background: rgba(45, 45, 45, 0.95);
    border-bottom: 1px solid var(--border-color);
}

[data-theme="dark"] .chart-card {
    background: rgba(58, 58, 58, 0.95);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .welcome-section {
    background: rgba(58, 58, 58, 0.95);
    color: var(--text-primary);
}

[data-theme="dark"] .statistics-section {
    background: rgba(58, 58, 58, 0.95);
    color: var(--text-primary);
}

[data-theme="dark"] .alerts-section {
    background: rgba(58, 58, 58, 0.95);
    color: var(--text-primary);
}

/* Theme Toggle Button */
.theme-toggle {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1001;
    background: var(--accent-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.theme-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}
