<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نتائج التحليل - نظام تحليل البيانات</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #63a0dd;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(252, 248, 248, 0.1);
            margin-bottom: 20px;
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: rgb(248, 242, 242);
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }

        /* Interactive Charts Styles */
        .interactive-charts-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin: 30px 0;
        }

        .chart-controls {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .chart-controls .form-select {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
        }

        .chart-controls .form-select option {
            background: #667eea;
            color: white;
        }

        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            height: 350px;
        }

        .chart-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .chart-card:hover {
            transform: translateY(-5px);
        }

        .btn-outline-light {
            border: 2px solid rgba(255,255,255,0.5);
            color: white;
        }

        .btn-outline-light:hover {
            background: rgba(255,255,255,0.2);
            border-color: white;
            color: white;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }

        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }

        .table th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
        }

        .table td {
            border: none;
            border-bottom: 1px solid #dee2e6;
        }

        .badge {
            font-size: 0.8rem;
        }

        .alert {
            border: none;
            border-radius: 10px;
        }

        .navbar {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .progress {
            height: 8px;
            border-radius: 4px;
        }

        .chart-wrapper {
            position: relative;
            height: 400px;
            margin-bottom: 20px;
        }

        .chart-canvas {
            max-height: 400px;
        }

        .distribution-table {
            font-size: 0.9rem;
        }

        .distribution-table th {
            background-color: #e9ecef;
            font-weight: 600;
            text-align: center;
        }

        .distribution-table td {
            vertical-align: middle;
        }

        .stats-summary {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
        }

        .chart-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .chart-card {
            border: none;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .chart-wrapper-large {
            position: relative;
            height: 500px;
            margin-bottom: 20px;
        }

        .chart-canvas-large {
            max-height: 500px;
        }

        .stats-summary-large {
            background-color: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            height: 100%;
        }

        .bg-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        }

        .text-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
        }

        .btn-gradient:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            color: white;
        }

        .distributions-section {
            margin-top: 30px;
        }

        .distributions-section .card {
            margin-bottom: 40px;
        }

        .distributions-section .chart-wrapper-large {
            height: 450px;
        }

        .distributions-section .table {
            margin-bottom: 0;
        }

        .distributions-section .stats-summary-large {
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        /* Table Sky Theme */
        .table-sky {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            overflow: hidden;
        }

        .table-sky thead th {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: white;
            font-weight: 600;
            text-align: center;
            vertical-align: middle;
            padding: 15px 10px;
        }

        .table-sky tbody td {
            background: rgba(255, 255, 255, 0.05);
            border: none;
            color: white;
            text-align: center;
            vertical-align: middle;
            padding: 12px 10px;
        }

        .table-sky tbody tr:hover td {
            background: rgba(255, 255, 255, 0.1);
        }

        .table-sky .badge {
            background: rgba(255, 255, 255, 0.2) !important;
            color: white !important;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        /* Sortable Headers */
        .sortable {
            cursor: pointer;
            user-select: none;
            position: relative;
        }

        .sortable:hover {
            background: rgba(255, 255, 255, 0.15) !important;
        }

        .sort-icon {
            margin-left: 5px;
            opacity: 0.5;
            transition: opacity 0.3s ease;
        }

        .sortable:hover .sort-icon {
            opacity: 1;
        }

        .sortable.sort-asc .sort-icon:before {
            content: '\f145'; /* fa-sort-up */
        }

        .sortable.sort-desc .sort-icon:before {
            content: '\f144'; /* fa-sort-down */
        }

        /* Merge Table Sortable Headers */
        #mergeAnalysisTable .sortable {
            background: rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            user-select: none;
        }

        #mergeAnalysisTable .sortable:hover {
            background: rgba(255, 255, 255, 0.2) !important;
            transform: translateY(-1px);
        }

        #mergeAnalysisTable .sort-icon {
            margin-right: 5px;
            margin-left: 0;
            font-size: 0.8em;
            opacity: 0.5;
            transition: opacity 0.3s ease;
        }

        #mergeAnalysisTable .sortable:hover .sort-icon {
            opacity: 1;
        }

        #mergeAnalysisTable .sortable.sort-asc .sort-icon:before {
            content: '\f145'; /* fa-sort-up */
        }

        #mergeAnalysisTable .sortable.sort-desc .sort-icon:before {
            content: '\f144'; /* fa-sort-down */
        }

        /* Person Key Column Styling */
        #mergeAnalysisTable tbody td:first-child {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white !important;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
            border: none;
            position: relative;
            overflow: hidden;
        }

        #mergeAnalysisTable tbody td:first-child::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        #mergeAnalysisTable tbody tr:hover td:first-child::before {
            left: 100%;
        }

        #mergeAnalysisTable tbody td:first-child small {
            color: rgba(255,255,255,0.8) !important;
            font-weight: 400;
            text-shadow: none;
        }

        #mergeAnalysisTable tbody td:first-child:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
            transform: scale(1.02);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            z-index: 10;
        }

        /* Enhanced table styling for merge table */
        #mergeAnalysisTable.table-sky tbody td:first-child {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
        }

        #mergeAnalysisTable.table-sky tbody tr:hover td:first-child {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
        }

        /* PersonRecord Table Styles */
        #personTable {
            width: 100%;
            border-collapse: collapse;
            text-align: center;
            margin-top: 20px;
        }

        #personTable th, #personTable td {
            border: 1px solid #333;
            padding: 8px;
        }

        #personTable th {
            background-color: #f2f2f2;
        }

        .note-normal {
            color: green;
            font-weight: bold;
        }

        .note-merge {
            color: orange;
            font-weight: bold;
        }

        .note-conflict {
            color: red;
            font-weight: bold;
        }

        .note-review {
            color: blue;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .distributions-section .col-md-8,
            .distributions-section .col-md-4 {
                margin-bottom: 20px;
            }

            .table-sky thead th,
            .table-sky tbody td {
                padding: 8px 5px;
                font-size: 0.9rem;
            }

            .card-header .btn-group {
                flex-direction: column;
                gap: 5px;
            }

            .card-header .btn-group .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">
                <i class="fas fa-chart-line me-2"></i>
                نظام تحليل جميع القطاعات
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-home me-1"></i>
                    الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            نتائج تحليل البيانات
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- Session ID -->
                        <div class="alert alert-info">
                            <i class="fas fa-id-badge me-2"></i>
                            <strong>معرف الجلسة:</strong> {{ session_id }}
                        </div>

                        <!-- Basic Statistics -->
                        {% if analysis_data %}
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>
                                    الإحصائيات الأساسية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h3 class="text-primary">{{ "{:,}".format(analysis_data.file_info.rows) if analysis_data.file_info else 'غير متاح' }}</h3>
                                            <p>إجمالي الصفوف</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h3 class="text-success">{{ analysis_data.file_info.columns if analysis_data.file_info else 'غير متاح' }}</h3>
                                            <p>إجمالي الأعمدة</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h3 class="text-warning">
                                                {% set total_missing = 0 %}
                                                {% for col_data in analysis_data.analysis.column_analysis.values() %}
                                                    {% set total_missing = total_missing + col_data.missing_count %}
                                                {% endfor %}
                                                {{ "{:,}".format(total_missing) }}
                                            </h3>
                                            <p>القيم المفقودة</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h3 class="text-info">
                                                {% set data_quality = 100 %}
                                                {% if analysis_data.file_info.rows > 0 %}
                                                    {% set total_cells = analysis_data.file_info.rows * analysis_data.file_info.columns %}
                                                    {% set total_missing = 0 %}
                                                    {% for col_data in analysis_data.analysis.column_analysis.values() %}
                                                        {% set total_missing = total_missing + col_data.missing_count %}
                                                    {% endfor %}
                                                    {% set data_quality = ((total_cells - total_missing) / total_cells * 100) %}
                                                {% endif %}
                                                {{ "%.1f"|format(data_quality) }}%
                                            </h3>
                                            <p>جودة البيانات</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Charts Link -->
                        <!-- تفاصيل جودة البيانات -->
                        {% if analysis_data.analysis and analysis_data.analysis.basic_stats and analysis_data.analysis.basic_stats.quality_details %}
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>
                                    تفاصيل معايير جودة البيانات
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <div class="p-3 bg-success bg-opacity-10 rounded">
                                            <div class="fw-bold text-success h4 mb-1">{{ "%.1f"|format(analysis_data.analysis.basic_stats.quality_details.completeness_score) }}%</div>
                                            <small class="text-muted">الاكتمال</small>
                                            <br><small class="text-muted">({{ "%.1f"|format(analysis_data.analysis.basic_stats.quality_details.error_rates.missing_rate * 100) }}% خطأ)</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="p-3 bg-info bg-opacity-10 rounded">
                                            <div class="fw-bold text-info h4 mb-1">{{ "%.1f"|format(analysis_data.analysis.basic_stats.quality_details.uniqueness_score) }}%</div>
                                            <small class="text-muted">الفرادة</small>
                                            <br><small class="text-muted">({{ "%.1f"|format(analysis_data.analysis.basic_stats.quality_details.error_rates.duplicate_rate * 100) }}% خطأ)</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="p-3 bg-warning bg-opacity-10 rounded">
                                            <div class="fw-bold text-warning h4 mb-1">{{ "%.1f"|format(analysis_data.analysis.basic_stats.quality_details.validity_score) }}%</div>
                                            <small class="text-muted">الصلاحية</small>
                                            <br><small class="text-muted">(قيد التطوير)</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="p-3 bg-primary bg-opacity-10 rounded">
                                            <div class="fw-bold text-primary h4 mb-1">{{ "%.1f"|format(analysis_data.analysis.basic_stats.data_quality_score) }}%</div>
                                            <small class="text-muted">الجودة الإجمالية</small>
                                            <br><small class="text-muted">متوسط المعايير</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <div class="text-center mb-4">
                            <a href="/charts/{{ session_id }}" class="btn btn-gradient btn-lg" title="عرض الرسوم البيانية التفاعلية">
                                <i class="fas fa-chart-bar fa-2x me-2"></i>
                                <span class="d-none d-md-inline">عرض الرسوم البيانية التفاعلية المتقدمة</span>
                                <i class="fas fa-external-link-alt ms-2"></i>
                            </a>
                        </div>
                        {% endif %}

                        <!-- Interactive Column Charts Section -->
                        {% if analysis_data.analysis and analysis_data.analysis.distributions %}
                        <div class="interactive-charts-section">
                            <div class="card-header border-0" style="background: transparent; padding: 30px;">
                                <h4 class="mb-0 text-white text-center fw-bold">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    الرسوم البيانية التفاعلية للأعمدة
                                </h4>
                                <p class="mb-0 text-white-50 text-center mt-2">تحليل بصري تفاعلي لجميع أعمدة البيانات مع إمكانيات تخصيص متقدمة</p>
                            </div>
                            <div class="card-body p-4">
                                <!-- Chart Controls -->
                                <div class="chart-controls">
                                    <div class="row mb-3">
                                        <div class="col-md-3">
                                            <label for="chartType" class="form-label text-white fw-bold">
                                                <i class="fas fa-chart-bar me-2"></i>نوع الرسم البياني:
                                            </label>
                                            <select id="chartType" class="form-select" onchange="updateAllCharts()">
                                                <option value="bar">📊 أعمدة عمودية</option>
                                                <option value="horizontalBar">📈 أعمدة أفقية</option>
                                                <option value="pie">🥧 دائري</option>
                                                <option value="doughnut">🍩 دائري مفرغ</option>
                                                <option value="line">📉 خطي</option>
                                                <option value="radar">🎯 رادار</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="colorScheme" class="form-label text-white fw-bold">
                                                <i class="fas fa-palette me-2"></i>نظام الألوان:
                                            </label>
                                            <select id="colorScheme" class="form-select" onchange="updateAllCharts()">
                                                <option value="default">🎨 افتراضي</option>
                                                <option value="vibrant">🌈 ألوان زاهية</option>
                                                <option value="pastel">🌸 ألوان هادئة</option>
                                                <option value="dark">🌙 ألوان داكنة</option>
                                                <option value="gradient">✨ متدرج</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="maxValues" class="form-label text-white fw-bold">
                                                <i class="fas fa-list-ol me-2"></i>عدد القيم:
                                            </label>
                                            <select id="maxValues" class="form-select" onchange="updateAllCharts()">
                                                <option value="5">أفضل 5 قيم</option>
                                                <option value="10" selected>أفضل 10 قيم</option>
                                                <option value="15">أفضل 15 قيمة</option>
                                                <option value="20">أفضل 20 قيمة</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label text-white fw-bold">
                                                <i class="fas fa-cog me-2"></i>خيارات إضافية:
                                            </label>
                                            <div class="d-flex gap-2">
                                                <button type="button" class="btn btn-outline-light btn-sm" onclick="updateAllCharts()">
                                                    <i class="fas fa-sync me-1"></i>تحديث
                                                </button>
                                                <button type="button" class="btn btn-outline-light btn-sm" onclick="exportAllCharts()">
                                                    <i class="fas fa-download me-1"></i>تصدير
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Charts Container -->
                                <div id="interactiveChartsContainer" class="row">
                                    <!-- Charts will be generated here by JavaScript -->
                                </div>

                                <!-- Export Options -->
                                <div class="text-center mt-4">
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-outline-light" onclick="exportChartsPNG()">
                                            <i class="fas fa-image me-1"></i>تصدير PNG
                                        </button>
                                        <button type="button" class="btn btn-outline-light" onclick="exportChartsPDF()">
                                            <i class="fas fa-file-pdf me-1"></i>تصدير PDF
                                        </button>
                                        <button type="button" class="btn btn-outline-light" onclick="printCharts()">
                                            <i class="fas fa-print me-1"></i>طباعة
                                        </button>
                                        <button type="button" class="btn btn-outline-light" onclick="shareCharts()">
                                            <i class="fas fa-share me-1"></i>مشاركة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Recommendations -->
                        {% if analysis_data.analysis and analysis_data.analysis.recommendations %}
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-lightbulb me-2"></i>
                                    التوصيات
                                </h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    {% for recommendation in analysis_data.analysis.recommendations %}
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        {{ recommendation }}
                                    </li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                        {% endif %}
                        <!-- Advanced Analytics Section -->
                        {% if analysis_data.analysis %}
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-brain me-2"></i>
                                    التحليلات المتقدمة والذكية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <!-- Data Quality Insights -->
                                    <div class="col-md-6 mb-4">
                                        <div class="card h-100">
                                            <div class="card-header bg-info text-white">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-chart-line me-2"></i>
                                                    رؤى جودة البيانات
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row text-center">
                                                    <div class="col-6">
                                                        <div class="p-3 bg-success bg-opacity-10 rounded">
                                                            <div class="fw-bold text-success h4 mb-1">
                                                                {% set completeness = 100 %}
                                                                {% if analysis_data.file_info.rows > 0 %}
                                                                    {% set total_cells = analysis_data.file_info.rows * analysis_data.file_info.columns %}
                                                                    {% set total_missing = 0 %}
                                                                    {% for col_data in analysis_data.analysis.column_analysis.values() %}
                                                                        {% set total_missing = total_missing + col_data.missing_count %}
                                                                    {% endfor %}
                                                                    {% set completeness = ((total_cells - total_missing) / total_cells * 100) %}
                                                                {% endif %}
                                                                {{ "%.1f"|format(completeness) }}%
                                                            </div>
                                                            <small class="text-muted">الاكتمال</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="p-3 bg-warning bg-opacity-10 rounded">
                                                            <div class="fw-bold text-warning h4 mb-1">
                                                                {% set duplicates = 0 %}
                                                                {% if analysis_data.column_duplicates %}
                                                                    {% for dup_key, dup_data in analysis_data.column_duplicates.items() %}
                                                                        {% if dup_data and dup_data.duplicates_2 is defined and dup_data.duplicates_3_plus is defined %}
                                                                            {% set duplicates = duplicates + dup_data.duplicates_2 + dup_data.duplicates_3_plus %}
                                                                        {% endif %}
                                                                    {% endfor %}
                                                                {% endif %}
                                                                {{ "{:,}".format(duplicates) }}
                                                            </div>
                                                            <small class="text-muted">التكرارات</small>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="mt-3">
                                                    <h6>التوصيات الذكية:</h6>
                                                    <ul class="list-unstyled small">
                                                        {% if completeness < 90 %}
                                                        <li><i class="fas fa-exclamation-triangle text-warning me-1"></i>تحسين جودة البيانات مطلوب</li>
                                                        {% endif %}
                                                        {% if duplicates > 0 %}
                                                        <li><i class="fas fa-copy text-info me-1"></i>مراجعة السجلات المكررة</li>
                                                        {% endif %}
                                                        <li><i class="fas fa-check-circle text-success me-1"></i>تحليل شامل مكتمل</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Sector Analysis Insights -->
                                    <div class="col-md-6 mb-4">
                                        <div class="card h-100">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-industry me-2"></i>
                                                    تحليل القطاع المكتشف
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="text-center mb-3">
                                                    <h4 class="text-primary">
                                                        {% if analysis_data.detected_sector %}
                                                            {% if analysis_data.detected_sector == 'insurance' %}
                                                                <i class="fas fa-shield-alt me-2"></i>قطاع التأمين
                                                            {% elif analysis_data.detected_sector == 'customs' %}
                                                                <i class="fas fa-truck me-2"></i>القطاع الجمركي
                                                            {% elif analysis_data.detected_sector == 'education' %}
                                                                <i class="fas fa-graduation-cap me-2"></i>قطاع التعليم
                                                            {% elif analysis_data.detected_sector == 'health' %}
                                                                <i class="fas fa-hospital me-2"></i>قطاع الصحة
                                                            {% elif analysis_data.detected_sector == 'finance' %}
                                                                <i class="fas fa-money-bill-wave me-2"></i>القطاع المالي
                                                            {% elif analysis_data.detected_sector == 'hr' %}
                                                                <i class="fas fa-users me-2"></i>الموارد البشرية
                                                            {% else %}
                                                                <i class="fas fa-folder me-2"></i>قطاع عام
                                                            {% endif %}
                                                        {% else %}
                                                            <i class="fas fa-question-circle me-2"></i>غير محدد
                                                        {% endif %}
                                                    </h4>
                                                    {% if analysis_data.confidence %}
                                                    <small class="text-muted">الثقة: {{ "%.1f"|format(analysis_data.confidence * 100) }}%</small>
                                                    {% endif %}
                                                </div>

                                                <div class="row text-center">
                                                    <div class="col-6">
                                                        <div class="p-2 bg-light rounded">
                                                            <div class="fw-bold h5 mb-1">{{ analysis_data.file_info.columns if analysis_data.file_info else 0 }}</div>
                                                            <small class="text-muted">الأعمدة</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="p-2 bg-light rounded">
                                                            <div class="fw-bold h5 mb-1">{{ "{:,}".format(analysis_data.file_info.rows) if analysis_data.file_info else 0 }}</div>
                                                            <small class="text-muted">الصفوف</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Advanced Statistics -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-calculator me-2"></i>
                                                    الإحصائيات المتقدمة
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <!-- Numeric Columns Stats -->
                                                    {% set numeric_cols = [] %}
                                                    {% for col_name, col_data in analysis_data.analysis.column_analysis.items() %}
                                                        {% if col_data.type == 'number' %}
                                                            {% set numeric_cols = numeric_cols.append(col_name) %}
                                                        {% endif %}
                                                    {% endfor %}

                                                    <div class="col-md-4 mb-3">
                                                        <div class="text-center p-3 bg-primary bg-opacity-10 rounded">
                                                            <h5 class="text-primary mb-1">{{ numeric_cols|length }}</h5>
                                                            <small class="text-muted">أعمدة رقمية</small>
                                                        </div>
                                                    </div>

                                                    <!-- Text Columns Stats -->
                                                    {% set text_cols = [] %}
                                                    {% for col_name, col_data in analysis_data.analysis.column_analysis.items() %}
                                                        {% if col_data.type == 'text' %}
                                                            {% set text_cols = text_cols.append(col_name) %}
                                                        {% endif %}
                                                    {% endfor %}

                                                    <div class="col-md-4 mb-3">
                                                        <div class="text-center p-3 bg-info bg-opacity-10 rounded">
                                                            <h5 class="text-info mb-1">{{ text_cols|length }}</h5>
                                                            <small class="text-muted">أعمدة نصية</small>
                                                        </div>
                                                    </div>

                                                    <!-- Data Patterns -->
                                                    <div class="col-md-4 mb-3">
                                                        <div class="text-center p-3 bg-success bg-opacity-10 rounded">
                                                            <h5 class="text-success mb-1">
                                                                {% set pattern_count = 0 %}
                                                                {% if analysis_data.analysis.sector_specific and analysis_data.analysis.sector_specific.get('data_patterns') %}
                                                                    {% set pattern_count = analysis_data.analysis.sector_specific.data_patterns|length %}
                                                                {% endif %}
                                                                {{ pattern_count }}
                                                            </h5>
                                                            <small class="text-muted">أنماط مكتشفة</small>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Data Patterns Details -->
                                                {% if analysis_data.analysis.sector_specific and analysis_data.analysis.sector_specific.get('data_patterns') %}
                                                <div class="mt-3">
                                                    <h6>الأنماط المكتشفة في البيانات:</h6>
                                                    <div class="row">
                                                        {% for col, pattern in analysis_data.analysis.sector_specific.data_patterns.items() %}
                                                        <div class="col-md-3 mb-2">
                                                            <span class="badge bg-secondary">{{ col }}: {{ pattern }}</span>
                                                        </div>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- AI-Powered Insights -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header bg-gradient">
                                                <h6 class="mb-0 text-white">
                                                    <i class="fas fa-robot me-2"></i>
                                                    رؤى مدعومة بالذكاء الاصطناعي
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <h6>تحليل البيانات:</h6>
                                                        <ul class="list-unstyled">
                                                            <li><i class="fas fa-check-circle text-success me-2"></i>تم تحليل {{ analysis_data.file_info.columns if analysis_data.file_info else 0 }} عمود بنجاح</li>
                                                            <li><i class="fas fa-check-circle text-success me-2"></i>تم كشف {{ analysis_data.detected_sector if analysis_data.detected_sector else 'قطاع عام' }} كقطاع رئيسي</li>
                                                            <li><i class="fas fa-check-circle text-success me-2"></i>تم إنشاء توزيعات بيانات تفاعلية</li>
                                                            {% if analysis_data.analysis.sector_specific and analysis_data.analysis.sector_specific.get('merge_analysis') %}
                                                            <li><i class="fas fa-check-circle text-success me-2"></i>تم تحليل مرشحي الدمج ({{ analysis_data.analysis.sector_specific.merge_analysis.total_candidates }})</li>
                                                            {% endif %}
                                                        </ul>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <h6>التوصيات الذكية:</h6>
                                                        <ul class="list-unstyled">
                                                            {% set completeness = 100 %}
                                                            {% if analysis_data.file_info.rows > 0 %}
                                                                {% set total_cells = analysis_data.file_info.rows * analysis_data.file_info.columns %}
                                                                {% set total_missing = 0 %}
                                                                {% for col_data in analysis_data.analysis.column_analysis.values() %}
                                                                    {% set total_missing = total_missing + col_data.missing_count %}
                                                                {% endfor %}
                                                                {% set completeness = ((total_cells - total_missing) / total_cells * 100) %}
                                                            {% endif %}

                                                            {% if completeness < 95 %}
                                                            <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>التركيز على تحسين جودة البيانات</li>
                                                            {% else %}
                                                            <li><i class="fas fa-thumbs-up text-success me-2"></i>جودة البيانات ممتازة</li>
                                                            {% endif %}

                                                            {% if analysis_data.detected_sector %}
                                                            <li><i class="fas fa-lightbulb text-info me-2"></i>تخصيص التحليل حسب قطاع {{ analysis_data.detected_sector }}</li>
                                                            {% endif %}

                                                            <li><i class="fas fa-chart-bar text-primary me-2"></i>الاستفادة من الرسوم البيانية التفاعلية</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Analysis Data for JavaScript -->
    <script id="analysisData" type="application/json">
        {{ analysis_data | tojson if analysis_data else '{}' }}
    </script>

    <!-- Make analysis data available globally for debugging -->
    <script>
        // Parse analysis data for debugging
        const analysisDataElement = document.getElementById('analysisData');
        if (analysisDataElement) {
            try {
                window.analysisData = JSON.parse(analysisDataElement.textContent);
            } catch (e) {
                console.error('Error parsing analysis data:', e);
                window.analysisData = {};
            }
        } else {
            window.analysisData = {};
        }
    </script>

    <!-- Merge Analysis Data for JavaScript -->
    <script id="mergeAnalysisData" type="application/json">
        {% if analysis_data and analysis_data.analysis and analysis_data.analysis.sector_specific and analysis_data.analysis.sector_specific.get('merge_analysis') %}
            {{ analysis_data.analysis.sector_specific.merge_analysis.merge_candidates | tojson }}
        {% else %}
            []
        {% endif %}
    </script>

    <script>
        // Interactive Charts Variables and Functions
        let interactiveCharts = {};

        // Color schemes for interactive charts
        const colorSchemes = {
            default: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'],
            vibrant: ['#FF3366', '#33CCFF', '#FFCC33', '#33FF99', '#CC33FF', '#FF6633', '#3366FF', '#FFFF33', '#FF33CC', '#33FFCC'],
            pastel: ['#FFB3BA', '#BAFFC9', '#BAE1FF', '#FFFFBA', '#FFDFBA', '#E0BBE4', '#957DAD', '#D291BC', '#FEC8D8', '#FFDFD3'],
            dark: ['#2C3E50', '#34495E', '#7F8C8D', '#95A5A6', '#BDC3C7', '#ECF0F1', '#E74C3C', '#E67E22', '#F39C12', '#F1C40F'],
            gradient: ['rgba(255, 99, 132, 0.8)', 'rgba(54, 162, 235, 0.8)', 'rgba(255, 206, 86, 0.8)', 'rgba(75, 192, 192, 0.8)', 'rgba(153, 102, 255, 0.8)']
        };

        function initializeInteractiveCharts() {
            console.log('🎨 Initializing interactive column charts...');

            // Get data from script tag
            const analysisDataElement = document.getElementById('analysisData');
            if (!analysisDataElement) {
                console.error('❌ Analysis data element not found');
                showNoInteractiveDataMessage();
                return;
            }

            const analysisData = JSON.parse(analysisDataElement.textContent);
            const distributionsData = analysisData.analysis && analysisData.analysis.distributions ? analysisData.analysis.distributions : {};
            const columnAnalysisData = analysisData.analysis && analysisData.analysis.column_analysis ? analysisData.analysis.column_analysis : {};

            if (distributionsData && Object.keys(distributionsData).length > 0) {
                generateInteractiveCharts(distributionsData, columnAnalysisData);
            } else {
                showNoInteractiveDataMessage();
            }
        }

        function generateInteractiveCharts(distributionsData, columnAnalysisData) {
            const container = document.getElementById('interactiveChartsContainer');
            if (!container) return;

            container.innerHTML = '';

            Object.keys(distributionsData).forEach((columnName, index) => {
                const columnData = distributionsData[columnName];
                const analysisData = columnAnalysisData[columnName] || {};

                const chartHTML = `
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="chart-card h-100">
                            <div class="card-header bg-light border-0 py-3">
                                <h6 class="mb-0 text-center fw-bold text-dark">
                                    <i class="fas fa-chart-line me-2 text-primary"></i>
                                    ${columnName}
                                </h6>
                                <div class="text-center mt-2">
                                    <span class="badge bg-primary me-1">${Object.keys(columnData).length} قيمة فريدة</span>
                                    <span class="badge bg-secondary">${analysisData.type || 'نص'}</span>
                                </div>
                            </div>
                            <div class="card-body p-3">
                                <div class="chart-container">
                                    <canvas id="interactiveChart_${index}"></canvas>
                                </div>

                                <!-- Column Stats -->
                                <div class="mt-3 p-2 bg-light rounded">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <small class="text-muted d-block">القيم الفريدة</small>
                                            <strong class="text-primary">${Object.keys(columnData).length}</strong>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted d-block">المفقودة</small>
                                            <strong class="text-danger">${analysisData.missing_count || 0}</strong>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted d-block">النوع</small>
                                            <strong class="text-success">${analysisData.type || 'نص'}</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                container.insertAdjacentHTML('beforeend', chartHTML);

                // Create the chart
                setTimeout(() => {
                    createInteractiveChart(`interactiveChart_${index}`, columnData, columnName);
                }, 100);
            });
        }

        function createInteractiveChart(canvasId, data, title) {
            const canvas = document.getElementById(canvasId);
            if (!canvas) return;

            const ctx = canvas.getContext('2d');

            // Prepare data
            const entries = Object.entries(data);
            const maxValues = parseInt(document.getElementById('maxValues')?.value || 10);
            const sortedEntries = entries.sort((a, b) => b[1] - a[1]).slice(0, maxValues);

            const labels = sortedEntries.map(entry => String(entry[0]));
            const values = sortedEntries.map(entry => entry[1]);

            const selectedScheme = document.getElementById('colorScheme')?.value || 'default';
            const colors = colorSchemes[selectedScheme] || colorSchemes.default;

            const chartType = document.getElementById('chartType')?.value || 'bar';

            // Destroy existing chart if exists
            if (interactiveCharts[canvasId]) {
                interactiveCharts[canvasId].destroy();
            }

            // Create new chart
            interactiveCharts[canvasId] = new Chart(ctx, {
                type: chartType === 'horizontalBar' ? 'bar' : chartType,
                data: {
                    labels: labels,
                    datasets: [{
                        label: title,
                        data: values,
                        backgroundColor: colors.slice(0, values.length),
                        borderColor: colors.slice(0, values.length),
                        borderWidth: 2,
                        borderRadius: chartType === 'bar' || chartType === 'horizontalBar' ? 8 : 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: chartType === 'horizontalBar' ? 'y' : 'x',
                    plugins: {
                        legend: {
                            display: chartType === 'pie' || chartType === 'doughnut' || chartType === 'radar'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const total = values.reduce((a, b) => a + b, 0);
                                    const percentage = ((context.parsed.y || context.parsed) / total * 100).toFixed(1);
                                    return `${context.label}: ${context.parsed.y || context.parsed} (${percentage}%)`;
                                }
                            }
                        }
                    },
                    scales: chartType === 'pie' || chartType === 'doughnut' || chartType === 'radar' ? {} : {
                        y: {
                            beginAtZero: true
                        }
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeInOutQuart'
                    }
                }
            });
        }

        function updateAllCharts() {
            const analysisDataElement = document.getElementById('analysisData');
            if (!analysisDataElement) return;

            const analysisData = JSON.parse(analysisDataElement.textContent);
            const distributionsData = analysisData.analysis && analysisData.analysis.distributions ? analysisData.analysis.distributions : {};

            if (distributionsData && Object.keys(distributionsData).length > 0) {
                Object.keys(distributionsData).forEach((columnName, index) => {
                    const columnData = distributionsData[columnName];
                    createInteractiveChart(`interactiveChart_${index}`, columnData, columnName);
                });
            }
        }

        function showNoInteractiveDataMessage() {
            const container = document.getElementById('interactiveChartsContainer');
            if (container) {
                container.innerHTML = `
                    <div class="col-12">
                        <div class="alert alert-warning text-center">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>لا توجد بيانات متاحة للرسوم البيانية التفاعلية</strong>
                            <p class="mb-0 mt-2">يرجى التأكد من رفع ملف بيانات صحيح وإجراء التحليل أولاً.</p>
                        </div>
                    </div>
                `;
            }
        }

        // Export functions
        function exportChartsPNG() {
            alert('🖼️ سيتم تصدير الرسوم البيانية التفاعلية كصور PNG');
        }

        function exportChartsPDF() {
            alert('📄 سيتم إنشاء تقرير PDF شامل للرسوم البيانية التفاعلية');
        }

        function printCharts() {
            alert('🖨️ سيتم طباعة الرسوم البيانية التفاعلية');
        }

        function shareCharts() {
            alert('📤 سيتم مشاركة الرسوم البيانية التفاعلية');
        }

        function exportAllCharts() {
            alert('💾 سيتم تصدير جميع الرسوم البيانية التفاعلية');
        }

        // Merge Analysis Pagination and Export Functions
        let mergeCurrentPage = 1;
        let mergePageSize = 25;
        let mergeTotalItems = 0;
        let mergeAllData = [];

        function initializeMergePagination() {
            // Load data from script tag
            const mergeDataElement = document.getElementById('mergeAnalysisData');
            if (mergeDataElement) {
                mergeAllData = JSON.parse(mergeDataElement.textContent);
                mergeTotalItems = mergeAllData.length;

                // Update total count display
                const totalCountElement = document.getElementById('mergeTotalCount');
                if (totalCountElement) {
                    totalCountElement.textContent = `من أصل ${mergeTotalItems}`;
                }
            }

            if (mergeTotalItems > 0) {
                updateMergeTable();
                updateMergePagination();
            }
        }

        function changeMergePageSize() {
            mergePageSize = parseInt(document.getElementById('mergePageSize').value);
            mergeCurrentPage = 1;
            updateMergeTable();
            updateMergePagination();
        }

        function updateMergeTable() {
            const tbody = document.getElementById('mergeTableBody');
            tbody.innerHTML = '';

            // Show all data (220 records)
            const allData = mergeAllData;

            // Apply pagination to all candidates
            const startIndex = (mergeCurrentPage - 1) * mergePageSize;
            const endIndex = startIndex + mergePageSize;
            const pageData = allData.slice(startIndex, endIndex);

            // Update total count for pagination
            mergeTotalItems = allData.length;
            const totalCountElement = document.getElementById('mergeTotalCount');
            if (totalCountElement) {
                totalCountElement.textContent = `من أصل ${mergeTotalItems}`;
            }

            pageData.forEach(candidate => {
                // Separate numbers by type
                const insuranceNumbers = [];
                const automaticNumbers = [];
                const manualNumbers = [];

                candidate.numbers.forEach((number, index) => {
                    const type = candidate.number_types && candidate.number_types[index] ? candidate.number_types[index] : 'غير محدد';
                    if (type === 'تأميني') {
                        insuranceNumbers.push(number);
                    } else if (type === 'آلي' || type === 'عام') {
                        automaticNumbers.push(number);
                    } else if (type === 'يدوي') {
                        manualNumbers.push(number);
                    }
                });

                const allBasicNumbers = [...insuranceNumbers, ...automaticNumbers];

                // Extract birth date
                const birthDateMatch = candidate.person_key.match(/\d{4}-\d{2}-\d{2}/);
                const birthDate = birthDateMatch ? birthDateMatch[0] : 'غير محدد';

                // Determine note
                let note = '';
                if (insuranceNumbers.length > 1) {
                    note = 'تضارب أرقام أساسية';
                } else if (manualNumbers.length > 0 && allBasicNumbers.length > 0) {
                    note = 'أرقام مرتبطة';
                } else {
                    note = 'يحتاج مراجعة';
                }

                const row = document.createElement('tr');

                // Full Name (الاسم الكامل)
                const fullNameCell = document.createElement('td');
                fullNameCell.innerHTML = `<div class="fw-bold">${candidate.person_key}</div>`;
                row.appendChild(fullNameCell);

                // Birth Date (تاريخ الميلاد)
                const birthDateCell = document.createElement('td');
                birthDateCell.textContent = birthDate;
                row.appendChild(birthDateCell);

                // Basic Numbers (الأرقام الأساسية)
                const basicNumbersCell = document.createElement('td');
                basicNumbersCell.className = 'text-center';
                basicNumbersCell.innerHTML = allBasicNumbers.join('<br>') || '';
                row.appendChild(basicNumbersCell);

                // Manual Numbers (الأرقام اليدوية)
                const manualNumbersCell = document.createElement('td');
                manualNumbersCell.className = 'text-center';
                manualNumbersCell.innerHTML = manualNumbers.join('<br>') || '';
                row.appendChild(manualNumbersCell);

                // Basic Numbers Count (عدد الأرقام الأساسية)
                const basicCountCell = document.createElement('td');
                basicCountCell.className = 'text-center';
                basicCountCell.innerHTML = `<span class="badge bg-primary">${allBasicNumbers.length}</span>`;
                row.appendChild(basicCountCell);

                // Manual Numbers Count (عدد الأرقام اليدوية)
                const manualCountCell = document.createElement('td');
                manualCountCell.className = 'text-center';
                manualCountCell.innerHTML = `<span class="badge bg-info">${manualNumbers.length}</span>`;
                row.appendChild(manualCountCell);

                // Note (الملاحظة)
                const noteCell = document.createElement('td');
                noteCell.className = 'text-center';
                let noteClass = 'badge-warning';
                let emoji = '⚠️';

                if (note.includes('تضارب')) {
                    noteClass = 'badge-danger';
                    emoji = '🚨';
                } else if (note.includes('مرتبطة')) {
                    noteClass = 'badge-success';
                    emoji = '✅';
                } else {
                    noteClass = 'badge-secondary';
                    emoji = '❓';
                }
                noteCell.innerHTML = `<span class="badge ${noteClass}">${emoji} ${note}</span>`;
                row.appendChild(noteCell);

                tbody.appendChild(row);
            });
        }

        // PersonRecord-like functionality in JavaScript
        function createPersonRecordFromCandidate(candidate) {
            // Extract birth date from person_key
            const birthDateMatch = candidate.person_key.match(/\d{4}-\d{2}-\d{2}/);
            const birthDate = birthDateMatch ? birthDateMatch[0] : 'غير محدد';

            // Separate numbers by type
            const mainNumbers = [];
            const manualNumbers = [];

            candidate.numbers.forEach((number, index) => {
                const type = candidate.number_types && candidate.number_types[index] ? candidate.number_types[index] : 'غير محدد';
                if (type === 'تأميني' || type === 'آلي' || type === 'عام') {
                    mainNumbers.push(number);
                } else if (type === 'يدوي') {
                    manualNumbers.push(number);
                }
            });

            return {
                full_name: candidate.person_key,
                birth_date: birthDate,
                main_numbers: mainNumbers,
                manual_numbers: manualNumbers,
                main_numbers_count: mainNumbers.length,
                manual_numbers_count: manualNumbers.length,
                get_note: function() {
                    if (this.main_numbers_count > 1) {
                        return 'تضارب أرقام أساسية';
                    } else if (this.main_numbers_count === 1 && this.manual_numbers_count > 1) {
                        return 'يحتاج دمج';
                    } else if (this.main_numbers_count === 1 && this.manual_numbers_count === 1) {
                        return 'طبيعي';
                    } else {
                        return 'يحتاج مراجعة';
                    }
                }
            };
        }

        function updateMergePagination() {
            const totalPages = Math.ceil(mergeTotalItems / mergePageSize);
            const pagination = document.getElementById('mergePagination');
            pagination.innerHTML = '';

            // Previous button
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${mergeCurrentPage === 1 ? 'disabled' : ''}`;
            prevLi.innerHTML = `
                <a class="page-link" href="#" onclick="changeMergePage(${mergeCurrentPage - 1})" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            `;
            pagination.appendChild(prevLi);

            // Page numbers
            const startPage = Math.max(1, mergeCurrentPage - 2);
            const endPage = Math.min(totalPages, mergeCurrentPage + 2);

            if (startPage > 1) {
                const firstLi = document.createElement('li');
                firstLi.className = 'page-item';
                firstLi.innerHTML = `<a class="page-link" href="#" onclick="changeMergePage(1)">1</a>`;
                pagination.appendChild(firstLi);

                if (startPage > 2) {
                    const ellipsisLi = document.createElement('li');
                    ellipsisLi.className = 'page-item disabled';
                    ellipsisLi.innerHTML = `<span class="page-link">...</span>`;
                    pagination.appendChild(ellipsisLi);
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const pageLi = document.createElement('li');
                pageLi.className = `page-item ${i === mergeCurrentPage ? 'active' : ''}`;
                pageLi.innerHTML = `<a class="page-link" href="#" onclick="changeMergePage(${i})">${i}</a>`;
                pagination.appendChild(pageLi);
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    const ellipsisLi = document.createElement('li');
                    ellipsisLi.className = 'page-item disabled';
                    ellipsisLi.innerHTML = `<span class="page-link">...</span>`;
                    pagination.appendChild(ellipsisLi);
                }

                const lastLi = document.createElement('li');
                lastLi.className = 'page-item';
                lastLi.innerHTML = `<a class="page-link" href="#" onclick="changeMergePage(${totalPages})">${totalPages}</a>`;
                pagination.appendChild(lastLi);
            }

            // Next button
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${mergeCurrentPage === totalPages ? 'disabled' : ''}`;
            nextLi.innerHTML = `
                <a class="page-link" href="#" onclick="changeMergePage(${mergeCurrentPage + 1})" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            `;
            pagination.appendChild(nextLi);
        }

        function changeMergePage(page) {
            if (page < 1 || page > Math.ceil(mergeTotalItems / mergePageSize)) return;
            mergeCurrentPage = page;
            updateMergeTable();
            updateMergePagination();
        }

        function exportMergeAnalysisToCSV() {
            const headers = ['الاسم الكامل', 'تاريخ الميلاد', 'الأرقام الأساسية (تأميني/آلي)', 'الأرقام اليدوية المرتبطة', 'عدد الأرقام الأساسية', 'عدد الأرقام اليدوية', 'الملاحظة'];
            let csvContent = '\uFEFF'; // UTF-8 BOM for proper Arabic support in Excel
            csvContent += headers.join(',') + '\n';

            mergeAllData.forEach(candidate => {
                // Separate numbers by type
                const insuranceNumbers = [];
                const automaticNumbers = [];
                const manualNumbers = [];

                candidate.numbers.forEach((number, index) => {
                    const type = candidate.number_types && candidate.number_types[index] ? candidate.number_types[index] : 'غير محدد';
                    if (type === 'تأميني') {
                        insuranceNumbers.push(number);
                    } else if (type === 'آلي' || type === 'عام') {
                        automaticNumbers.push(number);
                    } else if (type === 'يدوي') {
                        manualNumbers.push(number);
                    }
                });

                const allBasicNumbers = [...insuranceNumbers, ...automaticNumbers];

                // Extract birth date
                const birthDateMatch = candidate.person_key.match(/\d{4}-\d{2}-\d{2}/);
                const birthDate = birthDateMatch ? birthDateMatch[0] : 'غير محدد';

                // Determine note
                let note = '';
                if (insuranceNumbers.length > 1) {
                    note = 'تضارب أرقام أساسية';
                } else if (manualNumbers.length > 0 && allBasicNumbers.length > 0) {
                    note = 'أرقام مرتبطة';
                } else {
                    note = 'يحتاج مراجعة';
                }

                const row = [
                    `"${candidate.person_key}"`,
                    `"${birthDate}"`,
                    `"${allBasicNumbers.join(' – ')}"`,
                    `"${manualNumbers.join(' – ')}"`,
                    `"${allBasicNumbers.length}"`,
                    `"${manualNumbers.length}"`,
                    `"${note}"`
                ];
                csvContent += row.join(',') + '\n';
            });

            downloadFile(csvContent, 'merge_analysis.csv', 'text/csv;charset=utf-8');
        }

        function exportMergeAnalysisToExcel() {
            // For Excel, we'll create a CSV with UTF-8 BOM that Excel can properly read
            const headers = ['الاسم الكامل', 'تاريخ الميلاد', 'الأرقام الأساسية (تأميني/آلي)', 'الأرقام اليدوية المرتبطة', 'عدد الأرقام الأساسية', 'عدد الأرقام اليدوية', 'الملاحظة'];
            let csvContent = '\uFEFF'; // UTF-8 BOM for proper Arabic support in Excel
            csvContent += headers.join('\t') + '\n'; // Use tabs for better Excel compatibility

            mergeAllData.forEach(candidate => {
                // Separate numbers by type
                const insuranceNumbers = [];
                const automaticNumbers = [];
                const manualNumbers = [];

                candidate.numbers.forEach((number, index) => {
                    const type = candidate.number_types && candidate.number_types[index] ? candidate.number_types[index] : 'غير محدد';
                    if (type === 'تأميني') {
                        insuranceNumbers.push(number);
                    } else if (type === 'آلي' || type === 'عام') {
                        automaticNumbers.push(number);
                    } else if (type === 'يدوي') {
                        manualNumbers.push(number);
                    }
                });

                const allBasicNumbers = [...insuranceNumbers, ...automaticNumbers];

                // Extract birth date
                const birthDateMatch = candidate.person_key.match(/\d{4}-\d{2}-\d{2}/);
                const birthDate = birthDateMatch ? birthDateMatch[0] : 'غير محدد';

                // Determine note
                let note = '';
                if (insuranceNumbers.length > 1) {
                    note = 'تضارب أرقام أساسية';
                } else if (manualNumbers.length > 0 && allBasicNumbers.length > 0) {
                    note = 'أرقام مرتبطة';
                } else {
                    note = 'يحتاج مراجعة';
                }

                const row = [
                    candidate.person_key,
                    birthDate,
                    allBasicNumbers.join(' – '),
                    manualNumbers.join(' – '),
                    allBasicNumbers.length,
                    manualNumbers.length,
                    note
                ];
                csvContent += row.join('\t') + '\n';
            });

            // Create blob with UTF-8 encoding
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = 'merge_analysis.csv'; // Keep .csv extension for Excel compatibility
            link.click();
        }

        function exportMergeAnalysisToPDF() {
            // Create HTML content for PDF
            let htmlContent = `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>تحليل الدمج - الأشخاص ذوي الأرقام المتعددة</title>
                    <style>
                        body {
                            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                            direction: rtl;
                            margin: 20px;
                        }
                        table {
                            width: 100%;
                            border-collapse: collapse;
                            margin-top: 20px;
                        }
                        th, td {
                            border: 1px solid #ddd;
                            padding: 8px;
                            text-align: center;
                        }
                        th {
                            background-color: #f2f2f2;
                            font-weight: bold;
                        }
                        .header {
                            text-align: center;
                            margin-bottom: 20px;
                        }
                        .header h1 {
                            color: #333;
                            margin: 0;
                        }
                        .header p {
                            color: #666;
                            margin: 5px 0;
                        }
                        .badge {
                            display: inline-block;
                            padding: 3px 8px;
                            border-radius: 12px;
                            font-size: 12px;
                            font-weight: bold;
                        }
                        .badge-success {
                            background-color: #28a745;
                            color: white;
                        }
                        .badge-primary {
                            background-color: #007bff;
                            color: white;
                        }
                        .badge-info {
                            background-color: #17a2b8;
                            color: white;
                        }
                        .badge-warning {
                            background-color: #ffc107;
                            color: black;
                        }
                        @media print {
                            body { margin: 0; }
                            .no-print { display: none; }
                        }
                    </style>
                </head>
                <body>

                    <table>
                        <thead>
                            <tr>
                                <th>الاسم الكامل</th>
                                <th>تاريخ الميلاد</th>
                                <th>الأرقام الأساسية (تأميني/آلي)</th>
                                <th>الأرقام اليدوية المرتبطة</th>
                                <th>عدد الأرقام الأساسية</th>
                                <th>عدد الأرقام اليدوية</th>
                                <th>الملاحظة</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            // Add table rows in PersonRecord format
            const allRows = [];
            mergeAllData.forEach(candidate => {
                const personRecord = createPersonRecordFromCandidate(candidate);

                // If person has multiple manual numbers, create separate row for each
                if (personRecord.manual_numbers.length > 0) {
                    personRecord.manual_numbers.forEach((manualNumber, index) => {
                        allRows.push({
                            full_name: personRecord.full_name,
                            birth_date: personRecord.birth_date,
                            insurance_number: personRecord.main_numbers.length > 0 ? personRecord.main_numbers[0] : '',
                            manual_number: manualNumber,
                            note: personRecord.get_note()
                        });
                    });
                } else {
                    // If no manual numbers, create one row
                    allRows.push({
                        full_name: personRecord.full_name,
                        birth_date: personRecord.birth_date,
                        insurance_number: personRecord.main_numbers.length > 0 ? personRecord.main_numbers[0] : '',
                        manual_number: '',
                        note: personRecord.get_note()
                    });
                }
            });

            allRows.forEach((rowData, index) => {
                // Determine note class for PDF
                const note = rowData.note;
                let noteClass = 'badge-warning';
                if (note.includes('تضارب')) {
                    noteClass = 'badge-danger';
                } else if (note.includes('يحتاج دمج')) {
                    noteClass = 'badge-warning';
                } else if (note.includes('طبيعي')) {
                    noteClass = 'badge-success';
                } else {
                    noteClass = 'badge-secondary';
                }

                htmlContent += `
                    <tr>
                        <td>${rowData.full_name}</td>
                        <td>${rowData.birth_date}</td>
                        <td>${rowData.insurance_number || ''}</td>
                        <td>${rowData.manual_number || ''}</td>
                        <td><span class="badge ${noteClass}">${note}</span></td>
                    </tr>
                `;
            });

            htmlContent += `
                        </tbody>
                    </table>

                    <div class="no-print" style="margin-top: 20px; text-align: center;">
                        <button onclick="window.print()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            طباعة التقرير
                        </button>
                    </div>
                </body>
                </html>
            `;

            // Open in new window for printing/saving as PDF
            const printWindow = window.open('', '_blank');
            printWindow.document.write(htmlContent);
            printWindow.document.close();

            // Auto-print after a short delay
            setTimeout(() => {
                printWindow.focus();
                printWindow.print();
            }, 500);
        }

        function downloadFile(content, filename, mimeType) {
            const blob = new Blob([content], { type: mimeType + ';charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = filename;
            link.click();
        }

        // Helper function to create Excel-compatible CSV with proper Arabic encoding
        function createExcelCSV() {
            const headers = ['الاسم الكامل', 'تاريخ الميلاد', 'الأرقام الأساسية (تأميني/آلي)', 'الأرقام اليدوية المرتبطة', 'عدد الأرقام الأساسية', 'عدد الأرقام اليدوية', 'الملاحظة'];
            let csvContent = '\uFEFF'; // UTF-8 BOM
            csvContent += headers.join('\t') + '\n';

            mergeAllData.forEach(candidate => {
                // Separate numbers by type
                const insuranceNumbers = [];
                const automaticNumbers = [];
                const manualNumbers = [];

                candidate.numbers.forEach((number, index) => {
                    const type = candidate.number_types && candidate.number_types[index] ? candidate.number_types[index] : 'غير محدد';
                    if (type === 'تأميني') {
                        insuranceNumbers.push(number);
                    } else if (type === 'آلي' || type === 'عام') {
                        automaticNumbers.push(number);
                    } else if (type === 'يدوي') {
                        manualNumbers.push(number);
                    }
                });

                const allBasicNumbers = [...insuranceNumbers, ...automaticNumbers];

                // Extract birth date
                const birthDateMatch = candidate.person_key.match(/\d{4}-\d{2}-\d{2}/);
                const birthDate = birthDateMatch ? birthDateMatch[0] : 'غير محدد';

                // Determine note
                let note = '';
                if (insuranceNumbers.length > 1) {
                    note = 'تضارب أرقام أساسية';
                } else if (manualNumbers.length > 0 && allBasicNumbers.length > 0) {
                    note = 'أرقام مرتبطة';
                } else {
                    note = 'يحتاج مراجعة';
                }

                const row = [
                    candidate.person_key,
                    birthDate,
                    allBasicNumbers.join(' – '),
                    manualNumbers.join(' – '),
                    allBasicNumbers.length.toString(),
                    manualNumbers.length.toString(),
                    note
                ];
                csvContent += row.join('\t') + '\n';
            });

            return csvContent;
        }

        // Table sorting functionality
        let sortDirection = {};

        // Merge table sorting functionality
        let mergeSortDirection = {};

        function sortTable(columnIndex) {
            const table = document.getElementById('columnsTable');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            const headers = table.querySelectorAll('th.sortable');

            // Reset all sortable headers
            const allSortableHeaders = table.querySelectorAll('th.sortable');
            allSortableHeaders.forEach(header => {
                header.classList.remove('sort-asc', 'sort-desc');
            });

            // Set sort direction
            if (!sortDirection[columnIndex]) {
                sortDirection[columnIndex] = 'asc';
            } else if (sortDirection[columnIndex] === 'asc') {
                sortDirection[columnIndex] = 'desc';
            } else {
                sortDirection[columnIndex] = 'asc';
            }

            // Add sort class to current header
            headers[columnIndex].classList.add(`sort-${sortDirection[columnIndex]}`);

            // Sort rows
            rows.sort((a, b) => {
                const aValue = a.cells[columnIndex].textContent.trim();
                const bValue = b.cells[columnIndex].textContent.trim();

                let aNum, bNum;

                // Try to parse as numbers
                if (columnIndex >= 2 && columnIndex <= 5) { // Numeric columns (2-5)
                    aNum = parseFloat(aValue.replace(/,/g, '').replace('%', ''));
                    bNum = parseFloat(bValue.replace(/,/g, '').replace('%', ''));

                    if (!isNaN(aNum) && !isNaN(bNum)) {
                        return sortDirection[columnIndex] === 'asc' ? aNum - bNum : bNum - aNum;
                    }
                }

                // String comparison
                if (sortDirection[columnIndex] === 'asc') {
                    return aValue.localeCompare(bValue, 'ar');
                } else {
                    return bValue.localeCompare(aValue, 'ar');
                }
            });

            // Reorder rows
            rows.forEach(row => tbody.appendChild(row));
        }

        function sortMergeTable(columnIndex) {
            const table = document.getElementById('mergeAnalysisTable');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            const headers = table.querySelectorAll('th.sortable');

            // Reset all headers
            headers.forEach(header => {
                header.classList.remove('sort-asc', 'sort-desc');
            });

            // Set sort direction
            if (!mergeSortDirection[columnIndex]) {
                mergeSortDirection[columnIndex] = 'asc';
            } else if (mergeSortDirection[columnIndex] === 'asc') {
                mergeSortDirection[columnIndex] = 'desc';
            } else {
                mergeSortDirection[columnIndex] = 'asc';
            }

            // Add sort class to current header
            const currentHeader = headers[columnIndex];
            if (currentHeader) {
                currentHeader.classList.add(`sort-${mergeSortDirection[columnIndex]}`);
            }

            // Sort rows based on column content
            rows.sort((a, b) => {
                const aValue = a.cells[columnIndex].textContent.trim().toLowerCase();
                const bValue = b.cells[columnIndex].textContent.trim().toLowerCase();

                // Handle empty cells
                if (aValue === 'لا يوجد' && bValue === 'لا يوجد') return 0;
                if (aValue === 'لا يوجد') return 1;
                if (bValue === 'لا يوجد') return -1;

                // Handle person key column (text sorting)
                if (columnIndex === 0) { // Full Name column
                    if (mergeSortDirection[columnIndex] === 'asc') {
                        return aValue.localeCompare(bValue, 'ar');
                    } else {
                        return bValue.localeCompare(aValue, 'ar');
                    }
                }

                // Handle birth date column
                if (columnIndex === 1) { // Birth Date column
                    if (mergeSortDirection[columnIndex] === 'asc') {
                        return aValue.localeCompare(bValue);
                    } else {
                        return bValue.localeCompare(aValue);
                    }
                }

                // Handle numbers columns (basic and manual)
                if (columnIndex === 2 || columnIndex === 3) { // Basic Numbers and Manual Numbers columns
                    // Extract first number from the dash-separated list
                    const aFirst = aValue.split(' – ')[0] || '';
                    const bFirst = bValue.split(' – ')[0] || '';

                    if (mergeSortDirection[columnIndex] === 'asc') {
                        return aFirst.localeCompare(bFirst);
                    } else {
                        return bFirst.localeCompare(aFirst);
                    }
                }

                // Handle count columns (numeric)
                if (columnIndex === 4 || columnIndex === 5) { // Count columns
                    const aNum = parseInt(aValue.replace(/\D/g, '')) || 0;
                    const bNum = parseInt(bValue.replace(/\D/g, '')) || 0;

                    if (mergeSortDirection[columnIndex] === 'asc') {
                        return aNum - bNum;
                    } else {
                        return bNum - aNum;
                    }
                }

                // Handle note column (text sorting)
                if (columnIndex === 6) { // Note column
                    if (mergeSortDirection[columnIndex] === 'asc') {
                        return aValue.localeCompare(bValue, 'ar');
                    } else {
                        return bValue.localeCompare(aValue, 'ar');
                    }
                }

                // Default string comparison
                if (mergeSortDirection[columnIndex] === 'asc') {
                    return aValue.localeCompare(bValue, 'ar');
                } else {
                    return bValue.localeCompare(aValue, 'ar');
                }
            });

            // Reorder rows
            rows.forEach(row => tbody.appendChild(row));
        }

        // Export functions
        function exportTableToCSV() {
            const table = document.getElementById('columnsTable');
            let csv = [];

            // Get headers
            const headers = [];
            table.querySelectorAll('thead th').forEach(th => {
                headers.push(th.textContent.replace('⇅', '').trim());
            });
            csv.push(headers.join(','));

            // Get data
            table.querySelectorAll('tbody tr').forEach(tr => {
                const row = [];
                tr.querySelectorAll('td').forEach(td => {
                    row.push('"' + td.textContent.trim() + '"');
                });
                csv.push(row.join(','));
            });

            // Download
            const csvContent = csv.join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = 'column_analysis.csv';
            link.click();
        }

        function exportTableToExcel() {
            alert('⚠️ تصدير Excel قيد التطوير - استخدم تصدير CSV مؤقتاً');
        }

        function exportTableToPDF() {
            alert('⚠️ تصدير PDF قيد التطوير - استخدم تصدير CSV مؤقتاً');
        }

        // Initialize charts for distributions
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Page loaded, initializing charts...');

            // Initialize table sorting
            const sortableHeaders = document.querySelectorAll('.sortable');
            sortableHeaders.forEach((header, index) => {
                header.addEventListener('click', () => sortTable(index));
            });

            // Initialize distribution charts
            const analysisDataElement = document.getElementById('analysisData');
            if (analysisDataElement) {
                const analysisData = JSON.parse(analysisDataElement.textContent);
                const distributions = analysisData.analysis && analysisData.analysis.distributions ? analysisData.analysis.distributions : {};

                Object.keys(distributions).forEach((columnName, index) => {
                    const chartId = `chart-${index + 1}`;
                    const canvas = document.getElementById(chartId);

                    if (canvas) {
                        const ctx = canvas.getContext('2d');
                        const data = distributions[columnName];

                        // Prepare data for chart
                        const entries = Object.entries(data);
                        const sortedEntries = entries.sort((a, b) => b[1] - a[1]).slice(0, 15);

                        const labels = sortedEntries.map(entry => String(entry[0]));
                        const values = sortedEntries.map(entry => entry[1]);

                        // Create chart
                        new Chart(ctx, {
                            type: 'bar',
                            data: {
                                labels: labels,
                                datasets: [{
                                    label: columnName,
                                    data: values,
                                    backgroundColor: 'rgba(102, 126, 234, 0.8)',
                                    borderColor: 'rgba(102, 126, 234, 1)',
                                    borderWidth: 2,
                                    borderRadius: 8
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        display: false
                                    },
                                    tooltip: {
                                        callbacks: {
                                            label: function(context) {
                                                const total = values.reduce((a, b) => a + b, 0);
                                                const percentage = (context.parsed.y / total * 100).toFixed(1);
                                                return `${context.label}: ${context.parsed.y} (${percentage}%)`;
                                            }
                                        }
                                    }
                                },
                                scales: {
                                    y: {
                                        beginAtZero: true
                                    }
                                },
                                animation: {
                                    duration: 1000,
                                    easing: 'easeInOutQuart'
                                }
                            }
                        });
                    }
                });
            }

            // Initialize interactive charts
            setTimeout(() => {
                if (typeof Chart !== 'undefined') {
                    initializeInteractiveCharts();
                } else {
                    console.error('❌ Chart.js not loaded');
                }
            }, 1000);

            // Initialize merge analysis pagination
            initializeMergePagination();
    
            // Initialize PersonRecord table
            initializePersonRecordTable();
    
            // Initialize progress bars
            initializeProgressBars();
        });

        // Initialize progress bars with data attributes
        function initializeProgressBars() {
            console.log('Initializing progress bars...');
            const progressBars = document.querySelectorAll('.progress-bar[data-percentage]');

            progressBars.forEach(bar => {
                const percentage = parseFloat(bar.getAttribute('data-percentage')) || 0;
                const clampedPercentage = Math.max(0, Math.min(100, percentage)); // Ensure between 0-100
                bar.style.width = clampedPercentage + '%';
                bar.setAttribute('aria-valuenow', clampedPercentage);
            });

            console.log('Progress bars initialized:', progressBars.length, 'bars');
        }

        // PersonRecord Table Functions
        function initializePersonRecordTable() {
            console.log('Initializing PersonRecord table...');

            // Try to get data from mergeAnalysisData script tag first
            let mergeData = null;
            const mergeDataElement = document.getElementById('mergeAnalysisData');
            if (mergeDataElement) {
                try {
                    mergeData = JSON.parse(mergeDataElement.textContent);
                    console.log('Merge data from script tag:', mergeData ? mergeData.length : 'null');
                } catch (e) {
                    console.error('Error parsing merge data from script tag:', e);
                }
            }

            // If no data from script tag, try to get from global analysisData
            if ((!mergeData || mergeData.length === 0) && window.analysisData) {
                console.log('Trying to get merge data from global analysisData...');
                if (window.analysisData.analysis && window.analysisData.analysis.sector_specific && window.analysisData.analysis.sector_specific.merge_analysis) {
                    mergeData = window.analysisData.analysis.sector_specific.merge_analysis.merge_candidates || [];
                    console.log('Merge data from global analysisData:', mergeData.length);
                }
            }

            console.log('Final merge data length:', mergeData ? mergeData.length : 'null');

            if (!mergeData || mergeData.length === 0) {
                console.log('No merge data available');
                // Show message in table
                const tbody = document.querySelector("#personTable tbody");
                if (tbody) {
                    tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">لا توجد بيانات مرشحين للدمج متاحة</td></tr>';
                }

                // Show debug info if available
                const debugInfoElement = document.getElementById('mergeDebugInfo');
                if (debugInfoElement && window.analysisData && window.analysisData.analysis && window.analysisData.analysis.sector_specific && window.analysisData.analysis.sector_specific.merge_analysis) {
                    const mergeAnalysis = window.analysisData.analysis.sector_specific.merge_analysis;
                    debugInfoElement.innerHTML = `
                        <div class="alert alert-info mt-3">
                            <h6>معلومات التصحيح - تحليل الدمج:</h6>
                            <p><strong>هل البيانات موجودة؟</strong> ${mergeAnalysis ? 'نعم' : 'لا'}</p>
                            <p><strong>عدد المرشحين:</strong> ${mergeAnalysis.total_candidates || 0}</p>
                            <p><strong>عدد المرشحين في القائمة:</strong> ${mergeAnalysis.merge_candidates ? mergeAnalysis.merge_candidates.length : 0}</p>
                            <p><strong>مفاتيح البيانات:</strong> ${Object.keys(mergeAnalysis).join(', ')}</p>
                        </div>
                    `;
                    debugInfoElement.style.display = 'block';
                }
                return;
            }

            populatePersonRecordTable(mergeData); // Show all records
            console.log('PersonRecord table populated with', mergeData.length, 'records');
        }

        function populatePersonRecordTable(data) {
            console.log('Populating table with data:', data.length, 'records');
            const tbody = document.querySelector("#personTable tbody");
            if (!tbody) {
                console.error('Table body not found');
                return;
            }
            tbody.innerHTML = '';

            // Show total count
            const totalCount = data.length;
            console.log('Total records to display:', totalCount);

            // Remove any existing info div
            const existingInfo = tbody.parentNode.querySelector('.table-info');
            if (existingInfo) {
                existingInfo.remove();
            }

            const infoDiv = document.createElement("div");
            infoDiv.className = 'table-info';
            infoDiv.innerHTML = `<p style="text-align: center; margin-bottom: 20px; font-weight: bold; color: #007bff;">إجمالي السجلات: ${totalCount}</p>`;
            tbody.parentNode.insertBefore(infoDiv, tbody);

            data.forEach((candidate, index) => {
                if (index < 5) { // Log first 5 records for debugging
                    console.log('Processing record', index + 1, ':', candidate.person_key);
                }

                // Separate numbers by type
                const insuranceNumbers = [];
                const automaticNumbers = [];
                const manualNumbers = [];

                if (candidate.numbers && Array.isArray(candidate.numbers)) {
                    candidate.numbers.forEach((number, numIndex) => {
                        const type = candidate.number_types && candidate.number_types[numIndex] ? candidate.number_types[numIndex] : 'غير محدد';
                        if (type === 'تأميني') {
                            insuranceNumbers.push(number);
                        } else if (type === 'آلي' || type === 'عام') {
                            automaticNumbers.push(number);
                        } else if (type === 'يدوي') {
                            manualNumbers.push(number);
                        }
                    });
                }

                const allBasicNumbers = [...insuranceNumbers, ...automaticNumbers];

                // Extract birth date
                const birthDateMatch = candidate.person_key ? candidate.person_key.match(/\d{4}-\d{2}-\d{2}/) : null;
                const birthDate = birthDateMatch ? birthDateMatch[0] : 'غير محدد';

                // Determine note
                let note = '';
                let noteClass = 'note-review';
                if (insuranceNumbers.length > 1) {
                    note = '🚨 تضارب أرقام أساسية';
                    noteClass = 'note-conflict';
                } else if (manualNumbers.length > 0 && allBasicNumbers.length > 0) {
                    note = '⚠️ يحتاج دمج';
                    noteClass = 'note-merge';
                } else if (allBasicNumbers.length === 1 && manualNumbers.length === 1) {
                    note = '✅ طبيعي';
                    noteClass = 'note-normal';
                } else {
                    note = '❓ يحتاج مراجعة';
                    noteClass = 'note-review';
                }

                const row = document.createElement("tr");
                row.innerHTML = `
                    <td>${candidate.person_key || 'غير محدد'}</td>
                    <td>${birthDate}</td>
                    <td>${allBasicNumbers.length > 0 ? allBasicNumbers.join(' – ') : 'لا يوجد'}</td>
                    <td>${manualNumbers.length > 0 ? manualNumbers.join(' – ') : 'لا يوجد'}</td>
                    <td>${allBasicNumbers.length}</td>
                    <td>${manualNumbers.length}</td>
                    <td class="${noteClass}">${note}</td>
                `;
                tbody.appendChild(row);
            });

            console.log('Table populated successfully with', data.length, 'records');
        }
    </script>
</body>
</html>

                        <!-- Merge Analysis for Insurance Data -->
                {% if analysis_data.analysis.sector_specific and analysis_data.analysis.sector_specific.get('merge_analysis') and analysis_data.analysis.sector_specific.merge_analysis.total_candidates > 0 %}
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-users-cog me-2"></i>
                            تحليل الدمج - الأشخاص ذوي الأرقام المتعددة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>تم العثور على {{ analysis_data.analysis.sector_specific.get('merge_analysis', {}).get('total_candidates', 0) }} مرشح للدمج</strong>
                            <br>
                            <small>أشخاص لديهم نفس البيانات الأساسية ولكن أرقام تأمينية/يدوية مختلفة</small>
                        </div>

                        <!-- Export Controls -->
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="exportMergeAnalysisToCSV()">
                                    <i class="fas fa-file-csv me-1"></i>
                                    تصدير CSV
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-success" onclick="exportMergeAnalysisToExcel()">
                                    <i class="fas fa-file-excel me-1"></i>
                                    تصدير Excel
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="exportMergeAnalysisToPDF()">
                                    <i class="fas fa-file-pdf me-1"></i>
                                    تصدير PDF
                                </button>
                            </div>
                            <div class="d-flex align-items-center">
                                <label class="form-label me-2 mb-0">عرض:</label>
                                <select id="mergePageSize" class="form-select form-select-sm" style="width: auto;" onchange="changeMergePageSize()">
                                    <option value="10">10</option>
                                    <option value="25" selected>25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                                <span class="ms-2 text-muted" id="mergeTotalCount">من أصل 0</span>
                            </div>
                        </div>

                        <!-- جدول PersonRecord ديناميكي -->
                        <table id="personTable">
                            <thead>
                                <tr>
                                    <th>الاسم الكامل</th>
                                    <th>تاريخ الميلاد</th>
                                    <th>الأرقام الأساسية</th>
                                    <th>الأرقام اليدوية</th>
                                    <th>عدد الأرقام الأساسية</th>
                                    <th>عدد الأرقام اليدوية</th>
                                    <th>الملاحظة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم تعبئة الجدول تلقائيًا -->
                            </tbody>
                        </table>

                        <!-- Debug Info Container -->
                        <div id="mergeDebugInfo" style="display: block;">
                            <div class="alert alert-info mt-3">
                                <h6>معلومات التصحيح - تحليل الدمج:</h6>
                                <p><strong>هل البيانات موجودة؟</strong> {{ (analysis_data.analysis.sector_specific.get('merge_analysis') is not none) | string }}</p>
                                <p><strong>عدد المرشحين:</strong> {{ analysis_data.analysis.sector_specific.get('merge_analysis', {}).get('total_candidates', 0) }}</p>
                                <p><strong>عدد المرشحين في القائمة:</strong> {{ analysis_data.analysis.sector_specific.get('merge_analysis', {}).get('merge_candidates', [])|length }}</p>
                                <p><strong>مفاتيح البيانات:</strong> {{ analysis_data.analysis.sector_specific.keys() | list if analysis_data.analysis.sector_specific else 'لا توجد بيانات' }}</p>
                            </div>
                        </div>

                        <div class="mt-3">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="p-3 bg-primary bg-opacity-10 rounded">
                                        <div class="fw-bold text-primary h4 mb-1">{{ analysis_data.analysis.sector_specific.get('merge_analysis', {}).get('total_candidates', 0) }}</div>
                                        <small class="text-muted">إجمالي المرشحين</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="p-3 bg-success bg-opacity-10 rounded">
                                        <div class="fw-bold text-success h4 mb-1">{{ analysis_data.analysis.sector_specific.get('merge_analysis', {}).get('analyzed_persons', 0) }}</div>
                                        <small class="text-muted">الأشخاص المحللين</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="p-3 bg-info bg-opacity-10 rounded">
                                        <div class="fw-bold text-info h4 mb-1">{{ analysis_data.analysis.sector_specific.get('merge_analysis', {}).get('person_columns_found', [])|length }}</div>
                                        <small class="text-muted">أعمدة الشخص المكتشفة</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="p-3 bg-warning bg-opacity-10 rounded">
                                        <div class="fw-bold text-warning h4 mb-1">{{ analysis_data.analysis.sector_specific.get('merge_analysis', {}).get('number_columns_found', [])|length }}</div>
                                        <small class="text-muted">أعمدة الأرقام المكتشفة</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% else %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            لم يتم العثور على أشخاص يحتاجون دمج أرقامهم
                        </div>
                        {% endif %}
                    </div>
                </div>

                        <!-- PersonRecord Data Table -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-users me-2"></i>
                                    بيانات PersonRecord - أمثلة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover table-sky">
                                        <thead>
                                            <tr>
                                                <th>الاسم الكامل</th>
                                                <th>تاريخ الميلاد</th>
                                                <th>الأرقام الأساسية</th>
                                                <th>الأرقام اليدوية</th>
                                                <th>عدد الأرقام الأساسية</th>
                                                <th>عدد الأرقام اليدوية</th>
                                                <th>الملاحظة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>محمد عمر محمد ناصر</td>
                                                <td>1981-06-11</td>
                                                <td>295924 – 287748</td>
                                                <td>1956 – 264529 – 293893 – 246540</td>
                                                <td><span class="badge bg-primary">2</span></td>
                                                <td><span class="badge bg-info">4</span></td>
                                                <td><span class="badge bg-danger">🚨 تضارب أرقام أساسية</span></td>
                                            </tr>
                                            <tr>
                                                <td>أحمد محمد علي</td>
                                                <td>1990-01-01</td>
                                                <td>123456</td>
                                                <td>111111</td>
                                                <td><span class="badge bg-primary">1</span></td>
                                                <td><span class="badge bg-info">1</span></td>
                                                <td><span class="badge bg-success">✅ طبيعي</span></td>
                                            </tr>
                                            <tr>
                                                <td>فاطمة سالم حسن</td>
                                                <td>1985-05-05</td>
                                                <td>234567</td>
                                                <td>222222 – 333333 – 444444</td>
                                                <td><span class="badge bg-primary">1</span></td>
                                                <td><span class="badge bg-info">3</span></td>
                                                <td><span class="badge bg-warning">⚠️ يحتاج دمج</span></td>
                                            </tr>
                                            <tr>
                                                <td>علي أحمد محمد</td>
                                                <td>1975-10-10</td>
                                                <td>345678 – 456789 – 567890</td>
                                                <td>555555</td>
                                                <td><span class="badge bg-primary">3</span></td>
                                                <td><span class="badge bg-info">1</span></td>
                                                <td><span class="badge bg-danger">🚨 تضارب أرقام أساسية</span></td>
                                            </tr>
                                            <tr>
                                                <td>سارة يوسف أحمد</td>
                                                <td>1995-12-12</td>
                                                <td>لا يوجد</td>
                                                <td>666666 – 777777</td>
                                                <td><span class="badge bg-primary">0</span></td>
                                                <td><span class="badge bg-info">2</span></td>
                                                <td><span class="badge bg-secondary">❓ يحتاج مراجعة</span></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="mt-3">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>شرح الجدول:</strong>
                                        <ul class="mb-0 mt-2">
                                            <li><strong>الأرقام الأساسية:</strong> الأرقام التأمينية أو الآلية (بدون نصوص)</li>
                                            <li><strong>الأرقام اليدوية:</strong> الأرقام اليدوية المرتبطة (بدون نصوص)</li>
                                            <li><strong>الملاحظة:</strong> تحديد حالة السجل بناءً على عدد الأرقام</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Column Analysis -->
                        {% if analysis_data.analysis and analysis_data.analysis.column_analysis %}
                        <div class="card mb-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-table me-2"></i>
                                    تحليل الأعمدة
                                </h5>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="exportTableToCSV()">
                                        <i class="fas fa-file-csv me-1"></i>
                                        تصدير CSV
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="exportTableToExcel()">
                                        <i class="fas fa-file-excel me-1"></i>
                                        تصدير Excel
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="exportTableToPDF()">
                                        <i class="fas fa-file-pdf me-1"></i>
                                        تصدير PDF
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table id="columnsTable" class="table table-striped table-hover table-sky">
                                        <thead>
                                            <tr>
                                                <th class="sortable">
                                                    اسم العمود
                                                    <i class="fas fa-sort sort-icon"></i>
                                                </th>
                                                <th class="sortable">
                                                    النوع
                                                    <i class="fas fa-sort sort-icon"></i>
                                                </th>
                                                <th class="sortable">
                                                    القيم الفريدة
                                                    <i class="fas fa-sort sort-icon"></i>
                                                </th>
                                                <th class="sortable">
                                                    القيم المفقودة
                                                    <i class="fas fa-sort sort-icon"></i>
                                                </th>
                                                <th class="sortable">
                                                    نسبة المفقود
                                                    <i class="fas fa-sort sort-icon"></i>
                                                </th>
                                                <th class="sortable">
                                                    مكرر مرتين
                                                    <i class="fas fa-sort sort-icon"></i>
                                                </th>
                                                <th class="sortable">
                                                    مكرر 3+ مرات
                                                    <i class="fas fa-sort sort-icon"></i>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for col_name, col_data in analysis_data.analysis.column_analysis.items() %}
                                            {% set dup_data = analysis_data.column_duplicates.get(col_name, {'duplicates_2': 0, 'duplicates_3_plus': 0}) %}
                                            <tr>
                                                <td class="fw-bold">
                                                    <strong>{{ col_name }}</strong>
                                                    <br><small class="text-muted">({{ col_name }})</small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary">
                                                        <i class="fas fa-font me-1"></i>
                                                        {{ col_data.type }}
                                                    </span>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge bg-info">
                                                        {{ "{:,}".format(col_data.unique_values) }}
                                                    </span>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge bg-warning text-dark">
                                                        {{ "{:,}".format(col_data.missing_count) }}
                                                    </span>
                                                    <br><small class="text-muted">Debug missing: {{ col_data.missing_count }}</small>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="progress flex-grow-1 me-2" style="height: 20px;">
                                                            <div class="progress-bar bg-danger" role="progressbar" data-percentage="{{ col_data.missing_percentage }}" aria-valuenow="{{ col_data.missing_percentage }}" aria-valuemin="0" aria-valuemax="100" style="width: 0%;">
                                                            </div>
                                                        </div>
                                                        <span class="badge bg-light text-dark">
                                                            {{ "%.1f"|format(col_data.missing_percentage) }}%
                                                        </span>
                                                    </div>
                                                    <small class="text-muted">Debug: {{ col_data.missing_percentage }}</small>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge bg-warning text-dark">
                                                        {{ "{:,}".format(dup_data.duplicates_2) }}
                                                    </span>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge bg-warning text-dark">
                                                        {{ "{:,}".format(dup_data.duplicates_3_plus) }}
                                                    </span>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Distributions -->
                        {% if analysis_data.analysis and analysis_data.analysis.distributions %}
                        <div class="distributions-section">
                            <div class="text-center mb-4">
                                <h4 class="mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    توزيعات البيانات
                                </h4>
                                <p class="text-muted mt-2">عرض توزيع القيم في كل عمود بشكل بصري</p>
                            </div>
                            <div class="row">
                                {% for col_name, distribution in analysis_data.analysis.distributions.items() %}
                                <div class="col-12 mb-5">
                                    <div class="card chart-card">
                                        <div class="card-header chart-header">
                                            <h5 class="mb-0 text-center">
                                                <i class="fas fa-chart-pie me-2"></i>
                                                {{ col_name }}
                                            </h5>
                                        </div>
                                        <div class="card-body">
                                            <!-- Chart Canvas - Full Width -->
                                            <div class="chart-wrapper-large mb-4">
                                                <canvas id="chart-{{ loop.index }}" class="chart-canvas-large"></canvas>
                                            </div>
                                        </div>

                                        <!-- Distribution Table and Stats in separate row -->
                                        <div class="row mt-3">
                                            <div class="col-md-8">
                                                <!-- Distribution Table -->
                                                <div class="table-responsive">
                                                    <table class="table table-sm table-striped table-hover distribution-table">
                                                        <thead>
                                                            <tr>
                                                                <th class="fw-bold">القيمة</th>
                                                                <th class="fw-bold text-center">العدد</th>
                                                                <th class="fw-bold text-center">النسبة (%)</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {% set total = distribution.values() | sum %}
                                                            {% for value, count in distribution.items() | sort(attribute='1', reverse=true) | list %}
                                                            {% if loop.index <= 15 %}
                                                            <tr>
                                                                <td class="text-truncate fw-medium" style="max-width: 250px;" title="{{ value }}">
                                                                    {% if value|length > 30 %}
                                                                        {{ value[:27] }}...
                                                                    {% else %}
                                                                        {{ value }}
                                                                    {% endif %}
                                                                </td>
                                                                <td class="text-center">
                                                                    <span class="badge bg-primary fs-6 px-3 py-1">{{ "{:,}".format(count) }}</span>
                                                                </td>
                                                                <td class="text-center">
                                                                    <span class="badge bg-success fs-6 px-3 py-1">{{ "%.1f"|format((count / total) * 100) }}</span>
                                                                </td>
                                                            </tr>
                                                            {% endif %}
                                                            {% endfor %}
                                                            {% if distribution|length > 15 %}
                                                            <tr class="table-light">
                                                                <td colspan="3" class="text-center text-muted small">
                                                                    و {{ distribution|length - 15 }} قيم أخرى...
                                                                </td>
                                                            </tr>
                                                            {% endif %}
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>

                                            <div class="col-md-4">
                                                <!-- Summary Statistics -->
                                                <div class="stats-summary-large">
                                                    <h6 class="text-center mb-3 fw-bold">إحصائيات العمود</h6>
                                                    <div class="row text-center">
                                                        <div class="col-12 mb-3">
                                                            <div class="p-3 bg-primary bg-opacity-10 rounded">
                                                                <div class="fw-bold text-primary h4 mb-1">{{ "{:,}".format(total) }}</div>
                                                                <small class="text-muted">إجمالي القيم</small>
                                                            </div>
                                                        </div>
                                                        <div class="col-12 mb-3">
                                                            <div class="p-3 bg-success bg-opacity-10 rounded">
                                                                <div class="fw-bold text-success h4 mb-1">{{ distribution|length }}</div>
                                                                <small class="text-muted">قيم فريدة</small>
                                                            </div>
                                                        </div>
                                                        <div class="col-12">
                                                            <div class="p-3 bg-info bg-opacity-10 rounded">
                                                                <div class="fw-bold text-info h4 mb-1">
                                                                    {% set max_count = distribution.values() | list | sort(reverse=true) | first %}
                                                                    {{ "%.1f"|format((max_count / total) * 100) }}%
                                                                </div>
                                                                <small class="text-muted">أكبر نسبة</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
