<!DOCTYPE html>
<html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>نتائج التحليل - نظام تحليل البيانات</title>
        <!-- Bootstrap CSS -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- Font Awesome -->
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <!-- Chart.js -->
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background-color: #63a0dd;
            }

            .card {
                border: none;
                border-radius: 15px;
                box-shadow: 0 4px 6px rgba(252, 248, 248, 0.1);
                margin-bottom: 20px;
            }

            .card-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: rgb(248, 242, 242);
                border-radius: 15px 15px 0 0 !important;
                border: none;
            }

            /* Interactive Charts Styles */
            .interactive-charts-section {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: none;
                border-radius: 20px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                margin: 30px 0;
            }

            .chart-controls {
                background: rgba(255,255,255,0.1);
                border-radius: 15px;
                padding: 20px;
                margin-bottom: 30px;
            }

            .chart-controls .form-select {
                background: rgba(255,255,255,0.1);
                border: 1px solid rgba(255,255,255,0.3);
                color: white;
            }

            .chart-controls .form-select option {
                background: #667eea;
                color: white;
            }

            .chart-container {
                background: white;
                border-radius: 15px;
                padding: 10px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                height: 350px;
            }

            .chart-card {
                background: white;
                border-radius: 15px;
                overflow: hidden;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                transition: transform 0.3s ease;
            }

            .chart-card:hover {
                transform: translateY(-5px);
            }

            .btn-outline-light {
                border: 2px solid rgba(255,255,255,0.5);
                color: white;
            }

            .btn-outline-light:hover {
                background: rgba(255,255,255,0.2);
                border-color: white;
                color: white;
            }

            .stat-card {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-radius: 15px;
                padding: 20px;
                text-align: center;
                margin-bottom: 20px;
            }

            .table-responsive {
                border-radius: 10px;
                overflow: hidden;
            }

            .table th {
                background-color: #f8f9fa;
                border: none;
                font-weight: 600;
            }

            .table td {
                border: none;
                border-bottom: 1px solid #dee2e6;
            }

            .badge {
                font-size: 0.8rem;
            }

            .alert {
                border: none;
                border-radius: 10px;
            }

            .navbar {
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .progress {
                height: 8px;
                border-radius: 4px;
            }

            .chart-wrapper {
                position: relative;
                height: 400px;
                margin-bottom: 20px;
            }

            .chart-canvas {
                max-height: 400px;
            }

            .distribution-table {
                font-size: 0.9rem;
            }

            .distribution-table th {
                background-color: #e9ecef;
                font-weight: 600;
                text-align: center;
            }

            .distribution-table td {
                vertical-align: middle;
            }

            .stats-summary {
                background-color: #f8f9fa;
                border-radius: 10px;
                padding: 15px;
            }

            .chart-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
            }

            .chart-card {
                border: none;
                border-radius: 15px;
                overflow: hidden;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                margin-bottom: 30px;
            }

            .chart-wrapper-large {
                position: relative;
                height: 500px;
                margin-bottom: 20px;
            }

            .chart-canvas-large {
                max-height: 500px;
            }

            .stats-summary-large {
                background-color: #f8f9fa;
                border-radius: 15px;
                padding: 20px;
                height: 100%;
            }

            .bg-gradient {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            }

            .text-gradient {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }

            .btn-gradient {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: none;
                color: white;
            }

            .btn-gradient:hover {
                background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
                color: white;
            }

            .distributions-section {
                margin-top: 30px;
            }

            .distributions-section .card {
                margin-bottom: 40px;
            }

            .distributions-section .chart-wrapper-large {
                height: 450px;
            }

            .distributions-section .table {
                margin-bottom: 0;
            }

            .distributions-section .stats-summary-large {
                display: flex;
                flex-direction: column;
                justify-content: center;
            }

            /* Table Sky Theme */
            .table-sky {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-radius: 10px;
                overflow: hidden;
            }

            .table-sky thead th {
                background: rgba(255, 255, 255, 0.1);
                border: none;
                color: white;
                font-weight: 600;
                text-align: center;
                vertical-align: middle;
                padding: 15px 10px;
            }

            .table-sky tbody td {
                background: rgba(255, 255, 255, 0.05);
                border: none;
                color: white;
                text-align: center;
                vertical-align: middle;
                padding: 12px 10px;
            }

            .table-sky tbody tr:hover td {
                background: rgba(255, 255, 255, 0.1);
            }

            .table-sky .badge {
                background: rgba(255, 255, 255, 0.2) !important;
                color: white !important;
                border: 1px solid rgba(255, 255, 255, 0.3);
            }

            /* Sortable Headers */
            .sortable {
                cursor: pointer;
                user-select: none;
                position: relative;
            }

            .sortable:hover {
                background: rgba(255, 255, 255, 0.15) !important;
            }

            .sort-icon {
                margin-left: 5px;
                opacity: 0.5;
                transition: opacity 0.3s ease;
            }

            .sortable:hover .sort-icon {
                opacity: 1;
            }

            .sortable.sort-asc .sort-icon:before {
                content: '\f145';
                /* fa-sort-up */
            }

            .sortable.sort-desc .sort-icon:before {
                content: '\f144';
                /* fa-sort-down */
            }

            /* Merge Table Sortable Headers */
            #mergeAnalysisTable .sortable {
                background: rgba(255, 255, 255, 0.1);
                transition: all 0.3s ease;
                cursor: pointer;
                user-select: none;
            }

            #mergeAnalysisTable .sortable:hover {
                background: rgba(255, 255, 255, 0.2) !important;
                transform: translateY(-1px);
            }

            #mergeAnalysisTable .sort-icon {
                margin-right: 5px;
                margin-left: 0;
                font-size: 0.8em;
                opacity: 0.5;
                transition: opacity 0.3s ease;
            }

            #mergeAnalysisTable .sortable:hover .sort-icon {
                opacity: 1;
            }

            #mergeAnalysisTable .sortable.sort-asc .sort-icon:before {
                content: '\f145';
                /* fa-sort-up */
            }

            #mergeAnalysisTable .sortable.sort-desc .sort-icon:before {
                content: '\f144';
                /* fa-sort-down */
            }

            /* Person Key Column Styling */
            #mergeAnalysisTable tbody td:first-child {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white !important;
                font-weight: 600;
                text-shadow: 0 1px 2px rgba(0,0,0,0.3);
                border: none;
                position: relative;
                overflow: hidden;
            }

            #mergeAnalysisTable tbody td:first-child::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                transition: left 0.5s;
            }

            #mergeAnalysisTable tbody tr:hover td:first-child::before {
                left: 100%;
            }

            #mergeAnalysisTable tbody td:first-child small {
                color: rgba(255,255,255,0.8) !important;
                font-weight: 400;
                text-shadow: none;
            }

            #mergeAnalysisTable tbody td:first-child:hover {
                background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
                transform: scale(1.02);
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                z-index: 10;
            }

            /* Enhanced table styling for merge table */
            #mergeAnalysisTable.table-sky tbody td:first-child {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                color: white !important;
            }

            #mergeAnalysisTable.table-sky tbody tr:hover td:first-child {
                background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
            }

            /* PersonRecord Table Styles */
            #personTable {
                width: 100%;
                border-collapse: collapse;
                text-align: center;
                margin-top: 20px;
            }

            #personTable th, #personTable td {
                border: 1px solid #333;
                padding: 8px;
            }

            #personTable th {
                background-color: #f2f2f2;
            }

            .note-normal {
                color: green;
                font-weight: bold;
            }

            .note-merge {
                color: orange;
                font-weight: bold;
            }

            .note-conflict {
                color: red;
                font-weight: bold;
            }

            .note-review {
                color: blue;
                font-weight: bold;
            }

            /* Enhanced PersonRecord Table Styles */
            #personTable {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
                font-size: 14px;
                background-color: #ffffff;
                box-shadow: 0 4px 8px rgba(0,0,0,0.15);
                border-radius: 8px;
                overflow: hidden;
            }

            #personTable th, #personTable td {
                padding: 16px 12px;
                text-align: center;
                border: 1px solid #dee2e6;
                font-weight: 600;
            }

            #personTable th {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                font-weight: bold;
                position: sticky;
                top: 0;
                z-index: 10;
                transition: all 0.3s ease;
            }

            #personTable th:hover {
                background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
                color: #667eea !important;
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                border: 2px solid #667eea !important;
            }

            #personTable tbody tr:nth-child(even) {
                background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 25%, #d1ecf1 50%, #e6f3ff 75%, #f0f8ff 100%);
                border-bottom: 1px solid #b8daff;
