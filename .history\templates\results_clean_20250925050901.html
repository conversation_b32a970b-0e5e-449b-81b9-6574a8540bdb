<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نتائج التحليل - نظام تحليل البيانات</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #63a0dd;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(252, 248, 248, 0.1);
            margin-bottom: 20px;
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: rgb(248, 242, 242);
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }

        /* Interactive Charts Styles */
        .interactive-charts-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin: 30px 0;
        }

        .chart-controls {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .chart-controls .form-select {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
        }

        .chart-controls .form-select option {
            background: #667eea;
            color: white;
        }

        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            height: 350px;
        }

        .chart-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .chart-card:hover {
            transform: translateY(-5px);
        }

        .btn-outline-light {
            border: 2px solid rgba(255,255,255,0.5);
            color: white;
        }

        .btn-outline-light:hover {
            background: rgba(255,255,255,0.2);
            border-color: white;
            color: white;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }

        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }

        .table th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
        }

        .table td {
            border: none;
            border-bottom: 1px solid #dee2e6;
        }

        .badge {
            font-size: 0.8rem;
        }

        .alert {
            border: none;
            border-radius: 10px;
        }

        .navbar {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .progress {
            height: 8px;
            border-radius: 4px;
        }

        .chart-wrapper {
            position: relative;
            height: 400px;
            margin-bottom: 20px;
        }

        .chart-canvas {
            max-height: 400px;
        }

        .distribution-table {
            font-size: 0.9rem;
        }

        .distribution-table th {
            background-color: #e9ecef;
            font-weight: 600;
            text-align: center;
        }

        .distribution-table td {
            vertical-align: middle;
        }

        .stats-summary {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
        }

        .chart-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .chart-card {
            border: none;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .chart-wrapper-large {
            position: relative;
            height: 500px;
            margin-bottom: 20px;
        }

        .chart-canvas-large {
            max-height: 500px;
        }

        .stats-summary-large {
            background-color: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            height: 100%;
        }

        .bg-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        }

        .text-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
        }

        .btn-gradient:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            color: white;
        }

        .distributions-section {
            margin-top: 30px;
        }

        .distributions-section .card {
            margin-bottom: 40px;
        }

        .distributions-section .chart-wrapper-large {
            height: 450px;
        }

        .distributions-section .table {
            margin-bottom: 0;
        }

        .distributions-section .stats-summary-large {
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        /* Table Sky Theme */
        .table-sky {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            overflow: hidden;
        }

        .table-sky thead th {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: white;
            font-weight: 600;
            text-align: center;
            vertical-align: middle;
            padding: 15px 10px;
        }

        .table-sky tbody td {
            background: rgba(255, 255, 255, 0.05);
            border: none;
            color: white;
            text-align: center;
            vertical-align: middle;
            padding: 12px 10px;
        }

        .table-sky tbody tr:hover td {
            background: rgba(255, 255, 255, 0.1);
        }

        .table-sky .badge {
            background: rgba(255, 255, 255, 0.2) !important;
            color: white !important;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        /* Sortable Headers */
        .sortable {
            cursor: pointer;
            user-select: none;
            position: relative;
        }

        .sortable:hover {
            background: rgba(255, 255, 255, 0.15) !important;
        }

        .sort-icon {
            margin-left: 5px;
            opacity: 0.5;
            transition: opacity 0.3s ease;
        }

        .sortable:hover .sort-icon {
            opacity: 1;
        }

        .sortable.sort-asc .sort-icon:before {
            content: '\f145'; /* fa-sort-up */
        }

        .sortable.sort-desc .sort-icon:before {
            content: '\f144'; /* fa-sort-down */
        }

        /* Merge Table Sortable Headers */
        #mergeAnalysisTable .sortable {
            background: rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            user-select: none;
        }

        #mergeAnalysisTable .sortable:hover {
            background: rgba(255, 255, 255, 0.2) !important;
            transform: translateY(-1px);
        }

        #mergeAnalysisTable .sort-icon {
            margin-right: 5px;
            margin-left: 0;
            font-size: 0.8em;
            opacity: 0.5;
            transition: opacity 0.3s ease;
        }

        #mergeAnalysisTable .sortable:hover .sort-icon {
            opacity: 1;
        }

        #mergeAnalysisTable .sortable.sort-asc .sort-icon:before {
            content: '\f145'; /* fa-sort-up */
        }

        #mergeAnalysisTable .sortable.sort-desc .sort-icon:before {
            content: '\f144'; /* fa-sort-down */
        }

        /* Person Key Column Styling */
        #mergeAnalysisTable tbody td:first-child {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white !important;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
            border: none;
            position: relative;
            overflow: hidden;
        }

        #mergeAnalysisTable tbody td:first-child::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        #mergeAnalysisTable tbody tr:hover td:first-child::before {
            left: 100%;
        }

        #mergeAnalysisTable tbody td:first-child small {
            color: rgba(255,255,255,0.8) !important;
            font-weight: 400;
            text-shadow: none;
        }

        #mergeAnalysisTable tbody td:first-child:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
            transform: scale(1.02);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            z-index: 10;
        }

        /* Enhanced table styling for merge table */
        #mergeAnalysisTable.table-sky tbody td:first-child {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
        }

        #mergeAnalysisTable.table-sky tbody tr:hover td:first-child {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
        }

        /* PersonRecord Table Styles */
        #personTable {
            width: 100%;
            border-collapse: collapse;
            text-align: center;
            margin-top: 20px;
        }

        #personTable th, #personTable td {
            border: 1px solid #333;
            padding: 8px;
        }

        #personTable th {
            background-color: #f2f2f2;
        }

        .note-normal {
            color: green;
            font-weight: bold;
        }

        .note-merge {
            color: orange;
            font-weight: bold;
        }

        .note-conflict {
            color: red;
            font-weight: bold;
        }

        .note-review {
            color: blue;
            font-weight: bold;
        }

        /* Enhanced PersonRecord Table Styles */
        #personTable {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 14px;
            background-color: #ffffff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            border-radius: 8px;
            overflow: hidden;
        }

        #personTable th,
        #personTable td {
            padding: 16px 12px;
            text-align: center;
            border: 1px solid #dee2e6;
            font-weight: 600;
        }

        #personTable th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
            transition: all 0.3s ease;
        }

        #personTable th:hover {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
            color: #667eea !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            border: 2px solid #667eea !important;
        }

        #personTable tbody tr:nth-child(even) {
            background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 25%, #d1ecf1 50%, #e6f3ff 75%, #f0f8ff 100%);
            border-bottom: 1px solid #b8daff;
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.8), 0 1px 3px rgba(0, 123, 255, 0.08);
            border-left: 3px solid #cce7ff;
        }

        #personTable tbody tr:nth-child(odd) {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 20%, #f0f4ff 40%, #e8f0ff 60%, #f0f4ff 80%, #ffffff 100%);
            border-bottom: 1px solid #d4edda;
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.9), 0 1px 3px rgba(40, 167, 69, 0.06);
            border-left: 3px solid #d1ecf1;
        }

        #personTable tbody tr:hover {
            background: linear-gradient(135deg, #e8f4fd 0%, #b3e5fc 30%, #81d4fa 60%, #4fc3f7 80%, #e8f4fd 100%);
            cursor: pointer;
            transform: translateY(-3px) scale(1.008);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 25px rgba(33, 150, 243, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.6);
            border-left: 6px solid #1976d2;
            border-radius: 0 12px 12px 0;
            z-index: 5;
        }

        /* Special styling for conflict rows - VIBRANT CRIMSON RED with enhanced glow */
        #personTable tbody tr:has(.merge-reason:contains("🚨")) {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 25%, #ef9a9a 50%, #e57373 75%, #ffebee 100%);
            border-left: 8px solid #d32f2f;
            box-shadow: 0 4px 15px rgba(211, 47, 47, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
            position: relative;
            border-radius: 0 12px 12px 0;
            animation: pulseConflict 3s infinite ease-in-out;
        }

        #personTable tbody tr:has(.merge-reason:contains("🚨")):hover {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 30%, #ef5350 60%, #f44336 80%, #ffebee 100%);
            border-left: 10px solid #b71c1c;
            box-shadow: 0 8px 25px rgba(211, 47, 47, 0.35), inset 0 1px 0 rgba(255, 255, 255, 0.4);
            transform: translateY(-5px) scale(1.012);
            z-index: 15;
        }

        /* Special styling for merge needed rows - WARM AMBER ORANGE with golden glow */
        #personTable tbody tr:has(.merge-reason:contains("⚠️")) {
            background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 25%, #ffe082 50%, #ffd54f 75%, #fff8e1 100%);
            border-left: 8px solid #f57c00;
            box-shadow: 0 4px 15px rgba(245, 124, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
            border-radius: 0 12px 12px 0;
            animation: pulseWarning 4s infinite ease-in-out;
        }

        #personTable tbody tr:has(.merge-reason:contains("⚠️")):hover {
            background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 30%, #ffe082 60%, #ffb74d 80%, #fff8e1 100%);
            border-left: 10px solid #ef6c00;
            box-shadow: 0 8px 25px rgba(245, 124, 0, 0.35), inset 0 1px 0 rgba(255, 255, 255, 0.4);
            transform: translateY(-5px) scale(1.012);
            z-index: 15;
        }

        /* Special styling for normal rows - FRESH EMERALD GREEN with minty glow */
        #personTable tbody tr:has(.merge-reason:contains("✅")) {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 25%, #a5d6a7 50%, #81c784 75%, #e8f5e8 100%);
            border-left: 8px solid #2e7d32;
            box-shadow: 0 4px 15px rgba(46, 125, 50, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
            border-radius: 0 12px 12px 0;
            animation: pulseSuccess 5s infinite ease-in-out;
        }

        #personTable tbody tr:has(.merge-reason:contains("✅")):hover {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 30%, #a5d6a7 60%, #66bb6a 80%, #e8f5e8 100%);
            border-left: 10px solid #1b5e20;
            box-shadow: 0 8px 25px rgba(46, 125, 50, 0.35), inset 0 1px 0 rgba(255, 255, 255, 0.4);
            transform: translateY(-5px) scale(1.012);
            z-index: 15;
        }

        /* Special styling for review needed rows - MYSTICAL INDIGO PURPLE with ethereal glow */
        #personTable tbody tr:has(.merge-reason:contains("❓")) {
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 25%, #ce93d8 50%, #ba68c8 75%, #f3e5f5 100%);
            border-left: 8px solid #7b1fa2;
            box-shadow: 0 4px 15px rgba(123, 31, 162, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
            border-radius: 0 12px 12px 0;
            animation: pulseReview 6s infinite ease-in-out;
        }

        #personTable tbody tr:has(.merge-reason:contains("❓")):hover {
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 30%, #ce93d8 60%, #ab47bc 80%, #f3e5f5 100%);
            border-left: 10px solid #6a1b9a;
            box-shadow: 0 8px 25px rgba(123, 31, 162, 0.35), inset 0 1px 0 rgba(255, 255, 255, 0.4);
            transform: translateY(-5px) scale(1.012);
            z-index: 15;
        }

        #personTable .person-name {
            font-weight: 700;
            color: #2c3e50;
            max-width: 200px;
            word-wrap: break-word;
            background: #f8f9fa;
            padding: 10px 14px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
            transition: all 0.2s ease;
            font-size: 14px;
            line-height: 1.4;
        }

        #personTable .insurance-numbers,
        #personTable .manual-numbers {
            font-family: 'Courier New', monospace;
            background: #fff3cd;
            padding: 8px 12px;
            border-radius: 6px;
            color: #856404;
            border: 1px solid #ffeaa7;
            font-weight: 600;
            transition: all 0.2s ease;
            font-size: 13px;
            line-height: 1.4;
            word-spacing: 3px;
            letter-spacing: 0.5px;
        }

        #personTable .merge-reason {
            padding: 10px 14px;
            border-radius: 8px;
            font-weight: 700;
            border: 2px solid;
            text-transform: uppercase;
            font-size: 12px;
            letter-spacing: 0.5px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            text-shadow: 0 1px 1px rgba(0,0,0,0.1);
            line-height: 1.3;
            word-wrap: break-word;
        }

        /* Specific styling for each merge reason type */
        #personTable tbody tr:has(.merge-reason:contains("🚨")) .merge-reason {
            background: linear-gradient(135deg, #ff4757 0%, #ff3838 50%, #ff4757 100%);
            color: white;
            border-color: #ff3838;
            box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3);
        }

        #personTable tbody tr:has(.merge-reason:contains("⚠️")) .merge-reason {
            background: linear-gradient(135deg, #ffa726 0%, #fb8c00 50%, #ffa726 100%);
            color: white;
            border-color: #fb8c00;
            box-shadow: 0 2px 8px rgba(255, 167, 38, 0.3);
        }

        #personTable tbody tr:has(.merge-reason:contains("✅")) .merge-reason {
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 50%, #4caf50 100%);
            color: white;
            border-color: #388e3c;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
        }

        #personTable tbody tr:has(.merge-reason:contains("❓")) .merge-reason {
            background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 50%, #9c27b0 100%);
            color: white;
            border-color: #7b1fa2;
            box-shadow: 0 2px 8px rgba(156, 39, 176, 0.3);
        }

        #personTable .merge-reason::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        #personTable .merge-reason:hover::before {
            left: 100%;
        }

        #personTable .additional-info {
            font-size: 13px;
            color: #495057;
            max-width: 160px;
            word-wrap: break-word;
            background-color: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }

        #personTable .document-number {
            font-family: 'Courier New', monospace;
            background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
            color: #383d41;
            padding: 6px 10px;
            border-radius: 6px;
            font-weight: 600;
            border: 1px solid #adb5bd;
            font-size: 13px;
            letter-spacing: 0.5px;
        }

        #personTable .count-info {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: 600;
            border: 1px solid #28a745;
            font-size: 13px;
        }

        /* Enhanced animations for table rows */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(40px) scale(0.95);
                filter: blur(2px);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
                filter: blur(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-60px) skewX(-15deg) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateX(0) skewX(0deg) scale(1);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(60px) skewX(15deg) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateX(0) skewX(0deg) scale(1);
            }
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.1) rotate(-180deg);
                filter: blur(3px);
            }
            50% {
                opacity: 1;
                transform: scale(1.1) rotate(0deg);
                filter: blur(1px);
            }
            70% {
                transform: scale(0.95) rotate(0deg);
                filter: blur(0);
            }
            100% {
                opacity: 1;
                transform: scale(1) rotate(0deg);
                filter: blur(0);
            }
        }

        @keyframes pulseGlow {
            0%, 100% {
                box-shadow: 0 0 8px rgba(255, 255, 255, 0.4), 0 0 16px rgba(255, 255, 255, 0.2);
            }
            50% {
                box-shadow: 0 0 16px rgba(255, 255, 255, 0.6), 0 0 32px rgba(255, 255, 255, 0.4);
            }
        }

        @keyframes rainbowGlow {
            0% { box-shadow: 0 0 12px #ff6b6b, 0 0 24px #ff6b6b; }
            16.66% { box-shadow: 0 0 12px #ffa726, 0 0 24px #ffa726; }
            33.33% { box-shadow: 0 0 12px #ffeb3b, 0 0 24px #ffeb3b; }
            50% { box-shadow: 0 0 12px #4caf50, 0 0 24px #4caf50; }
            66.66% { box-shadow: 0 0 12px #2196f3, 0 0 24px #2196f3; }
            83.33% { box-shadow: 0 0 12px #9c27b0, 0 0 24px #9c27b0; }
            100% { box-shadow: 0 0 12px #ff6b6b, 0 0 24px #ff6b6b; }
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        @keyframes pulseConflict {
            0%, 100% {
                box-shadow: 0 4px 15px rgba(211, 47, 47, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
            }
            50% {
                box-shadow: 0 6px 20px rgba(211, 47, 47, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.4);
            }
        }

        @keyframes pulseWarning {
            0%, 100% {
                box-shadow: 0 4px 15px rgba(245, 124, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
            }
            50% {
                box-shadow: 0 6px 20px rgba(245, 124, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.4);
            }
        }

        @keyframes pulseSuccess {
            0%, 100% {
                box-shadow: 0 4px 15px rgba(46, 125, 50, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
            }
            50% {
                box-shadow: 0 6px 20px rgba(46, 125, 50, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.4);
            }
        }

        @keyframes pulseReview {
            0%, 100% {
                box-shadow: 0 4px 15px rgba(123, 31, 162, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
            }
            50% {
                box-shadow: 0 6px 20px rgba(123, 31, 162, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.4);
            }
        }

        #personTable tbody tr {
            animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            animation-fill-mode: both;
        }

        #personTable tbody tr:nth-child(odd) {
            animation-delay: calc(var(--row-index, 0) * 0.1s + 0.1s);
        }

        #personTable tbody tr:nth-child(even) {
            animation-delay: calc(var(--row-index, 0) * 0.1s + 0.2s);
        }

        /* Add row index for staggered animation */
        #personTable tbody tr:nth-child(1) { --row-index: 1; }
        #personTable tbody tr:nth-child(2) { --row-index: 2; }
        #personTable tbody tr:nth-child(3) { --row-index: 3; }
        #personTable tbody tr:nth-child(4) { --row-index: 4; }
        #personTable tbody tr:nth-child(5) { --row-index: 5; }
        #personTable tbody tr:nth-child(6) { --row-index: 6; }
        #personTable tbody tr:nth-child(7) { --row-index: 7; }
        #personTable tbody tr:nth-child(8) { --row-index: 8; }
        #personTable tbody tr:nth-child(9) { --row-index: 9; }
        #personTable tbody tr:nth-child(10) { --row-index: 10; }
        /* Add more as needed for larger tables */

        /* Cell-specific animations */
        #personTable .person-name {
            animation: slideInLeft 0.8s ease-out;
            animation-delay: 0.5s;
            animation-fill-mode: both;
        }

        #personTable .merge-reason {
            animation: bounceIn 0.8s ease-out;
            animation-delay: 0.7s;
            animation-fill-mode: both;
        }

        #personTable .insurance-numbers,
        #personTable .manual-numbers {
            animation: slideInRight 0.8s ease-out;
            animation-delay: 0.6s;
            animation-fill-mode: both;
        }

        /* Special animations for different row types */
        #personTable tbody tr:has(.merge-reason:contains("🚨")) .merge-reason {
            animation: rainbowGlow 2s infinite;
        }

        #personTable tbody tr:has(.merge-reason:contains("⚠️")) .merge-reason {
            animation: glow 1.5s infinite;
        }

        #personTable tbody tr:has(.merge-reason:contains("✅")) .merge-reason {
            animation: glow 2s infinite ease-in-out;
        }

        /* Hover pulse effect for important cells */
        #personTable tbody tr:hover .merge-reason {
            animation: rainbowGlow 1s infinite;
        }

        #personTable tbody tr:hover .person-name {
            animation: glow 1s infinite;
        }

        /* Enhanced cell-specific hover effects with smooth transitions */
        #personTable tbody tr:hover .person-name {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 50%, #f8f9fa 100%);
            border-color: #adb5bd;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateX(8px) scale(1.02);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 6px;
        }

        #personTable tbody tr:hover .insurance-numbers,
        #personTable tbody tr:hover .manual-numbers {
            background: linear-gradient(135deg, #fff9c4 0%, #fff3cd 50%, #ffeaa7 100%);
            border-color: #ffc107;
            box-shadow: 0 3px 10px rgba(255, 193, 7, 0.25);
            transform: scale(1.05) translateY(-1px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 6px;
        }

        #personTable tbody tr:hover .merge-reason {
            transform: scale(1.08) rotate(2deg) translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 20;
        }

        #personTable tbody tr:hover .count-info {
            background: linear-gradient(135deg, #cce7ff 0%, #d1ecf1 50%, #bee5eb 100%);
            color: #0c5460;
            box-shadow: 0 3px 10px rgba(23, 162, 184, 0.25);
            transform: scale(1.1) translateY(-1px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 6px;
        }

        #personTable tbody tr:hover .document-number {
            background: linear-gradient(135deg, #f8f9fa 0%, #e2e3e5 50%, #d6d8db 100%);
            border-color: #adb5bd;
            box-shadow: 0 3px 10px rgba(108, 117, 125, 0.2);
            transform: scale(1.02);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 6px;
        }

        /* Enhanced Pagination Styles */
        .pagination {
            margin-bottom: 0;
        }

        .pagination .page-link {
            color: #667eea;
            border-color: #dee2e6;
            padding: 0.5rem 0.75rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .pagination .page-link:hover {
            color: #5a6fd8;
            background-color: #e9ecef;
            border-color: #adb5bd;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .pagination .page-item.active .page-link {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            color: white;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .pagination .page-item.disabled .page-link {
            color: #6c757d;
            background-color: #e9ecef;
            border-color: #dee2e6;
        }

        .pagination-info, .pagination-jumper {
            font-size: 0.875rem;
        }

        .pagination-jumper input {
            text-align: center;
            border-radius: 4px;
            border: 1px solid #ced4da;
        }

        .pagination-jumper input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        /* Responsive pagination */
        @media (max-width: 768px) {
            .pagination {
                flex-wrap: wrap;
                justify-content: center !important;
            }

            .pagination-info, .pagination-jumper {
                order: 1;
                width: 100%;
                text-align: center;
                margin-bottom: 0.5rem;
            }

            .pagination {
                order: 2;
                width: 100%;
            }

            .pagination .page-link {
                padding: 0.375rem 0.5rem;
                font-size: 0.875rem;
            }
        }

        /* Responsive table improvements */
        @media (max-width: 1400px) {
            #personTable {
                min-width: 1200px;
            }

            #personTable th,
            #personTable td {
                padding: 10px 8px;
                font-size: 13px;
                font-weight: 600;
            }

            #personTable .person-name {
                max-width: 180px;
                font-size: 13px;
            }

            #personTable .merge-reason {
                font-size: 11px;
                padding: 8px 10px;
            }
        }

        @media (max-width: 1200px) {
            #personTable {
                min-width: 1100px;
            }

            #personTable th,
            #personTable td {
                padding: 8px 6px;
                font-size: 12px;
                font-weight: 600;
            }

            #personTable .person-name {
                max-width: 160px;
                font-size: 12px;
            }

            #personTable .merge-reason {
                font-size: 10px;
                padding: 6px 8px;
            }
        }

        @media (max-width: 992px) {
            #personTable {
                min-width: 1000px;
            }

            #personTable th,
            #personTable td {
                padding: 6px 4px;
                font-size: 11px;
                font-weight: 600;
            }

            #personTable .person-name {
                max-width: 140px;
                font-size: 11px;
            }

            #personTable .additional-info {
                max-width: 120px;
                font-size: 10px;
            }

            #personTable .merge-reason {
                font-size: 9px;
                padding: 4px 6px;
            }
        }

        /* Print styles for the table */
        @media print {
            #personTable {
                font-size: 12px;
                box-shadow: none;
            }

            #personTable th,
            #personTable td {
                padding: 6px 4px;
                border: 1px solid #000;
            }

            #personTable .merge-reason {
                background: white !important;
                color: black !important;
                border: 1px solid #000 !important;
                box-shadow: none !important;
            }
        }

        @media (max-width: 768px) {
            .distributions-section .col-md-8,
            .distributions-section .col-md-4 {
                margin-bottom: 20px;
            }

            .table-sky thead th,
            .table-sky tbody td {
                padding: 8px 5px;
                font-size: 0.9rem;
            }

            #personTable {
                min-width: 900px;
                font-size: 10px;
            }

            #personTable th,
            #personTable td {
                padding: 4px 3px;
                font-size: 10px;
                font-weight: 600;
            }

            #personTable .person-name {
                max-width: 120px;
                font-size: 10px;
                padding: 6px 8px;
            }

            #personTable .additional-info {
                max-width: 100px;
                font-size: 9px;
            }

            #personTable .merge-reason {
                font-size: 8px;
                padding: 3px 4px;
            }

            #personTable .insurance-numbers,
            #personTable .manual-numbers {
                font-size: 9px;
                padding: 4px 6px;
            }

            .card-header .btn-group {
                flex-direction: column;
                gap: 5px;
            }

            .card-header .btn-group .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">
                <i class="fas fa-chart-line me-2"></i>
                نظام تحليل جميع القطاعات
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-home me-1"></i>
                    الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            نتائج تحليل البيانات
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- Session ID -->
                        <div class="alert alert-info">
                            <i class="fas fa-id-badge me-2"></i>
                            <strong>معرف الجلسة:</strong> {{ session_id }}
                        </div>

                        <!-- Basic Statistics -->
                        {% if analysis_data %}
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>
                                    الإحصائيات الأساسية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h3 class="text-primary">{{ "{:,}".format(analysis_data.file_info.rows) if analysis_data.file_info else 'غير متاح' }}</h3>
                                            <p>إجمالي الصفوف</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h3 class="text-success">{{ analysis_data.file_info.columns if analysis_data.file_info else 'غير متاح' }}</h3>
                                            <p>إجمالي الأعمدة</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h3 class="text-warning">
                                                {% set total_missing = 0 %}
                                                {% if analysis_data.analysis and analysis_data.analysis.column_analysis %}
                                                    {% for col_data in analysis_data.analysis.column_analysis.values() %}
                                                        {% set total_missing = total_missing + col_data.missing_count %}
                                                    {% endfor %}
                                                {% endif %}
                                                {{ "{:,}".format(total_missing) }}
                                            </h3>
                                            <p>القيم المفقودة</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h3 class="text-info">
                                                {% set data_quality = 100 %}
                                                {% if analysis_data.file_info.rows > 0 %}
                                                    {% set total_cells = analysis_data.file_info.rows * analysis_data.file_info.columns %}
                                                    {% set total_missing = 0 %}
                                                    {% if analysis_data.analysis and analysis_data.analysis.column_analysis %}
                                                        {% for col_data in analysis_data.analysis.column_analysis.values() %}
                                                            {% set total_missing = total_missing + col_data.missing_count %}
                                                        {% endfor %}
                                                    {% endif %}
                                                    {% set data_quality = ((total_cells - total_missing) / total_cells * 100) %}
                                                {% endif %}
                                                {{ "%.1f"|format(data_quality) }}%
                                            </h3>
                                            <p>جودة البيانات</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Charts Link -->
                        <!-- تفاصيل جودة البيانات -->
                        {% if analysis_data.get('analysis') and analysis_data.analysis.get('basic_stats') %}
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>
                                    تفاصيل معايير جودة البيانات
                                </h5>
                            </div>
                            <div class="card-body">
                                {% set basic_stats = analysis_data.analysis.basic_stats %}
                                {% set data_quality_score = basic_stats.get('data_quality_score', 85.0) %}
                                {% set total_rows = basic_stats.get('total_rows', 0) %}
                                {% set total_columns = basic_stats.get('total_columns', 0) %}

                                {# Calculate completeness score #}
                                {% set completeness_score = 100 %}
                                {% if analysis_data.analysis.get('column_analysis') %}
                                    {% set total_missing = 0 %}
                                    {% set total_cells = total_rows * total_columns %}
                                    {% for col_data in analysis_data.analysis.column_analysis.values() %}
                                        {% set total_missing = total_missing + col_data.get('missing_count', 0) %}
                                    {% endfor %}
                                    {% if total_cells > 0 %}
                                        {% set completeness_score = ((total_cells - total_missing) / total_cells * 100) %}
                                    {% endif %}
                                {% endif %}

                                {# Calculate uniqueness score (simplified) #}
                                {% set uniqueness_score = 95.0 %}
                                {% if analysis_data.get('column_duplicates') %}
                                    {% set total_duplicates = 0 %}
                                    {% for dup_data in analysis_data.column_duplicates.values() %}
                                        {% set total_duplicates = total_duplicates + dup_data.get('duplicates_2', 0) + dup_data.get('duplicates_3_plus', 0) %}
                                    {% endfor %}
                                    {% if total_rows > 0 %}
                                        {% set uniqueness_score = (1 - (total_duplicates / total_rows)) * 100 %}
                                    {% endif %}
                                {% endif %}

                                {# Calculate validity score (placeholder) #}
                                {% set validity_score = 90.0 %}

                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <div class="p-3 bg-success bg-opacity-10 rounded quality-criterion-card" style="cursor: pointer;" onclick="showCriterionDetails('completeness')">
                                            <div class="fw-bold text-success h4 mb-1">{{ "%.1f"|format(completeness_score) }}%</div>
                                            <small class="text-muted">الاكتمال</small>
                                            <br><small class="text-muted">({{ "%.1f"|format(100 - completeness_score) }}% خطأ)</small>
                                            <div class="mt-2">
                                                <small class="text-success"><i class="fas fa-mouse-pointer me-1"></i>انقر للتفاصيل</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="p-3 bg-info bg-opacity-10 rounded quality-criterion-card" style="cursor: pointer;" onclick="showCriterionDetails('uniqueness')">
                                            <div class="fw-bold text-info h4 mb-1">{{ "%.1f"|format(uniqueness_score) }}%</div>
                                            <small class="text-muted">الفرادة</small>
                                            <br><small class="text-muted">({{ "%.1f"|format(100 - uniqueness_score) }}% خطأ)</small>
                                            <div class="mt-2">
                                                <small class="text-info"><i class="fas fa-mouse-pointer me-1"></i>انقر للتفاصيل</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="p-3 bg-warning bg-opacity-10 rounded quality-criterion-card" style="cursor: pointer;" onclick="showCriterionDetails('validity')">
                                            <div class="fw-bold text-warning h4 mb-1">{{ "%.1f"|format(validity_score) }}%</div>
                                            <small class="text-muted">الصلاحية</small>
                                            <br><small class="text-muted">(قيد التطوير)</small>
                                            <div class="mt-2">
                                                <small class="text-warning"><i class="fas fa-mouse-pointer me-1"></i>انقر للتفاصيل</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="p-3 bg-primary bg-opacity-10 rounded quality-criterion-card" style="cursor: pointer;" onclick="showCriterionDetails('overall')">
                                            <div class="fw-bold text-primary h4 mb-1">{{ "%.1f"|format(data_quality_score) }}%</div>
                                            <small class="text-muted">الجودة الإجمالية</small>
                                            <br><small class="text-muted">متوسط المعايير</small>
                                            <div class="mt-2">
                                                <small class="text-primary"><i class="fas fa-mouse-pointer me-1"></i>انقر للتفاصيل</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Quality Criteria Detail Modals -->
                        <div class="modal fade" id="completenessModal" tabindex="-1" aria-labelledby="completenessModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header bg-success text-white">
                                        <h5 class="modal-title" id="completenessModalLabel">
                                            <i class="fas fa-check-circle me-2"></i>
                                            تفاصيل معيار الاكتمال
                                        </h5>
                                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        {% set completeness_score = 100 %}
                                        {% if analysis_data.analysis.get('column_analysis') %}
                                            {% set total_missing = 0 %}
                                            {% set total_cells = total_rows * total_columns %}
                                            {% for col_data in analysis_data.analysis.column_analysis.values() %}
                                                {% set total_missing = total_missing + col_data.get('missing_count', 0) %}
                                            {% endfor %}
                                            {% if total_cells > 0 %}
                                                {% set completeness_score = ((total_cells - total_missing) / total_cells * 100) %}
                                            {% endif %}
                                        {% endif %}

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="card border-success">
                                                    <div class="card-body text-center">
                                                        <h3 class="text-success">{{ "%.1f"|format(completeness_score) }}%</h3>
                                                        <p class="text-muted mb-0">معدل الاكتمال</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="card border-danger">
                                                    <div class="card-body text-center">
                                                        <h3 class="text-danger">{{ "%.1f"|format(100 - completeness_score) }}%</h3>
                                                        <p class="text-muted mb-0">معدل الخطأ (قيم مفقودة)</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mt-4">
                                            <h6>تفسير النتائج:</h6>
                                            <ul class="list-unstyled">
                                                <li><i class="fas fa-info-circle text-info me-2"></i>معدل الاكتمال يقيس نسبة البيانات المكتملة في جميع الأعمدة</li>
                                                <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>القيم المفقودة تؤثر سلباً على جودة التحليل والتقارير</li>
                                                {% if completeness_score >= 90 %}
                                                <li><i class="fas fa-check-circle text-success me-2"></i>معدل الاكتمال ممتاز - البيانات جاهزة للتحليل</li>
                                                {% elif completeness_score >= 70 %}
                                                <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>معدل الاكتمال متوسط - يُنصح بتنظيف البيانات</li>
                                                {% else %}
                                                <li><i class="fas fa-times-circle text-danger me-2"></i>معدل الاكتمال منخفض - يتطلب تدخل فوري</li>
                                                {% endif %}
                                            </ul>
                                        </div>

                                        {% if analysis_data.analysis.column_analysis %}
                                        <div class="mt-4">
                                            <h6>تفاصيل الأعمدة:</h6>
                                            <div class="table-responsive">
                                                <table class="table table-sm table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>اسم العمود</th>
                                                            <th>القيم المفقودة</th>
                                                            <th>نسبة المفقود</th>
                                                            <th>الحالة</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {% for col_name, col_data in analysis_data.analysis.column_analysis.items() %}
                                                        <tr>
                                                            <td class="fw-bold">{{ col_name }}</td>
                                                            <td>{{ "{:,}".format(col_data.missing_count) }}</td>
                                                            <td>
                                                                <span class="badge {% if col_data.missing_percentage > 20 %}bg-danger{% elif col_data.missing_percentage > 10 %}bg-warning{% else %}bg-success{% endif %}">
                                                                    {{ "%.1f"|format(col_data.missing_percentage) }}%
                                                                </span>
                                                            </td>
                                                            <td>
                                                                {% if col_data.missing_percentage == 0 %}
                                                                <span class="badge bg-success">مكتمل</span>
                                                                {% elif col_data.missing_percentage < 10 %}
                                                                <span class="badge bg-info">جيد</span>
                                                                {% elif col_data.missing_percentage < 20 %}
                                                                <span class="badge bg-warning">متوسط</span>
                                                                {% else %}
                                                                <span class="badge bg-danger">يحتاج تدخل</span>
                                                                {% endif %}
                                                            </td>
                                                        </tr>
                                                        {% endfor %}
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        {% endif %}
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-success" onclick="exportCriterionData('completeness')">
                                            <i class="fas fa-download me-1"></i>
                                            تصدير التقرير
                                        </button>
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="modal fade" id="uniquenessModal" tabindex="-1" aria-labelledby="uniquenessModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header bg-info text-white">
                                        <h5 class="modal-title" id="uniquenessModalLabel">
                                            <i class="fas fa-fingerprint me-2"></i>
                                            تفاصيل معيار الفرادة
                                        </h5>
                                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        {% set uniqueness_score = 95.0 %}
                                        {% if analysis_data.get('column_duplicates') %}
                                            {% set total_duplicates = 0 %}
                                            {% for dup_data in analysis_data.column_duplicates.values() %}
                                                {% set total_duplicates = total_duplicates + dup_data.get('duplicates_2', 0) + dup_data.get('duplicates_3_plus', 0) %}
                                            {% endfor %}
                                            {% if total_rows > 0 %}
                                                {% set uniqueness_score = (1 - (total_duplicates / total_rows)) * 100 %}
                                            {% endif %}
                                        {% endif %}

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="card border-info">
                                                    <div class="card-body text-center">
                                                        <h3 class="text-info">{{ "%.1f"|format(uniqueness_score) }}%</h3>
                                                        <p class="text-muted mb-0">معدل الفرادة</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="card border-warning">
                                                    <div class="card-body text-center">
                                                        <h3 class="text-warning">{{ "%.1f"|format(100 - uniqueness_score) }}%</h3>
                                                        <p class="text-muted mb-0">معدل التكرار</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mt-4">
                                            <h6>تفسير النتائج:</h6>
                                            <ul class="list-unstyled">
                                                <li><i class="fas fa-info-circle text-info me-2"></i>معدل الفرادة يقيس مدى تنوع البيانات وعدم تكرارها</li>
                                                <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>التكرار المفرط يؤثر على دقة التحليل والإحصائيات</li>
                                                {% if uniqueness_score >= 90 %}
                                                <li><i class="fas fa-check-circle text-success me-2"></i>معدل الفرادة ممتاز - البيانات متنوعة وفريدة</li>
                                                {% elif uniqueness_score >= 70 %}
                                                <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>معدل الفرادة متوسط - يُنصح بمراجعة التكرارات</li>
                                                {% else %}
                                                <li><i class="fas fa-times-circle text-danger me-2"></i>معدل الفرادة منخفض - يتطلب تنظيف التكرارات</li>
                                                {% endif %}
                                            </ul>
                                        </div>

                                        {% if analysis_data.column_duplicates %}
                                        <div class="mt-4">
                                            <h6>تفاصيل التكرارات:</h6>
                                            <div class="table-responsive">
                                                <table class="table table-sm table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>اسم العمود</th>
                                                            <th>تكرار مرتين</th>
                                                            <th>تكرار 3+ مرات</th>
                                                            <th>إجمالي التكرارات</th>
                                                            <th>الحالة</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {% for col_name, dup_data in analysis_data.column_duplicates.items() %}
                                                        {% set total_dups = (dup_data.get('duplicates_2', 0) | int) + (dup_data.get('duplicates_3_plus', 0) | int) %}
                                                        <tr>
                                                            <td class="fw-bold">{{ col_name }}</td>
                                                            <td>{{ "{:,}".format(dup_data.get('duplicates_2', 0)) }}</td>
                                                            <td>{{ "{:,}".format(dup_data.get('duplicates_3_plus', 0)) }}</td>
                                                            <td>{{ "{:,}".format(total_dups) }}</td>
                                                            <td>
                                                                {% if total_dups == 0 %}
                                                                <span class="badge bg-success">فريد</span>
                                                                {% elif total_dups < 10 %}
                                                                <span class="badge bg-info">قليل</span>
                                                                {% elif total_dups < 50 %}
                                                                <span class="badge bg-warning">متوسط</span>
                                                                {% else %}
                                                                <span class="badge bg-danger">كثير</span>
                                                                {% endif %}
                                                            </td>
                                                        </tr>
                                                        {% endfor %}
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        {% endif %}
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-info" onclick="exportCriterionData('uniqueness')">
                                            <i class="fas fa-download me-1"></i>
                                            تصدير التقرير
                                        </button>
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="modal fade" id="validityModal" tabindex="-1" aria-labelledby="validityModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header bg-warning text-dark">
                                        <h5 class="modal-title" id="validityModalLabel">
                                            <i class="fas fa-balance-scale me-2"></i>
                                            تفاصيل معيار الصلاحية
                                        </h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        {% set validity_score = 90.0 %}

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="card border-warning">
                                                    <div class="card-body text-center">
                                                        <h3 class="text-warning">{{ "%.1f"|format(validity_score) }}%</h3>
                                                        <p class="text-muted mb-0">معدل الصلاحية</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="card border-secondary">
                                                    <div class="card-body text-center">
                                                        <h3 class="text-secondary">قيد التطوير</h3>
                                                        <p class="text-muted mb-0">حالة المعيار</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mt-4">
                                            <h6>تفسير النتائج:</h6>
                                            <ul class="list-unstyled">
                                                <li><i class="fas fa-info-circle text-info me-2"></i>معيار الصلاحية يتحقق من صحة البيانات ومطابقتها للمعايير</li>
                                                <li><i class="fas fa-tools text-warning me-2"></i>هذا المعيار قيد التطوير والتحسين المستمر</li>
                                                <li><i class="fas fa-clock text-muted me-2"></i>سيتم إضافة المزيد من فحوصات الصلاحية قريباً</li>
                                            </ul>
                                        </div>

                                        <div class="mt-4">
                                            <h6>الفحوصات المخططة:</h6>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="card border-light">
                                                        <div class="card-body">
                                                            <h6 class="card-title">فحوصات تنسيق البيانات</h6>
                                                            <ul class="list-unstyled small">
                                                                <li><i class="fas fa-check text-success me-1"></i>تنسيق التواريخ</li>
                                                                <li><i class="fas fa-check text-success me-1"></i>تنسيق الأرقام</li>
                                                                <li><i class="fas fa-clock text-muted me-1"></i>تنسيق النصوص</li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="card border-light">
                                                        <div class="card-body">
                                                            <h6 class="card-title">فحوصات منطقية</h6>
                                                            <ul class="list-unstyled small">
                                                                <li><i class="fas fa-clock text-muted me-1"></i>نطاقات القيم المقبولة</li>
                                                                <li><i class="fas fa-clock text-muted me-1"></i>العلاقات بين الأعمدة</li>
                                                                <li><i class="fas fa-clock text-muted me-1"></i>القيود المرجعية</li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-warning" onclick="exportCriterionData('validity')" disabled>
                                            <i class="fas fa-download me-1"></i>
                                            تصدير التقرير (قريباً)
                                        </button>
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="modal fade" id="overallModal" tabindex="-1" aria-labelledby="overallModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-xl">
                                <div class="modal-content">
                                    <div class="modal-header bg-primary text-white">
                                        <h5 class="modal-title" id="overallModalLabel">
                                            <i class="fas fa-trophy me-2"></i>
                                            تفاصيل الجودة الإجمالية
                                        </h5>
                                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="card border-success">
                                                    <div class="card-body text-center">
                                                        {% set completeness_score = 100 %}
                                                        {% if analysis_data.analysis.get('column_analysis') %}
                                                            {% set total_missing = 0 %}
                                                            {% set total_cells = analysis_data.analysis.basic_stats.total_rows * analysis_data.analysis.basic_stats.total_columns %}
                                                            {% for col_data in analysis_data.analysis.column_analysis.values() %}
                                                                {% set total_missing = total_missing + col_data.get('missing_count', 0) %}
                                                            {% endfor %}
                                                            {% if total_cells > 0 %}
                                                                {% set completeness_score = ((total_cells - total_missing) / total_cells * 100) %}
                                                            {% endif %}
                                                        {% endif %}
                                                        <h4 class="text-success">{{ "%.1f"|format(completeness_score) }}%</h4>
                                                        <p class="text-muted mb-0">الاكتمال</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="card border-info">
                                                    <div class="card-body text-center">
                                                        <h4 class="text-info">{{ "%.1f"|format(analysis_data.analysis.basic_stats.quality_details.uniqueness_score) }}%</h4>
                                                        <p class="text-muted mb-0">الفرادة</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="card border-warning">
                                                    <div class="card-body text-center">
                                                        <h4 class="text-warning">{{ "%.1f"|format(analysis_data.analysis.basic_stats.quality_details.validity_score) }}%</h4>
                                                        <p class="text-muted mb-0">الصلاحية</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="card border-primary">
                                                    <div class="card-body text-center">
                                                        <h3 class="text-primary fw-bold">{{ "%.1f"|format(analysis_data.analysis.basic_stats.data_quality_score) }}%</h3>
                                                        <p class="text-muted mb-0">المتوسط الإجمالي</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mt-4">
                                            <h6>تحليل شامل للجودة:</h6>
                                            <div class="progress mb-3" style="height: 30px;">
                                                <div class="progress-bar {% if analysis_data.analysis.basic_stats.data_quality_score >= 90 %}bg-success{% elif analysis_data.analysis.basic_stats.data_quality_score >= 70 %}bg-info{% elif analysis_data.analysis.basic_stats.data_quality_score >= 50 %}bg-warning{% else %}bg-danger{% endif %}"
                                                     role="progressbar"
                                                     data-width="{{ analysis_data.analysis.basic_stats.data_quality_score }}"
                                                     aria-valuenow="{{ analysis_data.analysis.basic_stats.data_quality_score }}"
                                                     aria-valuemin="0"
                                                     aria-valuemax="100">
                                                    {{ "%.1f"|format(analysis_data.analysis.basic_stats.data_quality_score) }}%
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <h6>نقاط القوة:</h6>
                                                    <ul class="list-unstyled">
                                                        {% set completeness_score = 100 %}
                                                        {% if analysis_data.analysis.get('column_analysis') %}
                                                            {% set total_missing = 0 %}
                                                            {% set total_cells = analysis_data.analysis.basic_stats.total_rows * analysis_data.analysis.basic_stats.total_columns %}
                                                            {% for col_data in analysis_data.analysis.column_analysis.values() %}
                                                                {% set total_missing = total_missing + col_data.get('missing_count', 0) %}
                                                            {% endfor %}
                                                            {% if total_cells > 0 %}
                                                                {% set completeness_score = ((total_cells - total_missing) / total_cells * 100) %}
                                                            {% endif %}
                                                        {% endif %}
                                                        {% if completeness_score >= 80 %}
                                                        <li><i class="fas fa-check-circle text-success me-2"></i>مستوى عالي من الاكتمال</li>
                                                        {% endif %}
                                                        {% if analysis_data.analysis.basic_stats.quality_details.uniqueness_score >= 80 %}
                                                        <li><i class="fas fa-check-circle text-success me-2"></i>تنوع جيد في البيانات</li>
                                                        {% endif %}
                                                        {% if analysis_data.analysis.basic_stats.data_quality_score >= 75 %}
                                                        <li><i class="fas fa-trophy text-warning me-2"></i>جودة إجمالية ممتازة</li>
                                                        {% endif %}
                                                    </ul>
                                                </div>
                                                <div class="col-md-6">
                                                    <h6>نقاط التحسين:</h6>
                                                    <ul class="list-unstyled">
                                                        {% if analysis_data.analysis.basic_stats.quality_details.completeness_score < 80 %}
                                                        <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>تحسين الاكتمال مطلوب</li>
                                                        {% endif %}
                                                        {% if analysis_data.analysis.basic_stats.quality_details.uniqueness_score < 80 %}
                                                        <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>تقليل التكرارات مُستحسن</li>
                                                        {% endif %}
                                                        {% if analysis_data.analysis.basic_stats.data_quality_score < 75 %}
                                                        <li><i class="fas fa-tools text-info me-2"></i>خطة تحسين شاملة مطلوبة</li>
                                                        {% endif %}
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mt-4">
                                            <h6>توصيات للتحسين:</h6>
                                            <div class="alert alert-info">
                                                <ul class="mb-0">
                                                    <li>تنفيذ عمليات تنظيف دورية للبيانات</li>
                                                    <li>تطبيق قواعد التحقق من صحة البيانات</li>
                                                    <li>تدريب المستخدمين على أفضل الممارسات</li>
                                                    <li>استخدام أدوات التحقق الآلي</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-primary" onclick="exportCriterionData('overall')">
                                            <i class="fas fa-download me-1"></i>
                                            تصدير التقرير الشامل
                                        </button>
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mb-4">
                            <div class="btn-group" role="group">
                                <a href="/charts/{{ session_id }}" class="btn btn-gradient btn-lg" title="عرض الرسوم البيانية التفاعلية">
                                    <i class="fas fa-chart-bar fa-2x me-2"></i>
                                    <span class="d-none d-md-inline">عرض الرسوم البيانية التفاعلية المتقدمة</span>
                                    <i class="fas fa-external-link-alt ms-2"></i>
                                </a>
                                <a href="/export/pdf/{{ session_id }}" class="btn btn-outline-primary btn-lg" title="تصدير تقرير PDF" target="_blank">
                                    <i class="fas fa-file-pdf fa-2x me-2"></i>
                                    <span class="d-none d-md-inline">تصدير تقرير PDF</span>
                                    <i class="fas fa-download ms-2"></i>
                                </a>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Interactive Column Charts Section -->
                        {% if analysis_data.analysis and analysis_data.analysis.distributions %}
                        <div class="interactive-charts-section">
                            <div class="card-header border-0" style="background: transparent; padding: 30px;">
                                <h4 class="mb-0 text-white text-center fw-bold">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    الرسوم البيانية التفاعلية للأعمدة
                                </h4>
                                <p class="mb-0 text-white-50 text-center mt-2">تحليل بصري تفاعلي لجميع أعمدة البيانات مع إمكانيات تخصيص متقدمة</p>
                            </div>
                            <div class="card-body p-4">
                                <!-- Chart Controls -->
                                <div class="chart-controls">
                                    <div class="row mb-3">
                                        <div class="col-md-4">
                                            <label for="chartType" class="form-label text-white fw-bold">
                                                <i class="fas fa-chart-bar me-2"></i>نوع الرسم البياني:
                                            </label>
                                            <select id="chartType" class="form-select" onchange="updateAllCharts()">
                                                <option value="bar">📊 أعمدة عمودية</option>
                                                <option value="horizontalBar">📈 أعمدة أفقية</option>
                                                <option value="pie">🥧 دائري</option>
                                                <option value="doughnut">🍩 دائري مفرغ</option>
                                                <option value="line">📉 خطي</option>
                                                <option value="radar">🎯 رادار</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="colorScheme" class="form-label text-white fw-bold">
                                                <i class="fas fa-palette me-2"></i>نظام الألوان:
                                            </label>
                                            <select id="colorScheme" class="form-select" onchange="updateAllCharts()">
                                                <option value="default">🎨 افتراضي</option>
                                                <option value="vibrant">🌈 ألوان زاهية</option>
                                                <option value="pastel">🌸 ألوان هادئة</option>
                                                <option value="dark">🌙 ألوان داكنة</option>
                                                <option value="gradient">✨ متدرج</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="maxValues" class="form-label text-white fw-bold">
                                                <i class="fas fa-list-ol me-2"></i>عدد القيم:
                                            </label>
                                            <select id="maxValues" class="form-select" onchange="updateAllCharts()">
                                                <option value="5">أفضل 5 قيم</option>
                                                <option value="10" selected>أفضل 10 قيم</option>
                                                <option value="15">أفضل 15 قيمة</option>
                                                <option value="20">أفضل 20 قيمة</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-12 text-center">
                                            <div class="d-flex gap-2 justify-content-center">
                                                <button type="button" class="btn btn-outline-light btn-sm" onclick="updateAllCharts()">
                                                    <i class="fas fa-sync me-1"></i>تحديث
                                                </button>
                                                <button type="button" class="btn btn-outline-light btn-sm" onclick="exportAllCharts()">
                                                    <i class="fas fa-download me-1"></i>تصدير
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Charts Container -->
                                <div id="interactiveChartsContainer" class="row">
                                    <!-- Charts will be generated here by JavaScript -->
                                </div>

                                <!-- Export Options -->
                                <div class="text-center mt-4">
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-outline-light" onclick="exportChartsPNG()">
                                            <i class="fas fa-image me-1"></i>تصدير PNG
                                        </button>
                                        <button type="button" class="btn btn-outline-light" onclick="exportChartsPDF()">
                                            <i class="fas fa-file-pdf me-1"></i>تصدير PDF
                                        </button>
                                        <button type="button" class="btn btn-outline-light" onclick="printCharts()">
                                            <i class="fas fa-print me-1"></i>طباعة
                                        </button>
                                        <button type="button" class="btn btn-outline-light" onclick="shareCharts()">
                                            <i class="fas fa-share me-1"></i>مشاركة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Recommendations -->
                        {% if analysis_data.analysis and analysis_data.analysis.recommendations %}
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-lightbulb me-2"></i>
                                    التوصيات
                                </h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    {% for recommendation in analysis_data.analysis.recommendations %}
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        {{ recommendation }}
                                    </li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                        {% endif %}
                        <!-- Advanced Analytics Section -->
                        {% if analysis_data.get('analysis') %}
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-brain me-2"></i>
                                    التحليلات المتقدمة والذكية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <!-- Data Quality Insights -->
                                    <div class="col-md-6 mb-4">
                                        <div class="card h-100">
                                            <div class="card-header bg-info text-white">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-chart-line me-2"></i>
                                                    رؤى جودة البيانات
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row text-center">
                                                    <div class="col-6">
                                                        <div class="p-3 bg-success bg-opacity-10 rounded">
                                                            <div class="fw-bold text-success h4 mb-1">
                                                                {% set completeness = 100 %}
                                                                {% if analysis_data.file_info.rows > 0 %}
                                                                    {% set total_cells = analysis_data.file_info.rows * analysis_data.file_info.columns %}
                                                                    {% set total_missing = 0 %}
                                                                    {% if analysis_data.analysis.get('column_analysis') %}
                                                                        {% for col_data in analysis_data.analysis.column_analysis.values() %}
                                                                            {% set total_missing = total_missing + col_data.missing_count %}
                                                                        {% endfor %}
                                                                    {% endif %}
                                                                    {% set completeness = ((total_cells - total_missing) / total_cells * 100) %}
                                                                {% endif %}
                                                                {{ "%.1f"|format(completeness) }}%
                                                            </div>
                                                            <small class="text-muted">الاكتمال</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="p-3 bg-warning bg-opacity-10 rounded">
                                                            <div class="fw-bold text-warning h4 mb-1">
                                                                {% set duplicates = 0 %}
                                                                {% if analysis_data.get('column_duplicates') %}
                                                                    {% for dup_key, dup_data in analysis_data.column_duplicates.items() %}
                                                                        {% if dup_data and dup_data.get('duplicates_2') is defined and dup_data.get('duplicates_3_plus') is defined %}
                                                                            {% set duplicates = duplicates + (dup_data.get('duplicates_2', 0) | int) + (dup_data.get('duplicates_3_plus', 0) | int) %}
                                                                        {% endif %}
                                                                    {% endfor %}
                                                                {% endif %}
                                                                {{ "{:,}".format(duplicates) }}
                                                            </div>
                                                            <small class="text-muted">التكرارات</small>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="mt-3">
                                                    <h6>التوصيات الذكية:</h6>
                                                    <ul class="list-unstyled small">
                                                        {% if completeness < 90 %}
                                                        <li><i class="fas fa-exclamation-triangle text-warning me-1"></i>تحسين جودة البيانات مطلوب</li>
                                                        {% endif %}
                                                        {% if duplicates > 0 %}
                                                        <li><i class="fas fa-copy text-info me-1"></i>مراجعة السجلات المكررة</li>
                                                        {% endif %}
                                                        <li><i class="fas fa-check-circle text-success me-1"></i>تحليل شامل مكتمل</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Sector Analysis Insights -->
                                    <div class="col-md-6 mb-4">
                                        <div class="card h-100">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-industry me-2"></i>
                                                    تحليل القطاع المكتشف
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="text-center mb-3">
                                                    <h4 class="text-primary">
                                                        {% if analysis_data.detected_sector %}
                                                            {% if analysis_data.detected_sector == 'insurance' %}
                                                                <i class="fas fa-shield-alt me-2"></i>قطاع التأمين
                                                            {% elif analysis_data.detected_sector == 'customs' %}
                                                                <i class="fas fa-truck me-2"></i>القطاع الجمركي
                                                            {% elif analysis_data.detected_sector == 'education' %}
                                                                <i class="fas fa-graduation-cap me-2"></i>قطاع التعليم
                                                            {% elif analysis_data.detected_sector == 'health' %}
                                                                <i class="fas fa-hospital me-2"></i>قطاع الصحة
                                                            {% elif analysis_data.detected_sector == 'finance' %}
                                                                <i class="fas fa-money-bill-wave me-2"></i>القطاع المالي
                                                            {% elif analysis_data.detected_sector == 'hr' %}
                                                                <i class="fas fa-users me-2"></i>الموارد البشرية
                                                            {% else %}
                                                                <i class="fas fa-folder me-2"></i>قطاع عام
                                                            {% endif %}
                                                        {% else %}
                                                            <i class="fas fa-question-circle me-2"></i>غير محدد
                                                        {% endif %}
                                                    </h4>
                                                    {% if analysis_data.confidence %}
                                                    <small class="text-muted">الثقة: {{ "%.1f"|format(analysis_data.confidence * 100) }}%</small>
                                                    {% endif %}
                                                </div>

                                                <div class="row text-center">
                                                    <div class="col-6">
                                                        <div class="p-2 bg-light rounded">
                                                            <div class="fw-bold h5 mb-1">{{ analysis_data.file_info.columns if analysis_data.file_info else 0 }}</div>
                                                            <small class="text-muted">الأعمدة</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="p-2 bg-light rounded">
                                                            <div class="fw-bold h5 mb-1">{{ "{:,}".format(analysis_data.file_info.rows) if analysis_data.file_info else 0 }}</div>
                                                            <small class="text-muted">الصفوف</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Advanced Statistics -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-calculator me-2"></i>
                                                    الإحصائيات المتقدمة
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <!-- Numeric Columns Stats -->
                                                    {% set numeric_cols = [] %}
                                                    {% if analysis_data.analysis.get('column_analysis') %}
                                                        {% for col_name, col_data in analysis_data.analysis.column_analysis.items() %}
                                                            {% if col_data.type == 'number' %}
                                                                {% set numeric_cols = numeric_cols.append(col_name) %}
                                                            {% endif %}
                                                        {% endfor %}
                                                    {% endif %}

                                                    <div class="col-md-4 mb-3">
                                                        <div class="text-center p-3 bg-primary bg-opacity-10 rounded">
                                                            <h5 class="text-primary mb-1">{{ numeric_cols|length }}</h5>
                                                            <small class="text-muted">أعمدة رقمية</small>
                                                        </div>
                                                    </div>

                                                    <!-- Text Columns Stats -->
                                                    {% set text_cols = [] %}
                                                    {% if analysis_data.analysis.get('column_analysis') %}
                                                        {% for col_name, col_data in analysis_data.analysis.column_analysis.items() %}
                                                            {% if col_data.type == 'text' %}
                                                                {% set text_cols = text_cols.append(col_name) %}
                                                            {% endif %}
                                                        {% endfor %}
                                                    {% endif %}

                                                    <div class="col-md-4 mb-3">
                                                        <div class="text-center p-3 bg-info bg-opacity-10 rounded">
                                                            <h5 class="text-info mb-1">{{ text_cols|length }}</h5>
                                                            <small class="text-muted">أعمدة نصية</small>
                                                        </div>
                                                    </div>

                                                    <!-- Data Patterns -->
                                                    <div class="col-md-4 mb-3">
                                                        <div class="text-center p-3 bg-success bg-opacity-10 rounded">
                                                            <h5 class="text-success mb-1">
                                                                {% set pattern_count = 0 %}
                                                                {% if analysis_data.analysis.get('sector_specific') and analysis_data.analysis.sector_specific.get('data_patterns') %}
                                                                    {% set pattern_count = analysis_data.analysis.sector_specific.data_patterns|length %}
                                                                {% endif %}
                                                                {{ pattern_count }}
                                                            </h5>
                                                            <small class="text-muted">أنماط مكتشفة</small>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Data Patterns Details -->
                                                {% if analysis_data.analysis.get('sector_specific') and analysis_data.analysis.sector_specific.get('data_patterns') %}
                                                <div class="mt-3">
                                                    <h6>الأنماط المكتشفة في البيانات:</h6>
                                                    <div class="row">
                                                        {% for col, pattern in analysis_data.analysis.sector_specific.data_patterns.items() %}
                                                        <div class="col-md-3 mb-2">
                                                            <span class="badge bg-secondary">{{ col }}: {{ pattern }}</span>
                                                        </div>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- AI-Powered Insights -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header bg-gradient">
                                                <h6 class="mb-0 text-white">
                                                    <i class="fas fa-robot me-2"></i>
                                                    رؤى مدعومة بالذكاء الاصطناعي
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <h6>تحليل البيانات:</h6>
                                                        <ul class="list-unstyled">
                                                            <li><i class="fas fa-check-circle text-success me-2"></i>تم تحليل {{ analysis_data.file_info.columns if analysis_data.file_info else 0 }} عمود بنجاح</li>
                                                            <li><i class="fas fa-check-circle text-success me-2"></i>تم كشف {{ analysis_data.detected_sector if analysis_data.detected_sector else 'قطاع عام' }} كقطاع رئيسي</li>
                                                            <li><i class="fas fa-check-circle text-success me-2"></i>تم إنشاء توزيعات بيانات تفاعلية</li>
                                                            {% if analysis_data.analysis.sector_specific and analysis_data.analysis.sector_specific.get('merge_analysis') %}
                                                            <li><i class="fas fa-check-circle text-success me-2"></i>تم تحليل مرشحي الدمج ({{ analysis_data.analysis.sector_specific.merge_analysis.total_candidates }})</li>
                                                            {% endif %}
                                                        </ul>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <h6>التوصيات الذكية:</h6>
                                                        <ul class="list-unstyled">
                                                            {% set completeness = 100 %}
                                                            {% if analysis_data.file_info.rows > 0 %}
                                                                {% set total_cells = analysis_data.file_info.rows * analysis_data.file_info.columns %}
                                                                {% set total_missing = 0 %}
                                                                {% for col_data in analysis_data.analysis.column_analysis.values() %}
                                                                    {% set total_missing = total_missing + col_data.missing_count %}
                                                                {% endfor %}
                                                                {% set completeness = ((total_cells - total_missing) / total_cells * 100) %}
                                                            {% endif %}

                                                            {% if completeness < 95 %}
                                                            <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>التركيز على تحسين جودة البيانات</li>
                                                            {% else %}
                                                            <li><i class="fas fa-thumbs-up text-success me-2"></i>جودة البيانات ممتازة</li>
                                                            {% endif %}

                                                            {% if analysis_data.detected_sector %}
                                                            <li><i class="fas fa-lightbulb text-info me-2"></i>تخصيص التحليل حسب قطاع {{ analysis_data.detected_sector }}</li>
                                                            {% endif %}

                                                            <li><i class="fas fa-chart-bar text-primary me-2"></i>الاستفادة من الرسوم البيانية التفاعلية</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Customs Sector Criteria Section -->
                        {% if analysis_data.detected_sector == 'customs' %}
                        <div class="card mb-4">
                            <div class="card-header bg-gradient">
                                <h5 class="mb-0 text-white">
                                    <i class="fas fa-gavel me-2"></i>
                                    معايير القطاع الجمركي
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>معايير شاملة لتحليل البيانات الجمركية</strong>
                                    <br>
                                    <small>تم تطوير هذه المعايير خصيصاً لقطاع الجمارك لضمان دقة وموثوقية التحليل</small>
                                </div>

                                <!-- Accuracy Analysis Results -->
                                {% if analysis_data.analysis.sector_specific and analysis_data.analysis.sector_specific.get('accuracy_analysis') %}
                                <div class="card mb-4">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0">
                                            <i class="fas fa-check-circle me-2"></i>
                                            تقرير تحليل الدقة - أول 5 سجلات
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <!-- Summary Statistics -->
                                        <div class="row text-center mb-4">
                                            <div class="col-md-3">
                                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                                    <div class="fw-bold text-primary h4 mb-1">{{ analysis_data.analysis.sector_specific.accuracy_analysis.summary.overall_accuracy.total_records }}</div>
                                                    <small class="text-muted">إجمالي السجلات المحللة</small>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                                    <div class="fw-bold text-success h4 mb-1">{{ analysis_data.analysis.sector_specific.accuracy_analysis.summary.overall_accuracy.accurate_records }}</div>
                                                    <small class="text-muted">سجلات دقيقة</small>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="p-3 bg-warning bg-opacity-10 rounded">
                                                    <div class="fw-bold text-warning h4 mb-1">{{ analysis_data.analysis.sector_specific.accuracy_analysis.summary.overall_accuracy.suspicious_records }}</div>
                                                    <small class="text-muted">سجلات مشكوك فيها</small>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                                    <div class="fw-bold text-danger h4 mb-1">{{ analysis_data.analysis.sector_specific.accuracy_analysis.summary.overall_accuracy.error_records }}</div>
                                                    <small class="text-muted">سجلات خاطئة</small>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Accuracy Rate -->
                                        <div class="text-center mb-4">
                                            <div class="p-4 bg-info bg-opacity-10 rounded">
                                                <h3 class="text-info mb-2">{{ "%.1f"|format(analysis_data.analysis.sector_specific.accuracy_analysis.summary.overall_accuracy.accuracy_rate) }}%</h3>
                                                <h5 class="text-muted">معدل الدقة الإجمالي</h5>
                                            </div>
                                        </div>

                                        <!-- Detailed Report Table -->
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover">
                                                <thead class="table-dark">
                                                    <tr>
                                                        <th class="text-center">#</th>
                                                        <th>الكمية</th>
                                                        <th>القيمة الجمركية</th>
                                                        <th>بلد المنشأ</th>
                                                        <th class="text-center">نسبة الدقة</th>
                                                        <th class="text-center">الحالة</th>
                                                        <th>المشاكل المكتشفة</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for record in analysis_data.analysis.sector_specific.accuracy_analysis.detailed_report %}
                                                    <tr class="{% if record.status == 'دقيق' %}table-success{% elif record.status == 'مشكوك فيه' %}table-warning{% else %}table-danger{% endif %}">
                                                        <td class="text-center fw-bold">{{ record.row_index }}</td>
                                                        <td>
                                                            <div class="d-flex justify-content-between">
                                                                <span>{{ record.data.quantity if record.data.quantity else 'غير محدد' }}</span>
                                                                <span class="badge {% if record.data.quantity_valid %}bg-success{% else %}bg-danger{% endif %}">
                                                                    {% if record.data.quantity_valid %}صحيح{% else %}خطأ{% endif %}
                                                                </span>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="d-flex justify-content-between">
                                                                <span>{{ record.data.value if record.data.value else 'غير محدد' }}</span>
                                                                <span class="badge {% if record.data.value_valid %}bg-success{% else %}bg-danger{% endif %}">
                                                                    {% if record.data.value_valid %}صحيح{% else %}خطأ{% endif %}
                                                                </span>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="d-flex justify-content-between">
                                                                <span>{{ record.data.origin if record.data.origin else 'غير محدد' }}</span>
                                                                <span class="badge {% if record.data.origin_valid %}bg-success{% else %}bg-danger{% endif %}">
                                                                    {% if record.data.origin_valid %}صحيح{% else %}خطأ{% endif %}
                                                                </span>
                                                            </div>
                                                        </td>
                                                        <td class="text-center">
                                                            <span class="badge bg-primary">{{ "%.1f"|format(record.accuracy_percentage) }}%</span>
                                                        </td>
                                                        <td class="text-center">
                                                            <span class="badge {% if record.status == 'دقيق' %}bg-success{% elif record.status == 'مشكوك فيه' %}bg-warning{% else %}bg-danger{% endif %}">
                                                                {{ record.status }}
                                                            </span>
                                                        </td>
                                                        <td>
                                                            {% if record.issues %}
                                                                <ul class="list-unstyled mb-0">
                                                                    {% for issue in record.issues %}
                                                                    <li class="text-danger small">
                                                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                                                        {{ issue }}
                                                                    </li>
                                                                    {% endfor %}
                                                                </ul>
                                                            {% else %}
                                                                <span class="text-success small">
                                                                    <i class="fas fa-check-circle me-1"></i>
                                                                    لا توجد مشاكل
                                                                </span>
                                                            {% endif %}
                                                        </td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>

                                        <!-- Export Options -->
                                        <div class="text-center mt-3">
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-outline-primary" onclick="exportAccuracyReportToCSV()">
                                                    <i class="fas fa-file-csv me-1"></i>تصدير CSV
                                                </button>
                                                <button type="button" class="btn btn-outline-success" onclick="exportAccuracyReportToExcel()">
                                                    <i class="fas fa-file-excel me-1"></i>تصدير Excel
                                                </button>
                                                <button type="button" class="btn btn-outline-danger" onclick="exportAccuracyReportToPDF()">
                                                    <i class="fas fa-file-pdf me-1"></i>تصدير PDF
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}

                                <div class="row">
                                    <!-- معايير جودة البيانات -->
                                    <div class="col-md-6 mb-4">
                                        <div class="card h-100">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-check-circle me-2"></i>
                                                    1. معايير جودة البيانات
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <ul class="list-unstyled">
                                                    <li class="mb-3">
                                                        <i class="fas fa-bullseye text-success me-2"></i>
                                                        <strong>الدقة (Accuracy):</strong>
                                                        <br><small class="text-muted">التأكد أن البيانات مطابقة للواقع (مثلاً: الكمية، القيمة، بلد المنشأ)</small>
                                                    </li>
                                                    <li class="mb-3">
                                                        <i class="fas fa-puzzle-piece text-info me-2"></i>
                                                        <strong>الاكتمال (Completeness):</strong>
                                                        <br><small class="text-muted">عدم وجود بيانات ناقصة (كود البيان، رقم الضريبي، تفاصيل المخلص)</small>
                                                    </li>
                                                    <li class="mb-3">
                                                        <i class="fas fa-link text-warning me-2"></i>
                                                        <strong>الاتساق (Consistency):</strong>
                                                        <br><small class="text-muted">تطابق القيم بين الجداول المختلفة (مثلاً رقم البيان في جدولين مختلفين)</small>
                                                    </li>
                                                    <li class="mb-3">
                                                        <i class="fas fa-clock text-primary me-2"></i>
                                                        <strong>الحداثة (Timeliness):</strong>
                                                        <br><small class="text-muted">أن تكون البيانات محدثة ومتزامنة مع العمليات الجمركية</small>
                                                    </li>
                                                    <li class="mb-3">
                                                        <i class="fas fa-fingerprint text-danger me-2"></i>
                                                        <strong>الفريدة (Uniqueness):</strong>
                                                        <br><small class="text-muted">عدم تكرار السجلات لنفس الشحنة أو المستورد</small>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- معايير التحليل الجمركي والإحصائي -->
                                    <div class="col-md-6 mb-4">
                                        <div class="card h-100">
                                            <div class="card-header bg-success text-white">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-chart-line me-2"></i>
                                                    2. معايير التحليل الجمركي والإحصائي
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <ul class="list-unstyled">
                                                    <li class="mb-3">
                                                        <i class="fas fa-chart-pie text-primary me-2"></i>
                                                        <strong>توزيع البيانات:</strong>
                                                        <br><small class="text-muted">عدد البيانات حسب المنفذ، نوع البيان، المستوردين، المخلصين</small>
                                                    </li>
                                                    <li class="mb-3">
                                                        <i class="fas fa-calendar-alt text-info me-2"></i>
                                                        <strong>الأنماط الزمنية:</strong>
                                                        <br><small class="text-muted">حركة البيانات يومياً/شهرياً/سنوياً (لتوقع المواسم)</small>
                                                    </li>
                                                    <li class="mb-3">
                                                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                                        <strong>المخاطر (Risk Indicators):</strong>
                                                        <br><small class="text-muted">مثل بيانات بقيم غير منطقية (سلعة رخيصة بقيمة كبيرة، أو العكس)</small>
                                                    </li>
                                                    <li class="mb-3">
                                                        <i class="fas fa-balance-scale text-success me-2"></i>
                                                        <strong>المقارنة المرجعية (Benchmarking):</strong>
                                                        <br><small class="text-muted">مقارنة الواردات والصادرات مع بيانات السنوات السابقة</small>
                                                    </li>
                                                    <li class="mb-3">
                                                        <i class="fas fa-search text-danger me-2"></i>
                                                        <strong>الكشف عن التكرار أو الاحتيال:</strong>
                                                        <br><small class="text-muted">بيانات بأسماء مختلفة لكن بنفس الرقم الضريبي أو العنوان</small>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <!-- معايير الجانب الجمركي والقانوني -->
                                    <div class="col-md-6 mb-4">
                                        <div class="card h-100">
                                            <div class="card-header bg-warning text-dark">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-balance-scale me-2"></i>
                                                    3. معايير الجانب الجمركي والقانوني
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <ul class="list-unstyled">
                                                    <li class="mb-3">
                                                        <i class="fas fa-tags text-primary me-2"></i>
                                                        <strong>التصنيف الجمركي (HS Code):</strong>
                                                        <br><small class="text-muted">التأكد من صحة استخدام رمز النظام المنسق للسلع</small>
                                                    </li>
                                                    <li class="mb-3">
                                                        <i class="fas fa-dollar-sign text-success me-2"></i>
                                                        <strong>القيمة الجمركية:</strong>
                                                        <br><small class="text-muted">مطابقة القيمة المصرح بها مع القيمة المرجعية العالمية</small>
                                                    </li>
                                                    <li class="mb-3">
                                                        <i class="fas fa-calculator text-info me-2"></i>
                                                        <strong>الضرائب والرسوم:</strong>
                                                        <br><small class="text-muted">التأكد من صحة حساب الرسوم الجمركية والضريبة المضافة</small>
                                                    </li>
                                                    <li class="mb-3">
                                                        <i class="fas fa-globe text-warning me-2"></i>
                                                        <strong>المنشأ:</strong>
                                                        <br><small class="text-muted">مطابقة بلد المنشأ مع الاتفاقيات التجارية (مثلاً إعفاءات أو رسوم إضافية)</small>
                                                    </li>
                                                    <li class="mb-3">
                                                        <i class="fas fa-shield-alt text-danger me-2"></i>
                                                        <strong>الضوابط الأمنية:</strong>
                                                        <br><small class="text-muted">اكتشاف السلع الممنوعة أو الخاضعة للرقابة الخاصة</small>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- معايير التحليل الذكي والتنبؤ -->
                                    <div class="col-md-6 mb-4">
                                        <div class="card h-100">
                                            <div class="card-header bg-info text-white">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-brain me-2"></i>
                                                    4. معايير التحليل الذكي والتنبؤ
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <ul class="list-unstyled">
                                                    <li class="mb-3">
                                                        <i class="fas fa-tachometer-alt text-primary me-2"></i>
                                                        <strong>مؤشرات الأداء (KPIs):</strong>
                                                        <br><small class="text-muted">عدد البيانات المُنجزة يومياً، متوسط زمن التخليص، حجم الإيرادات</small>
                                                    </li>
                                                    <li class="mb-3">
                                                        <i class="fas fa-crystal-ball text-success me-2"></i>
                                                        <strong>التحليل التنبؤي:</strong>
                                                        <br><small class="text-muted">التنبؤ بحجم البيانات المستقبلية، والكشف عن محاولات التهرب المتوقعة</small>
                                                    </li>
                                                    <li class="mb-3">
                                                        <i class="fas fa-project-diagram text-info me-2"></i>
                                                        <strong>تحليل الشبكات:</strong>
                                                        <br><small class="text-muted">الربط بين المستوردين والمخلصين والمصدرين لكشف العلاقات المشبوهة</small>
                                                    </li>
                                                    <li class="mb-3">
                                                        <i class="fas fa-robot text-warning me-2"></i>
                                                        <strong>استخدام الذكاء الاصطناعي:</strong>
                                                        <br><small class="text-muted">لاكتشاف أنماط التهريب أو تضخيم/تخفيض القيم الجمركية</small>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Summary Section -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <h6 class="text-primary mb-3">
                                                    <i class="fas fa-clipboard-check me-2"></i>
                                                    ملخص معايير القطاع الجمركي
                                                </h6>
                                                <div class="row text-center">
                                                    <div class="col-md-3">
                                                        <div class="p-3 bg-primary bg-opacity-10 rounded">
                                                            <div class="fw-bold text-primary h4 mb-1">5</div>
                                                            <small class="text-muted">معايير جودة البيانات</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="p-3 bg-success bg-opacity-10 rounded">
                                                            <div class="fw-bold text-success h4 mb-1">5</div>
                                                            <small class="text-muted">معايير التحليل الإحصائي</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="p-3 bg-warning bg-opacity-10 rounded">
                                                            <div class="fw-bold text-warning h4 mb-1">5</div>
                                                            <small class="text-muted">معايير قانونية</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="p-3 bg-info bg-opacity-10 rounded">
                                                            <div class="fw-bold text-info h4 mb-1">4</div>
                                                            <small class="text-muted">معايير ذكية وتنبؤية</small>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="mt-3">
                                                    <p class="text-muted mb-0">
                                                        <i class="fas fa-info-circle me-2"></i>
                                                        هذه المعايير تضمن تحليل شامل ودقيق للبيانات الجمركية وفقاً لأفضل الممارسات العالمية
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Customs KPI Dashboard Section -->
                        {% if analysis_data.detected_sector == 'customs' and analysis_data.analysis.sector_specific %}
                        <div class="card mb-4">
                            <div class="card-header bg-gradient">
                                <h5 class="mb-0 text-white">
                                    <i class="fas fa-tachometer-alt me-2"></i>
                                    لوحة مؤشرات الأداء الجمركية (KPIs)
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <i class="fas fa-chart-line me-2"></i>
                                    <strong>مؤشرات الأداء الرئيسية للقطاع الجمركي</strong>
                                    <br>
                                    <small>عرض بصري تفاعلي للمؤشرات المحسوبة من البيانات الجمركية</small>
                                </div>

                                <!-- KPI Cards Row -->
                                <div class="row mb-4">
                                    {% set kpis = analysis_data.analysis.sector_specific %}
                                    {% if kpis.data_quality_kpis %}
                                        {% for kpi_key, kpi_data in kpis.data_quality_kpis.items() %}
                                        <div class="col-md-3 mb-3">
                                            <div class="card h-100">
                                                <div class="card-body text-center">
                                                    <div class="mb-2">
                                                        {% if '%' in kpi_data.unit %}
                                                            <div class="progress-circle" data-percentage="{{ kpi_data.value }}">
                                                                <span class="progress-text">{{ "%.1f"|format(kpi_data.value) }}{{ kpi_data.unit }}</span>
                                                            </div>
                                                        {% else %}
                                                            <h3 class="text-primary mb-1">{{ "%.1f"|format(kpi_data.value) if kpi_data.value|round != kpi_data.value else kpi_data.value|round }}{{ kpi_data.unit }}</h3>
                                                        {% endif %}
                                                    </div>
                                                    <h6 class="card-title">{{ kpi_data.label }}</h6>
                                                    <small class="text-muted">{{ kpi_data.description }}</small>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    {% endif %}
                                </div>

                                <!-- Charts Section -->
                                <div class="row">
                                    <!-- Bar Chart - Port Distribution -->
                                    {% if kpis.customs_analysis_kpis and kpis.customs_analysis_kpis.port_distribution %}
                                    <div class="col-md-6 mb-4">
                                        <div class="card chart-card">
                                            <div class="card-header chart-header">
                                                <h6 class="mb-0 text-center text-white">
                                                    <i class="fas fa-chart-bar me-2"></i>
                                                    توزيع البيانات حسب المنفذ
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="chart-wrapper-large">
                                                    <canvas id="port-distribution-chart"></canvas>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}

                                    <!-- Pie Chart - Data Quality Ratios -->
                                    <div class="col-md-6 mb-4">
                                        <div class="card chart-card">
                                            <div class="card-header chart-header">
                                                <h6 class="mb-0 text-center text-white">
                                                    <i class="fas fa-chart-pie me-2"></i>
                                                    مؤشرات جودة البيانات
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="chart-wrapper-large">
                                                    <canvas id="data-quality-chart"></canvas>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Additional KPI Details -->
                                <div class="row">
                                    <!-- Customs Analysis KPIs -->
                                    {% if kpis.customs_analysis_kpis %}
                                    <div class="col-md-6 mb-4">
                                        <div class="card">
                                            <div class="card-header bg-success text-white">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-chart-line me-2"></i>
                                                    مؤشرات التحليل الجمركي
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                {% for kpi_key, kpi_data in kpis.customs_analysis_kpis.items() %}
                                                {% if kpi_key != 'port_distribution' %}
                                                <div class="d-flex justify-content-between align-items-center mb-3">
                                                    <div>
                                                        <strong>{{ kpi_data.label }}</strong>
                                                        <br><small class="text-muted">{{ kpi_data.description }}</small>
                                                    </div>
                                                    <div class="text-end">
                                                        <span class="badge bg-success fs-6">
                                                            {% if kpi_data.unit == '%' %}
                                                                {{ "%.1f"|format(kpi_data.value) }}{{ kpi_data.unit }}
                                                            {% elif kpi_data.unit == 'ريال' %}
                                                                {{ "{:,.0f}".format(kpi_data.value) }} {{ kpi_data.unit }}
                                                            {% else %}
                                                                {{ kpi_data.value }}{{ kpi_data.unit }}
                                                            {% endif %}
                                                        </span>
                                                    </div>
                                                </div>
                                                {% endif %}
                                                {% endfor %}
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}

                                    <!-- Legal and Smart KPIs -->
                                    <div class="col-md-6 mb-4">
                                        <div class="row">
                                            <!-- Legal KPIs -->
                                            {% if kpis.legal_kpis %}
                                            <div class="col-12 mb-3">
                                                <div class="card">
                                                    <div class="card-header bg-warning text-dark">
                                                        <h6 class="mb-0">
                                                            <i class="fas fa-balance-scale me-2"></i>
                                                            مؤشرات الامتثال القانوني
                                                        </h6>
                                                    </div>
                                                    <div class="card-body">
                                                        {% for kpi_key, kpi_data in kpis.legal_kpis.items() %}
                                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                                            <small>{{ kpi_data.label }}</small>
                                                            <span class="badge bg-warning text-dark">
                                                                {{ "%.1f"|format(kpi_data.value) }}{{ kpi_data.unit }}
                                                            </span>
                                                        </div>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                            </div>
                                            {% endif %}

                                            <!-- Smart KPIs -->
                                            {% if kpis.smart_kpis %}
                                            <div class="col-12">
                                                <div class="card">
                                                    <div class="card-header bg-info text-white">
                                                        <h6 class="mb-0">
                                                            <i class="fas fa-brain me-2"></i>
                                                            مؤشرات ذكية وتنبؤية
                                                        </h6>
                                                    </div>
                                                    <div class="card-body">
                                                        {% for kpi_key, kpi_data in kpis.smart_kpis.items() %}
                                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                                            <small>{{ kpi_data.label }}</small>
                                                            <span class="badge bg-info">
                                                                {% if kpi_data.unit == 'يوم' %}
                                                                    {{ "%.1f"|format(kpi_data.value) }} {{ kpi_data.unit }}
                                                                {% elif kpi_data.unit == '%' %}
                                                                    {{ "%.1f"|format(kpi_data.value) }}{{ kpi_data.unit }}
                                                                {% else %}
                                                                    {{ kpi_data.value }} {{ kpi_data.unit }}
                                                                {% endif %}
                                                            </span>
                                                        </div>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <!-- Summary Dashboard -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h6 class="text-center mb-3">
                                                    <i class="fas fa-clipboard-check me-2"></i>
                                                    ملخص مؤشرات الأداء الجمركية
                                                </h6>
                                                <div class="row text-center">
                                                    <div class="col-md-3">
                                                        <div class="p-3 bg-primary bg-opacity-10 rounded">
                                                            <div class="fw-bold text-primary h4 mb-1">
                                                                {% if kpis.data_quality_kpis and kpis.data_quality_kpis.completeness_ratio %}
                                                                    {{ "%.1f"|format(kpis.data_quality_kpis.completeness_ratio.value) }}%
                                                                {% else %}
                                                                    0%
                                                                {% endif %}
                                                            </div>
                                                            <small class="text-muted">جودة البيانات</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="p-3 bg-success bg-opacity-10 rounded">
                                                            <div class="fw-bold text-success h4 mb-1">
                                                                {% if kpis.customs_analysis_kpis and kpis.customs_analysis_kpis.avg_declaration_value %}
                                                                    {{ "{:,.0f}".format(kpis.customs_analysis_kpis.avg_declaration_value.value) }}
                                                                {% else %}
                                                                    0
                                                                {% endif %}
                                                            </div>
                                                            <small class="text-muted">متوسط القيمة</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="p-3 bg-warning bg-opacity-10 rounded">
                                                            <div class="fw-bold text-warning h4 mb-1">
                                                                {% if kpis.legal_kpis and kpis.legal_kpis.hs_code_validity_ratio %}
                                                                    {{ "%.1f"|format(kpis.legal_kpis.hs_code_validity_ratio.value) }}%
                                                                {% else %}
                                                                    0%
                                                                {% endif %}
                                                            </div>
                                                            <small class="text-muted">التصنيف الصحيح</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="p-3 bg-info bg-opacity-10 rounded">
                                                            <div class="fw-bold text-info h4 mb-1">
                                                                {% if kpis.smart_kpis and kpis.smart_kpis.processing_efficiency %}
                                                                    {{ "%.1f"|format(kpis.smart_kpis.processing_efficiency.value) }}%
                                                                {% else %}
                                                                    0%
                                                                {% endif %}
                                                            </div>
                                                            <small class="text-muted">كفاءة المعالجة</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {% endif %}

                        <!-- Machine Learning Predictions Section -->
                        {% if analysis_data.analysis and analysis_data.analysis.ml_predictions %}
                        <div class="card mb-4">
                            <div class="card-header bg-gradient">
                                <h5 class="mb-0 text-white">
                                    <i class="fas fa-brain me-2"></i>
                                    تنبؤات التعلم الآلي
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <i class="fas fa-robot me-2"></i>
                                    <strong>تم إنشاء {{ analysis_data.analysis.ml_predictions|length }} تنبؤ للأعمدة</strong>
                                    <br>
                                    <small>تحليل ذكي للأنماط والتنبؤات المستقبلية للبيانات</small>
                                </div>

                                <div class="row">
                                    {% for col_name, prediction in analysis_data.analysis.ml_predictions.items() %}
                                    <div class="col-md-6 mb-4">
                                        <div class="card h-100">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-chart-line me-2"></i>
                                                    {{ col_name }}
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <!-- Prediction Type -->
                                                <div class="mb-3">
                                                    <span class="badge bg-info mb-2">{{ prediction.get('data_type', 'غير محدد') }}</span>
                                                    {% if prediction.get('total_values') %}
                                                    <small class="text-muted d-block">إجمالي القيم: {{ prediction.total_values }}</small>
                                                    {% endif %}
                                                </div>

                                                <!-- Key Insights -->
                                                {% if prediction.get('insights') %}
                                                <div class="mb-3">
                                                    <h6 class="text-primary">الرؤى الرئيسية:</h6>
                                                    <ul class="list-unstyled">
                                                        {% for insight in prediction.insights %}
                                                        <li class="mb-1">
                                                            <i class="fas fa-lightbulb text-warning me-2"></i>
                                                            {{ insight }}
                                                        </li>
                                                        {% endfor %}
                                                    </ul>
                                                </div>
                                                {% endif %}

                                                <!-- Risk Analysis -->
                                                {% if prediction.get('risk_analysis') %}
                                                <div class="mb-3">
                                                    <h6 class="text-danger">تحليل المخاطر:</h6>
                                                    <div class="row text-center">
                                                        {% if prediction.risk_analysis.get('risks') %}
                                                        <div class="col-12">
                                                            <div class="p-2 bg-danger bg-opacity-10 rounded">
                                                                <small class="text-muted d-block">المخاطر المكتشفة:</small>
                                                                <ul class="list-unstyled mb-0">
                                                                    {% for risk in prediction.risk_analysis.risks %}
                                                                    <li class="small text-danger">{{ risk }}</li>
                                                                    {% endfor %}
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        {% endif %}
                                                        {% if prediction.risk_analysis.get('opportunities') %}
                                                        <div class="col-12 mt-2">
                                                            <div class="p-2 bg-success bg-opacity-10 rounded">
                                                                <small class="text-muted d-block">الفرص المتاحة:</small>
                                                                <ul class="list-unstyled mb-0">
                                                                    {% for opp in prediction.risk_analysis.opportunities %}
                                                                    <li class="small text-success">{{ opp }}</li>
                                                                    {% endfor %}
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                                {% endif %}

                                                <!-- Numeric Predictions -->
                                                {% if prediction.get('data_type') == 'numeric' and prediction.get('predictions') %}
                                                <div class="mb-3">
                                                    <h6 class="text-success">التنبؤات الرقمية:</h6>
                                                    <div class="row">
                                                        {% for pred_key, pred_value in prediction.predictions.items() %}
                                                        <div class="col-6 mb-2">
                                                            <div class="p-2 bg-success bg-opacity-10 rounded text-center">
                                                                <small class="text-muted d-block">{{ pred_key }}</small>
                                                                <strong class="text-success">
                                                                    {% if pred_value is number %}
                                                                        {{ "%.2f"|format(pred_value) }}
                                                                    {% else %}
                                                                        {{ pred_value }}
                                                                    {% endif %}
                                                                </strong>
                                                            </div>
                                                        </div>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                                {% endif %}

                                                <!-- Categorical Predictions -->
                                                {% if prediction.get('data_type') == 'categorical' and prediction.get('predictions') %}
                                                <div class="mb-3">
                                                    <h6 class="text-info">التنبؤات الفئوية:</h6>
                                                    <div class="row">
                                                        {% for category, data in prediction.predictions.items() %}
                                                        <div class="col-12 mb-2">
                                                            <div class="d-flex justify-content-between align-items-center p-2 bg-info bg-opacity-10 rounded">
                                                                <span class="fw-medium">{{ data.get('most_likely_next', category) }}</span>
                                                                <span class="badge bg-info">{{ data.get('probability', '0%') }}</span>
                                                            </div>
                                                        </div>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                                {% endif %}

                                                <!-- Temporal Predictions -->
                                                {% if prediction.get('data_type') == 'temporal' and prediction.get('predictions') %}
                                                <div class="mb-3">
                                                    <h6 class="text-warning">التنبؤات الزمنية:</h6>
                                                    <div class="row">
                                                        {% for time_key, time_value in prediction.predictions.items() %}
                                                        <div class="col-6 mb-2">
                                                            <div class="p-2 bg-warning bg-opacity-10 rounded text-center">
                                                                <small class="text-muted d-block">{{ time_key }}</small>
                                                                <strong class="text-warning">
                                                                    {% if time_value is number %}
                                                                        {{ "%.2f"|format(time_value) }}
                                                                    {% else %}
                                                                        {{ time_value }}
                                                                    {% endif %}
                                                                </strong>
                                                            </div>
                                                        </div>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>

                                <!-- ML Summary Statistics -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-chart-pie me-2"></i>
                                                    إحصائيات التنبؤات
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row text-center">
                                                    <div class="col-md-3">
                                                        <div class="p-3 bg-primary bg-opacity-10 rounded">
                                                            <h5 class="text-primary mb-1">{{ analysis_data.analysis.ml_predictions|length }}</h5>
                                                            <small class="text-muted">إجمالي التنبؤات</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="p-3 bg-success bg-opacity-10 rounded">
                                                            <h5 class="text-success mb-1">
                                                                {% set numeric_count = 0 %}
                                                                {% for pred in analysis_data.analysis.ml_predictions.values() %}
                                                                    {% if pred.get('data_type') == 'numeric' %}
                                                                        {% set numeric_count = numeric_count + 1 %}
                                                                    {% endif %}
                                                                {% endfor %}
                                                                {{ numeric_count }}
                                                            </h5>
                                                            <small class="text-muted">تنبؤات رقمية</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="p-3 bg-info bg-opacity-10 rounded">
                                                            <h5 class="text-info mb-1">
                                                                {% set categorical_count = 0 %}
                                                                {% for pred in analysis_data.analysis.ml_predictions.values() %}
                                                                    {% if pred.get('data_type') == 'categorical' %}
                                                                        {% set categorical_count = categorical_count + 1 %}
                                                                    {% endif %}
                                                                {% endfor %}
                                                                {{ categorical_count }}
                                                            </h5>
                                                            <small class="text-muted">تنبؤات فئوية</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="p-3 bg-warning bg-opacity-10 rounded">
                                                            <h5 class="text-warning mb-1">
                                                                {% set temporal_count = 0 %}
                                                                {% for pred in analysis_data.analysis.ml_predictions.values() %}
                                                                    {% if pred.get('data_type') == 'temporal' %}
                                                                        {% set temporal_count = temporal_count + 1 %}
                                                                    {% endif %}
                                                                {% endfor %}
                                                                {{ temporal_count }}
                                                            </h5>
                                                            <small class="text-muted">تنبؤات زمنية</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dark Mode CSS -->
    <link rel="stylesheet" href="/static/css/dark-mode.css">

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Dark Mode Toggle Script -->
    <script>
        // Dark Mode Toggle Functionality
        class DarkModeManager {
            constructor() {
                this.themeToggle = null;
                this.currentTheme = localStorage.getItem('theme') || 'light';
                this.init();
            }

            init() {
                this.createToggleButton();
                this.applyTheme(this.currentTheme);
                this.bindEvents();
            }

            createToggleButton() {
                // Create toggle button
                this.themeToggle = document.createElement('button');
                this.themeToggle.className = 'theme-toggle';
                this.themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
                this.themeToggle.title = 'تبديل الوضع المظلم';
                this.themeToggle.setAttribute('aria-label', 'تبديل الوضع المظلم');

                // Add to page
                document.body.appendChild(this.themeToggle);
            }

            applyTheme(theme) {
                document.documentElement.setAttribute('data-theme', theme);
                this.currentTheme = theme;
                localStorage.setItem('theme', theme);

                // Update button icon
                if (this.themeToggle) {
                    const icon = this.themeToggle.querySelector('i');
                    if (theme === 'dark') {
                        icon.className = 'fas fa-sun';
                        this.themeToggle.title = 'تبديل الوضع الفاتح';
                    } else {
                        icon.className = 'fas fa-moon';
                        this.themeToggle.title = 'تبديل الوضع المظلم';
                    }
                }

                // Dispatch custom event for other components
                window.dispatchEvent(new CustomEvent('themeChanged', {
                    detail: { theme: theme }
                }));
            }

            toggleTheme() {
                const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
                this.applyTheme(newTheme);
            }

            bindEvents() {
                if (this.themeToggle) {
                    this.themeToggle.addEventListener('click', () => {
                        this.toggleTheme();
                    });
                }

                // Listen for system theme changes
                window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                    if (!localStorage.getItem('theme')) {
                        this.applyTheme(e.matches ? 'dark' : 'light');
                    }
                });
            }
        }

        // Initialize dark mode when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            new DarkModeManager();
        });

        // Performance monitoring for dark mode
        window.addEventListener('themeChanged', function(e) {
            console.log(`Theme changed to: ${e.detail.theme}`);
            if (typeof getPerformanceReport === 'function') {
                getPerformanceReport();
            }
        });
    </script>

    <!-- Analysis Data for JavaScript -->
    <script id="analysisData" type="application/json">
        {{ analysis_data | tojson if analysis_data else '{}' }}
    </script>

    <!-- Make analysis data available globally for debugging -->
    <script>
        // Parse analysis data for debugging
        const analysisDataElement = document.getElementById('analysisData');
        if (analysisDataElement) {
            try {
                window.analysisData = JSON.parse(analysisDataElement.textContent);
            } catch (e) {
                console.error('Error parsing analysis data:', e);
                window.analysisData = {};
            }
        } else {
            window.analysisData = {};
        }
    </script>

    <!-- Merge Analysis Data for JavaScript -->
    <script id="mergeAnalysisData" type="application/json">
        {% if analysis_data and analysis_data.analysis and analysis_data.analysis.sector_specific and analysis_data.analysis.sector_specific.get('merge_analysis') and analysis_data.analysis.sector_specific.merge_analysis.get('merge_candidates') %}
            {{ analysis_data.analysis.sector_specific.merge_analysis.merge_candidates | tojson }}
        {% else %}
            []
        {% endif %}
    </script>

    <script>
        // Interactive Charts Variables and Functions
        let interactiveCharts = {};

        // Color schemes for interactive charts
        const colorSchemes = {
            default: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'],
            vibrant: ['#FF3366', '#33CCFF', '#FFCC33', '#33FF99', '#CC33FF', '#FF6633', '#3366FF', '#FFFF33', '#FF33CC', '#33FFCC'],
            pastel: ['#FFB3BA', '#BAFFC9', '#BAE1FF', '#FFFFBA', '#FFDFBA', '#E0BBE4', '#957DAD', '#D291BC', '#FEC8D8', '#FFDFD3'],
            dark: ['#2C3E50', '#34495E', '#7F8C8D', '#95A5A6', '#BDC3C7', '#ECF0F1', '#E74C3C', '#E67E22', '#F39C12', '#F1C40F'],
            gradient: ['rgba(255, 99, 132, 0.8)', 'rgba(54, 162, 235, 0.8)', 'rgba(255, 206, 86, 0.8)', 'rgba(75, 192, 192, 0.8)', 'rgba(153, 102, 255, 0.8)']
        };

        function initializeInteractiveCharts() {
            console.log('🎨 Initializing interactive column charts...');

            try {
                // Get data from script tag
                const analysisDataElement = document.getElementById('analysisData');
                if (!analysisDataElement) {
                    console.error('❌ Analysis data element not found');
                    showNoInteractiveDataMessage();
                    return;
                }

                const analysisData = JSON.parse(analysisDataElement.textContent);
                const distributionsData = analysisData.analysis && analysisData.analysis.distributions ? analysisData.analysis.distributions : {};
                const columnAnalysisData = analysisData.analysis && analysisData.analysis.column_analysis ? analysisData.analysis.column_analysis : {};

                if (distributionsData && Object.keys(distributionsData).length > 0) {
                    // Limit the number of interactive charts to prevent performance issues
                    const maxInteractiveCharts = 6;
                    const limitedDistributionsData = {};
                    const limitedColumnAnalysisData = {};

                    Object.keys(distributionsData).slice(0, maxInteractiveCharts).forEach(key => {
                        limitedDistributionsData[key] = distributionsData[key];
                        if (columnAnalysisData[key]) {
                            limitedColumnAnalysisData[key] = columnAnalysisData[key];
                        }
                    });

                    generateInteractiveCharts(limitedDistributionsData, limitedColumnAnalysisData);
                } else {
                    showNoInteractiveDataMessage();
                }
            } catch (error) {
                console.error('Error in initializeInteractiveCharts:', error);
                showNoInteractiveDataMessage();
            }
        }

        function generateInteractiveCharts(distributionsData, columnAnalysisData) {
            const container = document.getElementById('interactiveChartsContainer');
            if (!container) {
                console.error('Interactive charts container not found');
                return;
            }

            try {
                container.innerHTML = '';

                const chartPromises = Object.keys(distributionsData).map((columnName, index) => {
                    return new Promise((resolve) => {
                        try {
                            const columnData = distributionsData[columnName];
                            const analysisData = columnAnalysisData[columnName] || {};

                            if (!columnData || Object.keys(columnData).length === 0) {
                                console.warn(`No data for column ${columnName}`);
                                resolve();
                                return;
                            }

                            const chartHTML = `
                                <div class="col-lg-6 col-xl-4 mb-4">
                                    <div class="chart-card h-100">
                                        <div class="card-header bg-light border-0 py-3">
                                            <h6 class="mb-0 text-center fw-bold text-dark">
                                                <i class="fas fa-chart-line me-2 text-primary"></i>
                                                ${columnName}
                                            </h6>
                                            <div class="text-center mt-2">
                                                <span class="badge bg-primary me-1">${Object.keys(columnData).length} قيمة فريدة</span>
                                                <span class="badge bg-secondary">${analysisData.type || 'نص'}</span>
                                            </div>
                                        </div>
                                        <div class="card-body p-3">
                                            <div class="chart-container">
                                                <canvas id="interactiveChart_${index}"></canvas>
                                            </div>

                                            <!-- Column Stats -->
                                            <div class="mt-3 p-2 bg-light rounded">
                                                <div class="row text-center">
                                                    <div class="col-4">
                                                        <small class="text-muted d-block">القيم الفريدة</small>
                                                        <strong class="text-primary">${Object.keys(columnData).length}</strong>
                                                    </div>
                                                    <div class="col-4">
                                                        <small class="text-muted d-block">المفقودة</small>
                                                        <strong class="text-danger">${analysisData.missing_count || 0}</strong>
                                                    </div>
                                                    <div class="col-4">
                                                        <small class="text-muted d-block">النوع</small>
                                                        <strong class="text-success">${analysisData.type || 'نص'}</strong>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
   
                                    <!-- Analysis Criteria Selection -->
                                    {% if not analysis_data.analysis.sector_specific %}
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="analysisCriteria" class="form-label text-white fw-bold">
                                                <i class="fas fa-list-check me-2"></i>معايير التحليل:
                                            </label>
                                            <select id="analysisCriteria" class="form-select">
                                                <option value="all">جميع المعايير</option>
                                            </select>
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>
                            `;

                            container.insertAdjacentHTML('beforeend', chartHTML);

                            // Create the chart with timeout to prevent blocking
                            setTimeout(() => {
                                try {
                                    createInteractiveChart(`interactiveChart_${index}`, columnData, columnName);
                                    resolve();
                                } catch (error) {
                                    console.error(`Error creating chart for ${columnName}:`, error);
                                    resolve();
                                }
                            }, index * 200); // Stagger chart creation

                        } catch (error) {
                            console.error(`Error generating chart HTML for ${columnName}:`, error);
                            resolve();
                        }
                    });
                });

                // Wait for all charts to be created
                Promise.all(chartPromises).then(() => {
                    console.log('✅ All interactive charts generated successfully');
                }).catch((error) => {
                    console.error('Error in chart generation promises:', error);
                });

            } catch (error) {
                console.error('Error in generateInteractiveCharts:', error);
                container.innerHTML = `
                    <div class="col-12">
                        <div class="alert alert-danger text-center">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>خطأ في إنشاء الرسوم البيانية التفاعلية</strong>
                            <p class="mb-0 mt-2">حدث خطأ أثناء تحميل الرسوم البيانية. يرجى إعادة تحميل الصفحة.</p>
                        </div>
                    </div>
                `;
            }
        }

        function createInteractiveChart(canvasId, data, title) {
            const canvas = document.getElementById(canvasId);
            if (!canvas) return;

            const ctx = canvas.getContext('2d');

            // Prepare data
            const entries = Object.entries(data);
            const maxValues = parseInt(document.getElementById('maxValues')?.value || 10);
            const sortedEntries = entries.sort((a, b) => b[1] - a[1]).slice(0, maxValues);

            const labels = sortedEntries.map(entry => String(entry[0]));
            const values = sortedEntries.map(entry => entry[1]);

            const selectedScheme = document.getElementById('colorScheme')?.value || 'default';
            const colors = colorSchemes[selectedScheme] || colorSchemes.default;

            const chartType = document.getElementById('chartType')?.value || 'bar';

            // Destroy existing chart if exists
            if (interactiveCharts[canvasId]) {
                interactiveCharts[canvasId].destroy();
            }

            // Create new chart
            interactiveCharts[canvasId] = new Chart(ctx, {
                type: chartType === 'horizontalBar' ? 'bar' : chartType,
                data: {
                    labels: labels,
                    datasets: [{
                        label: title,
                        data: values,
                        backgroundColor: colors.slice(0, values.length),
                        borderColor: colors.slice(0, values.length),
                        borderWidth: 2,
                        borderRadius: chartType === 'bar' || chartType === 'horizontalBar' ? 8 : 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: chartType === 'horizontalBar' ? 'y' : 'x',
                    plugins: {
                        legend: {
                            display: chartType === 'pie' || chartType === 'doughnut' || chartType === 'radar'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const total = values.reduce((a, b) => a + b, 0);
                                    const percentage = ((context.parsed.y || context.parsed) / total * 100).toFixed(1);
                                    return `${context.label}: ${context.parsed.y || context.parsed} (${percentage}%)`;
                                }
                            }
                        }
                    },
                    scales: chartType === 'pie' || chartType === 'doughnut' || chartType === 'radar' ? {} : {
                        y: {
                            beginAtZero: true
                        }
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeInOutQuart'
                    }
                }
            });
        }

        function updateAllCharts() {
            const analysisDataElement = document.getElementById('analysisData');
            if (!analysisDataElement) return;

            const analysisData = JSON.parse(analysisDataElement.textContent);
            const distributionsData = analysisData.analysis && analysisData.analysis.distributions ? analysisData.analysis.distributions : {};

            if (distributionsData && Object.keys(distributionsData).length > 0) {
                Object.keys(distributionsData).forEach((columnName, index) => {
                    const columnData = distributionsData[columnName];
                    createInteractiveChart(`interactiveChart_${index}`, columnData, columnName);
                });
            }
        }

        function showNoInteractiveDataMessage() {
            const container = document.getElementById('interactiveChartsContainer');
            if (container) {
                container.innerHTML = `
                    <div class="col-12">
                        <div class="alert alert-warning text-center">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>لا توجد بيانات متاحة للرسوم البيانية التفاعلية</strong>
                            <p class="mb-0 mt-2">يرجى التأكد من رفع ملف بيانات صحيح وإجراء التحليل أولاً.</p>
                        </div>
                    </div>
                `;
            }
        }

        // Export functions
        function exportChartsPNG() {
            alert('🖼️ سيتم تصدير الرسوم البيانية التفاعلية كصور PNG');
        }

        function exportChartsPDF() {
            alert('📄 سيتم إنشاء تقرير PDF شامل للرسوم البيانية التفاعلية');
        }

        function printCharts() {
            alert('🖨️ سيتم طباعة الرسوم البيانية التفاعلية');
        }

        function shareCharts() {
            alert('📤 سيتم مشاركة الرسوم البيانية التفاعلية');
        }

        function exportAllCharts() {
            alert('💾 سيتم تصدير جميع الرسوم البيانية التفاعلية');
        }

        // Merge Analysis Pagination, Search, Filter and Export Functions
        let mergeCurrentPage = 1;
        let mergePageSize = 25;
        let mergeTotalItems = 0;
        let mergeAllData = [];
        let mergeFilteredData = [];
        let mergeSortDirection = {};
        let mergeSearchTerm = '';
        let mergeNoteFilter = '';

        function initializeMergePagination() {
            // Load data from script tag
            const mergeDataElement = document.getElementById('mergeAnalysisData');
            if (mergeDataElement) {
                mergeAllData = JSON.parse(mergeDataElement.textContent);
                mergeFilteredData = [...mergeAllData]; // Initialize filtered data
                mergeTotalItems = mergeAllData.length;

                // Update total count display
                updateMergeTotalCount();
            }

            if (mergeTotalItems > 0) {
                updateMergeTable();
                updateMergePagination();
            }
        }

        function updateMergeTotalCount() {
            const totalCountElement = document.getElementById('mergeTotalCount');
            if (totalCountElement) {
                const totalText = mergeFilteredData.length === mergeAllData.length ?
                    `من أصل ${mergeAllData.length}` :
                    `من أصل ${mergeAllData.length} (مفلتر: ${mergeFilteredData.length})`;
                totalCountElement.textContent = totalText;
            }
        }

        function filterMergeTable() {
            const searchInput = document.getElementById('mergeSearchInput');
            const noteFilter = document.getElementById('mergeNoteFilter');

            mergeSearchTerm = searchInput ? searchInput.value.toLowerCase().trim() : '';
            mergeNoteFilter = noteFilter ? noteFilter.value : '';

            // Apply filters
            mergeFilteredData = mergeAllData.filter(candidate => {
                // Search filter
                const searchMatch = !mergeSearchTerm ||
                    candidate.person_key.toLowerCase().includes(mergeSearchTerm) ||
                    (candidate.numbers && candidate.numbers.some(num => num.toString().includes(mergeSearchTerm)));

                // Note filter
                let noteMatch = true;
                if (mergeNoteFilter) {
                    const note = getCandidateNote(candidate);
                    if (mergeNoteFilter === 'تضارب') {
                        noteMatch = note.includes('تضارب');
                    } else if (mergeNoteFilter === 'يحتاج دمج') {
                        noteMatch = note.includes('يحتاج دمج');
                    } else if (mergeNoteFilter === 'طبيعي') {
                        noteMatch = note.includes('طبيعي');
                    } else if (mergeNoteFilter === 'مراجعة') {
                        noteMatch = note.includes('مراجعة');
                    }
                }

                return searchMatch && noteMatch;
            });

            // Reset to first page when filtering
            mergeCurrentPage = 1;

            updateMergeTotalCount();
            updateMergeTable();
            updateMergePagination();
        }

        function clearMergeSearch() {
            const searchInput = document.getElementById('mergeSearchInput');
            if (searchInput) {
                searchInput.value = '';
                filterMergeTable();
            }
        }

        function resetMergeFilters() {
            const searchInput = document.getElementById('mergeSearchInput');
            const noteFilter = document.getElementById('mergeNoteFilter');

            if (searchInput) searchInput.value = '';
            if (noteFilter) noteFilter.value = '';

            mergeSearchTerm = '';
            mergeNoteFilter = '';
            mergeFilteredData = [...mergeAllData];
            mergeCurrentPage = 1;

            updateMergeTotalCount();
            updateMergeTable();
            updateMergePagination();
        }

        function getCandidateNote(candidate) {
            // Separate numbers by type
            const insuranceNumbers = [];
            const automaticNumbers = [];
            const manualNumbers = [];

            if (candidate.numbers && Array.isArray(candidate.numbers)) {
                candidate.numbers.forEach((number, index) => {
                    const type = candidate.number_types && candidate.number_types[index] ? candidate.number_types[index] : 'غير محدد';
                    if (type === 'تأميني') {
                        insuranceNumbers.push(number);
                    } else if (type === 'آلي' || type === 'عام') {
                        automaticNumbers.push(number);
                    } else if (type === 'يدوي') {
                        manualNumbers.push(number);
                    }
                });
            }

            const allBasicNumbers = [...insuranceNumbers, ...automaticNumbers];

            if (insuranceNumbers.length > 1) {
                return 'تضارب أرقام أساسية';
            } else if (manualNumbers.length > 0 && allBasicNumbers.length > 0) {
                return 'يحتاج دمج';
            } else if (allBasicNumbers.length === 1 && manualNumbers.length === 1) {
                return 'طبيعي';
            } else {
                return 'يحتاج مراجعة';
            }
        }

        function changeMergePageSize() {
            mergePageSize = parseInt(document.getElementById('mergePageSize').value);
            mergeCurrentPage = 1;
            updateMergeTable();
            updateMergePagination();
        }

        function changeMergePage(page) {
            if (page < 1 || page > Math.ceil(mergeFilteredData.length / mergePageSize)) return;
            mergeCurrentPage = page;
            updateMergeTable();
            updateMergePagination();
        }

        function updateMergeTable() {
            const tbody = document.getElementById('mergeTableBody');
            if (!tbody) return;

            tbody.innerHTML = '';

            // Use filtered data
            const dataToShow = mergeFilteredData;

            // Apply pagination to filtered candidates
            const startIndex = (mergeCurrentPage - 1) * mergePageSize;
            const endIndex = startIndex + mergePageSize;
            const pageData = dataToShow.slice(startIndex, endIndex);

            // Update total count for pagination
            mergeTotalItems = dataToShow.length;

            pageData.forEach(candidate => {
                // Separate numbers by type
                const insuranceNumbers = [];
                const automaticNumbers = [];
                const manualNumbers = [];

                candidate.numbers.forEach((number, index) => {
                    const type = candidate.number_types && candidate.number_types[index] ? candidate.number_types[index] : 'غير محدد';
                    if (type === 'تأميني') {
                        insuranceNumbers.push(number);
                    } else if (type === 'آلي' || type === 'عام') {
                        automaticNumbers.push(number);
                    } else if (type === 'يدوي') {
                        manualNumbers.push(number);
                    }
                });

                const allBasicNumbers = [...insuranceNumbers, ...automaticNumbers];

                // Extract birth date
                const birthDateMatch = candidate.person_key.match(/\d{4}-\d{2}-\d{2}/);
                const birthDate = birthDateMatch ? birthDateMatch[0] : 'غير محدد';

                // Determine note
                let note = '';
                if (insuranceNumbers.length > 1) {
                    note = 'تضارب أرقام أساسية';
                } else if (manualNumbers.length > 0 && allBasicNumbers.length > 0) {
                    note = 'أرقام مرتبطة';
                } else {
                    note = 'يحتاج مراجعة';
                }

                const row = document.createElement('tr');

                // Full Name (الاسم الكامل)
                const fullNameCell = document.createElement('td');
                fullNameCell.innerHTML = `<div class="fw-bold">${candidate.person_key}</div>`;
                row.appendChild(fullNameCell);

                // Birth Date (تاريخ الميلاد)
                const birthDateCell = document.createElement('td');
                birthDateCell.textContent = birthDate;
                row.appendChild(birthDateCell);

                // Basic Numbers (الأرقام الأساسية)
                const basicNumbersCell = document.createElement('td');
                basicNumbersCell.className = 'text-center';
                basicNumbersCell.innerHTML = allBasicNumbers.join('<br>') || '';
                row.appendChild(basicNumbersCell);

                // Manual Numbers (الأرقام اليدوية)
                const manualNumbersCell = document.createElement('td');
                manualNumbersCell.className = 'text-center';
                manualNumbersCell.innerHTML = manualNumbers.join('<br>') || '';
                row.appendChild(manualNumbersCell);

                // Basic Numbers Count (عدد الأرقام الأساسية)
                const basicCountCell = document.createElement('td');
                basicCountCell.className = 'text-center';
                basicCountCell.innerHTML = `<span class="badge bg-primary">${allBasicNumbers.length}</span>`;
                row.appendChild(basicCountCell);

                // Manual Numbers Count (عدد الأرقام اليدوية)
                const manualCountCell = document.createElement('td');
                manualCountCell.className = 'text-center';
                manualCountCell.innerHTML = `<span class="badge bg-info">${manualNumbers.length}</span>`;
                row.appendChild(manualCountCell);

                // Note (الملاحظة)
                const noteCell = document.createElement('td');
                noteCell.className = 'text-center';
                let noteClass = 'badge-warning';
                let emoji = '⚠️';

                if (note.includes('تضارب')) {
                    noteClass = 'badge-danger';
                    emoji = '🚨';
                } else if (note.includes('مرتبطة')) {
                    noteClass = 'badge-success';
                    emoji = '✅';
                } else {
                    noteClass = 'badge-secondary';
                    emoji = '❓';
                }
                noteCell.innerHTML = `<span class="badge ${noteClass}">${emoji} ${note}</span>`;
                row.appendChild(noteCell);

                tbody.appendChild(row);
            });
        }

        // PersonRecord-like functionality in JavaScript
        function createPersonRecordFromCandidate(candidate) {
            // Extract birth date from person_key
            const birthDateMatch = candidate.person_key.match(/\d{4}-\d{2}-\d{2}/);
            const birthDate = birthDateMatch ? birthDateMatch[0] : 'غير محدد';

            // Separate numbers by type
            const mainNumbers = [];
            const manualNumbers = [];

            candidate.numbers.forEach((number, index) => {
                const type = candidate.number_types && candidate.number_types[index] ? candidate.number_types[index] : 'غير محدد';
                if (type === 'تأميني' || type === 'آلي' || type === 'عام') {
                    mainNumbers.push(number);
                } else if (type === 'يدوي') {
                    manualNumbers.push(number);
                }
            });

            return {
                full_name: candidate.person_key,
                birth_date: birthDate,
                main_numbers: mainNumbers,
                manual_numbers: manualNumbers,
                main_numbers_count: mainNumbers.length,
                manual_numbers_count: manualNumbers.length,
                get_note: function() {
                    if (this.main_numbers_count > 1) {
                        return 'تضارب أرقام أساسية';
                    } else if (this.main_numbers_count === 1 && this.manual_numbers_count > 1) {
                        return 'يحتاج دمج';
                    } else if (this.main_numbers_count === 1 && this.manual_numbers_count === 1) {
                        return 'طبيعي';
                    } else {
                        return 'يحتاج مراجعة';
                    }
                }
            };
        }

        function updateMergePagination() {
            const totalPages = Math.ceil(mergeTotalItems / mergePageSize);
            const pagination = document.getElementById('mergePagination');
            pagination.innerHTML = '';

            // Previous button
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${mergeCurrentPage === 1 ? 'disabled' : ''}`;
            prevLi.innerHTML = `
                <a class="page-link" href="#" onclick="changeMergePage(${mergeCurrentPage - 1})" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            `;
            pagination.appendChild(prevLi);

            // Page numbers
            const startPage = Math.max(1, mergeCurrentPage - 2);
            const endPage = Math.min(totalPages, mergeCurrentPage + 2);

            if (startPage > 1) {
                const firstLi = document.createElement('li');
                firstLi.className = 'page-item';
                firstLi.innerHTML = `<a class="page-link" href="#" onclick="changeMergePage(1)">1</a>`;
                pagination.appendChild(firstLi);

                if (startPage > 2) {
                    const ellipsisLi = document.createElement('li');
                    ellipsisLi.className = 'page-item disabled';
                    ellipsisLi.innerHTML = `<span class="page-link">...</span>`;
                    pagination.appendChild(ellipsisLi);
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const pageLi = document.createElement('li');
                pageLi.className = `page-item ${i === mergeCurrentPage ? 'active' : ''}`;
                pageLi.innerHTML = `<a class="page-link" href="#" onclick="changeMergePage(${i})">${i}</a>`;
                pagination.appendChild(pageLi);
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    const ellipsisLi = document.createElement('li');
                    ellipsisLi.className = 'page-item disabled';
                    ellipsisLi.innerHTML = `<span class="page-link">...</span>`;
                    pagination.appendChild(ellipsisLi);
                }

                const lastLi = document.createElement('li');
                lastLi.className = 'page-item';
                lastLi.innerHTML = `<a class="page-link" href="#" onclick="changeMergePage(${totalPages})">${totalPages}</a>`;
                pagination.appendChild(lastLi);
            }

            // Next button
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${mergeCurrentPage === totalPages ? 'disabled' : ''}`;
            nextLi.innerHTML = `
                <a class="page-link" href="#" onclick="changeMergePage(${mergeCurrentPage + 1})" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            `;
            pagination.appendChild(nextLi);
        }

        function changeMergePage(page) {
            if (page < 1 || page > Math.ceil(mergeTotalItems / mergePageSize)) return;
            mergeCurrentPage = page;
            updateMergeTable();
            updateMergePagination();
        }

        function exportMergeAnalysisToCSV() {
            const headers = ['الاسم الكامل', 'تاريخ الميلاد', 'الأرقام الأساسية (تأميني/آلي)', 'الأرقام اليدوية المرتبطة', 'عدد الأرقام الأساسية', 'عدد الأرقام اليدوية', 'الملاحظة'];
            let csvContent = '\uFEFF'; // UTF-8 BOM for proper Arabic support in Excel
            csvContent += headers.join(',') + '\n';

            mergeAllData.forEach(candidate => {
                // Separate numbers by type
                const insuranceNumbers = [];
                const automaticNumbers = [];
                const manualNumbers = [];

                candidate.numbers.forEach((number, index) => {
                    const type = candidate.number_types && candidate.number_types[index] ? candidate.number_types[index] : 'غير محدد';
                    if (type === 'تأميني') {
                        insuranceNumbers.push(number);
                    } else if (type === 'آلي' || type === 'عام') {
                        automaticNumbers.push(number);
                    } else if (type === 'يدوي') {
                        manualNumbers.push(number);
                    }
                });

                const allBasicNumbers = [...insuranceNumbers, ...automaticNumbers];

                // Extract birth date
                const birthDateMatch = candidate.person_key.match(/\d{4}-\d{2}-\d{2}/);
                const birthDate = birthDateMatch ? birthDateMatch[0] : 'غير محدد';

                // Determine note
                let note = '';
                if (insuranceNumbers.length > 1) {
                    note = 'تضارب أرقام أساسية';
                } else if (manualNumbers.length > 0 && allBasicNumbers.length > 0) {
                    note = 'أرقام مرتبطة';
                } else {
                    note = 'يحتاج مراجعة';
                }

                const row = [
                    `"${candidate.person_key}"`,
                    `"${birthDate}"`,
                    `"${allBasicNumbers.join(' – ')}"`,
                    `"${manualNumbers.join(' – ')}"`,
                    `"${allBasicNumbers.length}"`,
                    `"${manualNumbers.length}"`,
                    `"${note}"`
                ];
                csvContent += row.join(',') + '\n';
            });

            downloadFile(csvContent, 'merge_analysis.csv', 'text/csv;charset=utf-8');
        }

        function exportMergeAnalysisToExcel() {
            // For Excel, we'll create a CSV with UTF-8 BOM that Excel can properly read
            const headers = ['الاسم الكامل', 'تاريخ الميلاد', 'الأرقام الأساسية (تأميني/آلي)', 'الأرقام اليدوية المرتبطة', 'عدد الأرقام الأساسية', 'عدد الأرقام اليدوية', 'الملاحظة'];
            let csvContent = '\uFEFF'; // UTF-8 BOM for proper Arabic support in Excel
            csvContent += headers.join('\t') + '\n'; // Use tabs for better Excel compatibility

            mergeAllData.forEach(candidate => {
                // Separate numbers by type
                const insuranceNumbers = [];
                const automaticNumbers = [];
                const manualNumbers = [];

                candidate.numbers.forEach((number, index) => {
                    const type = candidate.number_types && candidate.number_types[index] ? candidate.number_types[index] : 'غير محدد';
                    if (type === 'تأميني') {
                        insuranceNumbers.push(number);
                    } else if (type === 'آلي' || type === 'عام') {
                        automaticNumbers.push(number);
                    } else if (type === 'يدوي') {
                        manualNumbers.push(number);
                    }
                });

                const allBasicNumbers = [...insuranceNumbers, ...automaticNumbers];

                // Extract birth date
                const birthDateMatch = candidate.person_key.match(/\d{4}-\d{2}-\d{2}/);
                const birthDate = birthDateMatch ? birthDateMatch[0] : 'غير محدد';

                // Determine note
                let note = '';
                if (insuranceNumbers.length > 1) {
                    note = 'تضارب أرقام أساسية';
                } else if (manualNumbers.length > 0 && allBasicNumbers.length > 0) {
                    note = 'أرقام مرتبطة';
                } else {
                    note = 'يحتاج مراجعة';
                }

                const row = [
                    candidate.person_key,
                    birthDate,
                    allBasicNumbers.join(' – '),
                    manualNumbers.join(' – '),
                    allBasicNumbers.length,
                    manualNumbers.length,
                    note
                ];
                csvContent += row.join('\t') + '\n';
            });

            // Create blob with UTF-8 encoding
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = 'merge_analysis.csv'; // Keep .csv extension for Excel compatibility
            link.click();
        }

        function exportMergeAnalysisToPDF() {
            // Create HTML content for PDF
            let htmlContent = `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>تحليل الدمج - الأشخاص ذوي الأرقام المتعددة</title>
                    <style>
                        body {
                            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                            direction: rtl;
                            margin: 20px;
                        }
                        table {
                            width: 100%;
                            border-collapse: collapse;
                            margin-top: 20px;
                        }
                        th, td {
                            border: 1px solid #ddd;
                            padding: 8px;
                            text-align: center;
                        }
                        th {
                            background-color: #f2f2f2;
                            font-weight: bold;
                        }
                        .header {
                            text-align: center;
                            margin-bottom: 20px;
                        }
                        .header h1 {
                            color: #333;
                            margin: 0;
                        }
                        .header p {
                            color: #666;
                            margin: 5px 0;
                        }
                        .badge {
                            display: inline-block;
                            padding: 3px 8px;
                            border-radius: 12px;
                            font-size: 12px;
                            font-weight: bold;
                        }
                        .badge-success {
                            background-color: #28a745;
                            color: white;
                        }
                        .badge-primary {
                            background-color: #007bff;
                            color: white;
                        }
                        .badge-info {
                            background-color: #17a2b8;
                            color: white;
                        }
                        .badge-warning {
                            background-color: #ffc107;
                            color: black;
                        }
                        @media print {
                            body { margin: 0; }
                            .no-print { display: none; }
                        }
                    </style>
                </head>
                <body>

                    <table>
                        <thead>
                            <tr>
                                <th>الاسم الكامل</th>
                                <th>تاريخ الميلاد</th>
                                <th>الأرقام الأساسية (تأميني/آلي)</th>
                                <th>الأرقام اليدوية المرتبطة</th>
                                <th>عدد الأرقام الأساسية</th>
                                <th>عدد الأرقام اليدوية</th>
                                <th>الملاحظة</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            // Add table rows in PersonRecord format
            const allRows = [];
            mergeAllData.forEach(candidate => {
                const personRecord = createPersonRecordFromCandidate(candidate);

                // If person has multiple manual numbers, create separate row for each
                if (personRecord.manual_numbers.length > 0) {
                    personRecord.manual_numbers.forEach((manualNumber, index) => {
                        allRows.push({
                            full_name: personRecord.full_name,
                            birth_date: personRecord.birth_date,
                            insurance_number: personRecord.main_numbers.length > 0 ? personRecord.main_numbers[0] : '',
                            manual_number: manualNumber,
                            note: personRecord.get_note()
                        });
                    });
                } else {
                    // If no manual numbers, create one row
                    allRows.push({
                        full_name: personRecord.full_name,
                        birth_date: personRecord.birth_date,
                        insurance_number: personRecord.main_numbers.length > 0 ? personRecord.main_numbers[0] : '',
                        manual_number: '',
                        note: personRecord.get_note()
                    });
                }
            });

            allRows.forEach((rowData, index) => {
                // Determine note class for PDF
                const note = rowData.note;
                let noteClass = 'badge-warning';
                if (note.includes('تضارب')) {
                    noteClass = 'badge-danger';
                } else if (note.includes('يحتاج دمج')) {
                    noteClass = 'badge-warning';
                } else if (note.includes('طبيعي')) {
                    noteClass = 'badge-success';
                } else {
                    noteClass = 'badge-secondary';
                }

                htmlContent += `
                    <tr>
                        <td>${rowData.full_name}</td>
                        <td>${rowData.birth_date}</td>
                        <td>${rowData.insurance_number || ''}</td>
                        <td>${rowData.manual_number || ''}</td>
                        <td><span class="badge ${noteClass}">${note}</span></td>
                    </tr>
                `;
            });

            htmlContent += `
                        </tbody>
                    </table>

                    <div class="no-print" style="margin-top: 20px; text-align: center;">
                        <button onclick="window.print()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            طباعة التقرير
                        </button>
                    </div>
                </body>
                </html>
            `;

            // Open in new window for printing/saving as PDF
            const printWindow = window.open('', '_blank');
            printWindow.document.write(htmlContent);
            printWindow.document.close();

            // Auto-print after a short delay
            setTimeout(() => {
                printWindow.focus();
                printWindow.print();
            }, 500);
        }

        function exportMergeAnalysisToJSON() {
            // Create JSON data structure
            const jsonData = {
                export_info: {
                    export_date: new Date().toISOString(),
                    export_type: 'merge_analysis_json',
                    total_records: mergeAllData.length,
                    filtered_records: mergeFilteredData.length,
                    export_format: 'PersonRecord'
                },
                merge_analysis: {
                    summary: {
                        total_candidates: mergeAllData.length,
                        analyzed_persons: mergeAllData.length,
                        person_columns_found: mergeAllData.length > 0 ? 1 : 0,
                        number_columns_found: mergeAllData.length > 0 ? mergeAllData[0].numbers.length : 0
                    },
                    candidates: []
                }
            };

            // Process each candidate into PersonRecord format
            mergeAllData.forEach((candidate, index) => {
                // Separate numbers by type
                const insuranceNumbers = [];
                const automaticNumbers = [];
                const manualNumbers = [];

                if (candidate.numbers && Array.isArray(candidate.numbers)) {
                    candidate.numbers.forEach((number, numIndex) => {
                        const type = candidate.number_types && candidate.number_types[numIndex] ? candidate.number_types[numIndex] : 'غير محدد';
                        if (type === 'تأميني') {
                            insuranceNumbers.push(number);
                        } else if (type === 'آلي' || type === 'عام') {
                            automaticNumbers.push(number);
                        } else if (type === 'يدوي') {
                            manualNumbers.push(number);
                        }
                    });
                }

                const allBasicNumbers = [...insuranceNumbers, ...automaticNumbers];

                // Extract birth date
                const birthDateMatch = candidate.person_key ? candidate.person_key.match(/\d{4}-\d{2}-\d{2}/) : null;
                const birthDate = birthDateMatch ? birthDateMatch[0] : 'غير محدد';

                // Determine note
                let note = '';
                if (insuranceNumbers.length > 1) {
                    note = 'تضارب أرقام أساسية';
                } else if (manualNumbers.length > 0 && allBasicNumbers.length > 0) {
                    note = 'يحتاج دمج';
                } else if (allBasicNumbers.length === 1 && manualNumbers.length === 1) {
                    note = 'طبيعي';
                } else {
                    note = 'يحتاج مراجعة';
                }

                // Create PersonRecord object
                const personRecord = {
                    id: index + 1,
                    full_name: candidate.person_key || 'غير محدد',
                    birth_date: birthDate,
                    basic_numbers: {
                        insurance: insuranceNumbers,
                        automatic: automaticNumbers,
                        all: allBasicNumbers,
                        count: allBasicNumbers.length
                    },
                    manual_numbers: {
                        numbers: manualNumbers,
                        count: manualNumbers.length
                    },
                    analysis: {
                        note: note,
                        has_conflict: insuranceNumbers.length > 1,
                        needs_merge: manualNumbers.length > 0 && allBasicNumbers.length > 0,
                        is_normal: allBasicNumbers.length === 1 && manualNumbers.length === 1,
                        needs_review: !(insuranceNumbers.length > 1 || (manualNumbers.length > 0 && allBasicNumbers.length > 0) || (allBasicNumbers.length === 1 && manualNumbers.length === 1))
                    },
                    metadata: {
                        original_data: candidate,
                        processed_at: new Date().toISOString(),
                        data_quality_score: calculateDataQuality(candidate)
                    }
                };

                jsonData.merge_analysis.candidates.push(personRecord);
            });

            // Helper function to calculate data quality score
            function calculateDataQuality(candidate) {
                let score = 100;

                // Reduce score for missing data
                if (!candidate.person_key) score -= 20;
                if (!candidate.numbers || candidate.numbers.length === 0) score -= 30;

                // Reduce score for conflicts
                const insuranceNumbers = candidate.numbers.filter((num, index) =>
                    candidate.number_types && candidate.number_types[index] === 'تأميني'
                );
                if (insuranceNumbers.length > 1) score -= 25;

                // Reduce score for missing number types
                if (candidate.number_types && candidate.number_types.includes('غير محدد')) {
                    score -= 10;
                }

                return Math.max(0, score);
            }

            // Convert to JSON string with proper Arabic encoding
            const jsonString = JSON.stringify(jsonData, null, 2);

            // Create and download file
            const blob = new Blob([jsonString], { type: 'application/json;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `merge_analysis_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            // Show success message
            alert('✅ تم تصدير بيانات تحليل الدمج بصيغة JSON بنجاح');
        }

        function downloadFile(content, filename, mimeType) {
            const blob = new Blob([content], { type: mimeType + ';charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = filename;
            link.click();
        }

        // Helper function to create Excel-compatible CSV with proper Arabic encoding
        function createExcelCSV() {
            const headers = ['الاسم الكامل', 'تاريخ الميلاد', 'الأرقام الأساسية (تأميني/آلي)', 'الأرقام اليدوية المرتبطة', 'عدد الأرقام الأساسية', 'عدد الأرقام اليدوية', 'الملاحظة'];
            let csvContent = '\uFEFF'; // UTF-8 BOM
            csvContent += headers.join('\t') + '\n';

            mergeAllData.forEach(candidate => {
                // Separate numbers by type
                const insuranceNumbers = [];
                const automaticNumbers = [];
                const manualNumbers = [];

                candidate.numbers.forEach((number, index) => {
                    const type = candidate.number_types && candidate.number_types[index] ? candidate.number_types[index] : 'غير محدد';
                    if (type === 'تأميني') {
                        insuranceNumbers.push(number);
                    } else if (type === 'آلي' || type === 'عام') {
                        automaticNumbers.push(number);
                    } else if (type === 'يدوي') {
                        manualNumbers.push(number);
                    }
                });

                const allBasicNumbers = [...insuranceNumbers, ...automaticNumbers];

                // Extract birth date
                const birthDateMatch = candidate.person_key.match(/\d{4}-\d{2}-\d{2}/);
                const birthDate = birthDateMatch ? birthDateMatch[0] : 'غير محدد';

                // Determine note
                let note = '';
                if (insuranceNumbers.length > 1) {
                    note = 'تضارب أرقام أساسية';
                } else if (manualNumbers.length > 0 && allBasicNumbers.length > 0) {
                    note = 'أرقام مرتبطة';
                } else {
                    note = 'يحتاج مراجعة';
                }

                const row = [
                    candidate.person_key,
                    birthDate,
                    allBasicNumbers.join(' – '),
                    manualNumbers.join(' – '),
                    allBasicNumbers.length.toString(),
                    manualNumbers.length.toString(),
                    note
                ];
                csvContent += row.join('\t') + '\n';
            });

            return csvContent;
        }


        // Table sorting functionality
        let sortDirection = {};

        function sortTable(columnIndex) {
            const table = document.getElementById('columnsTable');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            const headers = table.querySelectorAll('th.sortable');

            // Reset all sortable headers
            const allSortableHeaders = table.querySelectorAll('th.sortable');
            allSortableHeaders.forEach(header => {
                header.classList.remove('sort-asc', 'sort-desc');
            });

            // Set sort direction
            if (!sortDirection[columnIndex]) {
                sortDirection[columnIndex] = 'asc';
            } else if (sortDirection[columnIndex] === 'asc') {
                sortDirection[columnIndex] = 'desc';
            } else {
                sortDirection[columnIndex] = 'asc';
            }

            // Add sort class to current header
            headers[columnIndex].classList.add(`sort-${sortDirection[columnIndex]}`);

            // Sort rows
            rows.sort((a, b) => {
                const aValue = a.cells[columnIndex].textContent.trim();
                const bValue = b.cells[columnIndex].textContent.trim();

                let aNum, bNum;

                // Try to parse as numbers
                if (columnIndex >= 2 && columnIndex <= 5) { // Numeric columns (2-5)
                    aNum = parseFloat(aValue.replace(/,/g, '').replace('%', ''));
                    bNum = parseFloat(bValue.replace(/,/g, '').replace('%', ''));

                    if (!isNaN(aNum) && !isNaN(bNum)) {
                        return sortDirection[columnIndex] === 'asc' ? aNum - bNum : bNum - aNum;
                    }
                }

                // String comparison
                if (sortDirection[columnIndex] === 'asc') {
                    return aValue.localeCompare(bValue, 'ar');
                } else {
                    return bValue.localeCompare(aValue, 'ar');
                }
            });

            // Reorder rows
            rows.forEach(row => tbody.appendChild(row));
        }

        function sortMergeTable(columnIndex) {
            const table = document.getElementById('personTable');
            const tbody = table.querySelector('tbody');
            const headers = table.querySelectorAll('th.sortable');

            // Reset all headers
            headers.forEach(header => {
                header.classList.remove('sort-asc', 'sort-desc');
            });

            // Set sort direction
            if (!mergeSortDirection[columnIndex]) {
                mergeSortDirection[columnIndex] = 'asc';
            } else if (mergeSortDirection[columnIndex] === 'asc') {
                mergeSortDirection[columnIndex] = 'desc';
            } else {
                mergeSortDirection[columnIndex] = 'asc';
            }

            // Add sort class to current header
            const currentHeader = headers[columnIndex];
            if (currentHeader) {
                currentHeader.classList.add(`sort-${mergeSortDirection[columnIndex]}`);
            }

            // Sort the filtered data array
            mergeFilteredData.sort((a, b) => {
                let aValue, bValue;

                switch (columnIndex) {
                    case 0: // Full Name
                        aValue = a.person_key || '';
                        bValue = b.person_key || '';
                        break;
                    case 1: // Birth Date
                        const aBirthDate = a.person_key ? a.person_key.match(/\d{4}-\d{2}-\d{2}/) : null;
                        const bBirthDate = b.person_key ? b.person_key.match(/\d{4}-\d{2}-\d{2}/) : null;
                        aValue = aBirthDate ? aBirthDate[0] : '';
                        bValue = bBirthDate ? bBirthDate[0] : '';
                        break;
                    case 2: // Basic Numbers
                        const aBasicNumbers = getBasicNumbers(a);
                        const bBasicNumbers = getBasicNumbers(b);
                        aValue = aBasicNumbers.length > 0 ? aBasicNumbers[0] : '';
                        bValue = bBasicNumbers.length > 0 ? bBasicNumbers[0] : '';
                        break;
                    case 3: // Manual Numbers
                        const aManualNumbers = getManualNumbers(a);
                        const bManualNumbers = getManualNumbers(b);
                        aValue = aManualNumbers.length > 0 ? aManualNumbers[0] : '';
                        bValue = bManualNumbers.length > 0 ? bManualNumbers[0] : '';
                        break;
                    case 4: // Document Number
                        aValue = a.document_number || '';
                        bValue = b.document_number || '';
                        break;
                    case 5: // Basic Numbers Count
                        aValue = getBasicNumbers(a).length;
                        bValue = getBasicNumbers(b).length;
                        break;
                    case 6: // Manual Numbers Count
                        aValue = getManualNumbers(a).length;
                        bValue = getManualNumbers(b).length;
                        break;
                    case 7: // Note
                        aValue = getCandidateNote(a);
                        bValue = getCandidateNote(b);
                        break;
                    default:
                        aValue = '';
                        bValue = '';
                }

                // Handle numeric values
                if (columnIndex === 5 || columnIndex === 6) {
                    const aNum = parseInt(aValue) || 0;
                    const bNum = parseInt(bValue) || 0;
                    return mergeSortDirection[columnIndex] === 'asc' ? aNum - bNum : bNum - aNum;
                }

                // Handle string values
                aValue = String(aValue).toLowerCase();
                bValue = String(bValue).toLowerCase();

                if (mergeSortDirection[columnIndex] === 'asc') {
                    return aValue.localeCompare(bValue, 'ar');
                } else {
                    return bValue.localeCompare(aValue, 'ar');
                }
            });

            // Reset to first page and update table
            mergeCurrentPage = 1;
            updateMergeTable();
            updateMergePagination();
        }

        // Search and Filter Functions
        function filterMergeTable() {
            const searchInput = document.getElementById('mergeSearchInput');
            const noteFilter = document.getElementById('mergeNoteFilter');

            const searchTerm = searchInput ? searchInput.value.toLowerCase().trim() : '';
            const noteFilterValue = noteFilter ? noteFilter.value : '';

            console.log('Filtering with:', { searchTerm, noteFilterValue });

            // Filter data
            mergeFilteredData = mergeAllData.filter(candidate => {
                // Search filter
                if (searchTerm) {
                    const personKey = (candidate.person_key || '').toLowerCase();
                    const numbers = Array.isArray(candidate.numbers) ? candidate.numbers.join(' ').toLowerCase() : '';

                    const matchesSearch = personKey.includes(searchTerm) || numbers.includes(searchTerm);
                    if (!matchesSearch) return false;
                }

                // Note filter
                if (noteFilterValue) {
                    const candidateNote = getCandidateNote(candidate);
                    if (candidateNote !== noteFilterValue) return false;
                }

                return true;
            });

            console.log('Filtered data count:', mergeFilteredData.length);

            // Reset to first page and update display
            mergeCurrentPage = 1;
            updateMergeTable();
            updateMergePagination();
            updateMergeTotalCount();
        }

        function clearMergeSearch() {
            const searchInput = document.getElementById('mergeSearchInput');
            if (searchInput) {
                searchInput.value = '';
                filterMergeTable();
            }
        }

        function resetMergeFilters() {
            const searchInput = document.getElementById('mergeSearchInput');
            const noteFilter = document.getElementById('mergeNoteFilter');

            if (searchInput) searchInput.value = '';
            if (noteFilter) noteFilter.value = '';

            filterMergeTable();
        }

        function updateMergeTotalCount() {
            const totalCountElement = document.getElementById('mergeTotalCount');
            if (totalCountElement) {
                const totalText = `من أصل ${mergeAllData.length}`;
                totalCountElement.textContent = totalText;
            }
        }

        function getCandidateNote(candidate) {
            // Separate numbers by type
            const insuranceNumbers = [];
            const automaticNumbers = [];
            const manualNumbers = [];

            if (candidate.numbers && Array.isArray(candidate.numbers)) {
                candidate.numbers.forEach((number, index) => {
                    const type = candidate.number_types && candidate.number_types[index] ? candidate.number_types[index] : 'غير محدد';
                    if (type === 'تأميني') {
                        insuranceNumbers.push(number);
                    } else if (type === 'آلي' || type === 'عام') {
                        automaticNumbers.push(number);
                    } else if (type === 'يدوي') {
                        manualNumbers.push(number);
                    }
                });
            }

            const allBasicNumbers = [...insuranceNumbers, ...automaticNumbers];

            // Determine note
            if (insuranceNumbers.length > 1) {
                return 'تضارب أرقام أساسية';
            } else if (manualNumbers.length > 0 && allBasicNumbers.length > 0) {
                return 'يحتاج دمج';
            } else if (allBasicNumbers.length === 1 && manualNumbers.length === 1) {
                return 'طبيعي';
            } else {
                return 'يحتاج مراجعة';
            }
        }

        function getBasicNumbers(candidate) {
            const insuranceNumbers = [];
            const automaticNumbers = [];

            if (candidate.numbers && Array.isArray(candidate.numbers)) {
                candidate.numbers.forEach((number, index) => {
                    const type = candidate.number_types && candidate.number_types[index] ? candidate.number_types[index] : 'غير محدد';
                    if (type === 'تأميني') {
                        insuranceNumbers.push(number);
                    } else if (type === 'آلي' || type === 'عام') {
                        automaticNumbers.push(number);
                    }
                });
            }

            return [...insuranceNumbers, ...automaticNumbers];
        }

        function getManualNumbers(candidate) {
            const manualNumbers = [];

            if (candidate.numbers && Array.isArray(candidate.numbers)) {
                candidate.numbers.forEach((number, index) => {
                    const type = candidate.number_types && candidate.number_types[index] ? candidate.number_types[index] : 'غير محدد';
                    if (type === 'يدوي') {
                        manualNumbers.push(number);
                    }
                });
            }

            return manualNumbers;
        }

        // Export functions
        function exportTableToCSV() {
            const table = document.getElementById('columnsTable');
            let csv = [];

            // Get headers
            const headers = [];
            table.querySelectorAll('thead th').forEach(th => {
                headers.push(th.textContent.replace('⇅', '').trim());
            });
            csv.push(headers.join(','));

            // Get data
            table.querySelectorAll('tbody tr').forEach(tr => {
                const row = [];
                tr.querySelectorAll('td').forEach(td => {
                    row.push('"' + td.textContent.trim() + '"');
                });
                csv.push(row.join(','));
            });

            // Download
            const csvContent = csv.join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = 'column_analysis.csv';
            link.click();
        }

        function exportTableToExcel() {
            alert('⚠️ تصدير Excel قيد التطوير - استخدم تصدير CSV مؤقتاً');
        }

        function exportTableToPDF() {
            alert('⚠️ تصدير PDF قيد التطوير - استخدم تصدير CSV مؤقتاً');
        }

        // Merge Analysis Export Functions
        function exportMergeAnalysisToCSV() {
            try {
                // Get all merge candidates data from the global analysisData
                let allData = [];
                if (window.analysisData && window.analysisData.analysis && window.analysisData.analysis.sector_specific && window.analysisData.analysis.sector_specific.merge_analysis) {
                    allData = window.analysisData.analysis.sector_specific.merge_analysis.merge_candidates || [];
                }

                if (allData.length === 0) {
                    alert('لا توجد بيانات متاحة للتصدير');
                    return;
                }

                let csv = [];

                // Define headers
                const headers = ['الاسم الكامل', 'تاريخ الميلاد', 'الأرقام الأساسية', 'الأرقام اليدوية', 'رقم الوثيقة', 'عدد الأرقام الأساسية', 'عدد الأرقام اليدوية', 'الملاحظة'];
                csv.push(headers.join(','));

                // Process all data
                allData.forEach(candidate => {
                    // Extract data similar to how it's displayed in the table
                    const insuranceNumbers = candidate.insurance_numbers || [];
                    const manualNumbers = candidate.manual_numbers || [];
                    const allBasicNumbers = [...insuranceNumbers];

                    // Extract birth date from additional_info
                    let birthDate = 'غير محدد';
                    if (candidate.additional_info && Array.isArray(candidate.additional_info)) {
                        for (const info of candidate.additional_info) {
                            if (info && typeof info === 'string' && info !== 'غير محدد' && info.trim() !== '') {
                                const datePatterns = [
                                    /\d{4}-\d{2}-\d{2}/,
                                    /\d{2}\/\d{2}\/\d{4}/,
                                    /\d{2}-\d{2}-\d{4}/,
                                    /\d{4}\/\d{2}\/\d{2}/,
                                    /\d{2}\.\d{2}\.\d{4}/,
                                    /\d{1,2}\s+\w+\s+\d{4}/,
                                    /\w+\s+\d{1,2},?\s+\d{4}/
                                ];
                                for (const pattern of datePatterns) {
                                    const dateMatch = info.match(pattern);
                                    if (dateMatch) {
                                        birthDate = dateMatch[0];
                                        break;
                                    }
                                }
                                if (birthDate !== 'غير محدد') break;
                            }
                        }
                    }

                    // Determine note
                    let note = '';
                    if (insuranceNumbers.length > 1) {
                        note = 'تضارب أرقام أساسية';
                    } else if (manualNumbers.length > 0 && allBasicNumbers.length > 0) {
                        note = 'يحتاج دمج';
                    } else if (allBasicNumbers.length === 1 && manualNumbers.length === 1) {
                        note = 'طبيعي';
                    } else {
                        note = 'يحتاج مراجعة';
                    }

                    const row = [
                        candidate.person_name || 'غير محدد',
                        birthDate,
                        allBasicNumbers.length > 0 ? allBasicNumbers.join(' – ') : 'لا يوجد',
                        manualNumbers.length > 0 ? manualNumbers.join(' – ') : 'لا يوجد',
                        candidate.document_number || 'غير محدد',
                        allBasicNumbers.length.toString(),
                        manualNumbers.length.toString(),
                        note
                    ];

                    // Escape quotes and wrap in quotes
                    const escapedRow = row.map(field => '"' + field.replace(/"/g, '""') + '"');
                    csv.push(escapedRow.join(','));
                });

                // Download
                const csvContent = csv.join('\n');
                const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = 'merge_analysis_all_data.csv';
                link.click();

                console.log('CSV export completed successfully - exported', allData.length, 'records');
                alert('تم تصدير ' + allData.length + ' سجل بنجاح');
            } catch (error) {
                console.error('Error exporting CSV:', error);
                alert('حدث خطأ أثناء تصدير ملف CSV');
            }
        }

        function exportMergeAnalysisToExcel() {
            try {
                // Get all merge candidates data from the global analysisData
                let allData = [];
                if (window.analysisData && window.analysisData.analysis && window.analysisData.analysis.sector_specific && window.analysisData.analysis.sector_specific.merge_analysis) {
                    allData = window.analysisData.analysis.sector_specific.merge_analysis.merge_candidates || [];
                }

                if (allData.length === 0) {
                    alert('لا توجد بيانات متاحة للتصدير');
                    return;
                }

                let tsv = [];

                // Define headers
                const headers = ['الاسم الكامل', 'تاريخ الميلاد', 'الأرقام الأساسية', 'الأرقام اليدوية', 'رقم الوثيقة', 'عدد الأرقام الأساسية', 'عدد الأرقام اليدوية', 'الملاحظة'];
                tsv.push(headers.join('\t'));

                // Process all data (same logic as CSV)
                allData.forEach(candidate => {
                    const insuranceNumbers = candidate.insurance_numbers || [];
                    const manualNumbers = candidate.manual_numbers || [];
                    const allBasicNumbers = [...insuranceNumbers];

                    let birthDate = 'غير محدد';
                    if (candidate.additional_info && Array.isArray(candidate.additional_info)) {
                        for (const info of candidate.additional_info) {
                            if (info && typeof info === 'string' && info !== 'غير محدد' && info.trim() !== '') {
                                const datePatterns = [
                                    /\d{4}-\d{2}-\d{2}/,
                                    /\d{2}\/\d{2}\/\d{4}/,
                                    /\d{2}-\d{2}-\d{4}/,
                                    /\d{4}\/\d{2}\/\d{2}/,
                                    /\d{2}\.\d{2}\.\d{4}/,
                                    /\d{1,2}\s+\w+\s+\d{4}/,
                                    /\w+\s+\d{1,2},?\s+\d{4}/
                                ];
                                for (const pattern of datePatterns) {
                                    const dateMatch = info.match(pattern);
                                    if (dateMatch) {
                                        birthDate = dateMatch[0];
                                        break;
                                    }
                                }
                                if (birthDate !== 'غير محدد') break;
                            }
                        }
                    }

                    let note = '';
                    if (insuranceNumbers.length > 1) {
                        note = 'تضارب أرقام أساسية';
                    } else if (manualNumbers.length > 0 && allBasicNumbers.length > 0) {
                        note = 'يحتاج دمج';
                    } else if (allBasicNumbers.length === 1 && manualNumbers.length === 1) {
                        note = 'طبيعي';
                    } else {
                        note = 'يحتاج مراجعة';
                    }

                    const row = [
                        candidate.person_name || 'غير محدد',
                        birthDate,
                        allBasicNumbers.length > 0 ? allBasicNumbers.join(' – ') : 'لا يوجد',
                        manualNumbers.length > 0 ? manualNumbers.join(' – ') : 'لا يوجد',
                        candidate.document_number || 'غير محدد',
                        allBasicNumbers.length.toString(),
                        manualNumbers.length.toString(),
                        note
                    ];

                    tsv.push(row.join('\t'));
                });

                // Download as TSV (Excel can open it)
                const tsvContent = tsv.join('\n');
                const blob = new Blob(['\uFEFF' + tsvContent], { type: 'text/tab-separated-values;charset=utf-8;' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = 'merge_analysis_all_data.xls';
                link.click();

                console.log('Excel export completed successfully - exported', allData.length, 'records');
                alert('تم تصدير ' + allData.length + ' سجل بنجاح');
            } catch (error) {
                console.error('Error exporting Excel:', error);
                alert('حدث خطأ أثناء تصدير ملف Excel');
            }
        }

        function exportMergeAnalysisToPDF() {
            try {
                // Get all merge candidates data from the global analysisData
                let allData = [];
                if (window.analysisData && window.analysisData.analysis && window.analysisData.analysis.sector_specific && window.analysisData.analysis.sector_specific.merge_analysis) {
                    allData = window.analysisData.analysis.sector_specific.merge_analysis.merge_candidates || [];
                }

                if (allData.length === 0) {
                    alert('لا توجد بيانات متاحة للتصدير');
                    return;
                }

                // Create a simple HTML table for PDF generation
                let htmlContent = `
                    <html>
                    <head>
                        <meta charset="UTF-8">
                        <title>تحليل الدمج - الأشخاص ذوي الأرقام المتعددة</title>
                        <style>
                            body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
                            table { width: 100%; border-collapse: collapse; margin: 20px 0; font-size: 12px; }
                            th, td { border: 1px solid #ddd; padding: 6px; text-align: center; }
                            th { background-color: #f2f2f2; font-weight: bold; }
                            .merge-reason { font-weight: bold; }
                            h1 { text-align: center; color: #333; }
                            .summary { background-color: #e9ecef; padding: 10px; margin-bottom: 20px; border-radius: 5px; }
                            @media print { body { margin: 0; } }
                        </style>
                    </head>
                    <body>
                        <h1>تحليل الدمج - الأشخاص ذوي الأرقام المتعددة</h1>
                        <div class="summary">
                            <strong>إجمالي السجلات:</strong> ${allData.length} |
                            <strong>تاريخ التصدير:</strong> ${new Date().toLocaleString('ar')}
                        </div>
                        <table>
                `;

                // Add headers
                const headers = ['الاسم الكامل', 'تاريخ الميلاد', 'الأرقام الأساسية', 'الأرقام اليدوية', 'رقم الوثيقة', 'عدد الأرقام الأساسية', 'عدد الأرقام اليدوية', 'الملاحظة'];
                htmlContent += '<thead><tr>';
                headers.forEach(header => {
                    htmlContent += `<th>${header}</th>`;
                });
                htmlContent += '</tr></thead><tbody>';

                // Process all data
                allData.forEach(candidate => {
                    const insuranceNumbers = candidate.insurance_numbers || [];
                    const manualNumbers = candidate.manual_numbers || [];
                    const allBasicNumbers = [...insuranceNumbers];

                    let birthDate = 'غير محدد';
                    if (candidate.additional_info && Array.isArray(candidate.additional_info)) {
                        for (const info of candidate.additional_info) {
                            if (info && typeof info === 'string' && info !== 'غير محدد' && info.trim() !== '') {
                                const datePatterns = [
                                    /\d{4}-\d{2}-\d{2}/,
                                    /\d{2}\/\d{2}\/\d{4}/,
                                    /\d{2}-\d{2}-\d{4}/,
                                    /\d{4}\/\d{2}\/\d{2}/,
                                    /\d{2}\.\d{2}\.\d{4}/,
                                    /\d{1,2}\s+\w+\s+\d{4}/,
                                    /\w+\s+\d{1,2},?\s+\d{4}/
                                ];
                                for (const pattern of datePatterns) {
                                    const dateMatch = info.match(pattern);
                                    if (dateMatch) {
                                        birthDate = dateMatch[0];
                                        break;
                                    }
                                }
                                if (birthDate !== 'غير محدد') break;
                            }
                        }
                    }

                    let note = '';
                    if (insuranceNumbers.length > 1) {
                        note = 'تضارب أرقام أساسية';
                    } else if (manualNumbers.length > 0 && allBasicNumbers.length > 0) {
                        note = 'يحتاج دمج';
                    } else if (allBasicNumbers.length === 1 && manualNumbers.length === 1) {
                        note = 'طبيعي';
                    } else {
                        note = 'يحتاج مراجعة';
                    }

                    const rowData = [
                        candidate.person_name || 'غير محدد',
                        birthDate,
                        allBasicNumbers.length > 0 ? allBasicNumbers.join(' – ') : 'لا يوجد',
                        manualNumbers.length > 0 ? manualNumbers.join(' – ') : 'لا يوجد',
                        candidate.document_number || 'غير محدد',
                        allBasicNumbers.length.toString(),
                        manualNumbers.length.toString(),
                        note
                    ];

                    htmlContent += '<tr>';
                    rowData.forEach(cell => {
                        htmlContent += `<td>${cell}</td>`;
                    });
                    htmlContent += '</tr>';
                });

                htmlContent += `
                        </tbody>
                        </table>
                        <div style="text-align: center; margin-top: 20px; font-size: 11px; color: #666;">
                            تم إنشاء هذا التقرير بواسطة نظام تحليل البيانات - ${new Date().toLocaleString('ar')}
                        </div>
                    </body>
                    </html>
                `;

                // Create blob and download
                const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8;' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = 'merge_analysis_all_data.html';
                link.click();

                console.log('PDF export completed successfully - exported', allData.length, 'records');
                alert('تم تصدير ' + allData.length + ' سجل كملف HTML - يمكن طباعته كـ PDF من المتصفح');
            } catch (error) {
                console.error('Error exporting PDF:', error);
                alert('حدث خطأ أثناء تصدير ملف PDF');
            }
        }

        function exportMergeAnalysisToJSON() {
            try {
                // Get all merge candidates data from the global analysisData
                let allData = [];
                if (window.analysisData && window.analysisData.analysis && window.analysisData.analysis.sector_specific && window.analysisData.analysis.sector_specific.merge_analysis) {
                    allData = window.analysisData.analysis.sector_specific.merge_analysis.merge_candidates || [];
                }

                if (allData.length === 0) {
                    alert('لا توجد بيانات متاحة للتصدير');
                    return;
                }

                // Process all data into structured format
                const processedData = allData.map(candidate => {
                    const insuranceNumbers = candidate.insurance_numbers || [];
                    const manualNumbers = candidate.manual_numbers || [];
                    const allBasicNumbers = [...insuranceNumbers];

                    let birthDate = 'غير محدد';
                    if (candidate.additional_info && Array.isArray(candidate.additional_info)) {
                        for (const info of candidate.additional_info) {
                            if (info && typeof info === 'string' && info !== 'غير محدد' && info.trim() !== '') {
                                const datePatterns = [
                                    /\d{4}-\d{2}-\d{2}/,
                                    /\d{2}\/\d{2}\/\d{4}/,
                                    /\d{2}-\d{2}-\d{4}/,
                                    /\d{4}\/\d{2}\/\d{2}/,
                                    /\d{2}\.\d{2}\.\d{4}/,
                                    /\d{1,2}\s+\w+\s+\d{4}/,
                                    /\w+\s+\d{1,2},?\s+\d{4}/
                                ];
                                for (const pattern of datePatterns) {
                                    const dateMatch = info.match(pattern);
                                    if (dateMatch) {
                                        birthDate = dateMatch[0];
                                        break;
                                    }
                                }
                                if (birthDate !== 'غير محدد') break;
                            }
                        }
                    }

                    let note = '';
                    let noteType = '';
                    if (insuranceNumbers.length > 1) {
                        note = 'تضارب أرقام أساسية';
                        noteType = 'conflict';
                    } else if (manualNumbers.length > 0 && allBasicNumbers.length > 0) {
                        note = 'يحتاج دمج';
                        noteType = 'merge';
                    } else if (allBasicNumbers.length === 1 && manualNumbers.length === 1) {
                        note = 'طبيعي';
                        noteType = 'normal';
                    } else {
                        note = 'يحتاج مراجعة';
                        noteType = 'review';
                    }

                    return {
                        person_name: candidate.person_name || 'غير محدد',
                        birth_date: birthDate,
                        insurance_numbers: allBasicNumbers,
                        manual_numbers: manualNumbers,
                        document_number: candidate.document_number || 'غير محدد',
                        insurance_count: allBasicNumbers.length,
                        manual_count: manualNumbers.length,
                        merge_reason: note,
                        merge_reason_type: noteType,
                        additional_info: candidate.additional_info || [],
                        row_count: candidate.row_count || 1
                    };
                });

                // Create JSON with metadata
                const jsonContent = JSON.stringify({
                    export_info: {
                        export_date: new Date().toISOString(),
                        export_timestamp: Date.now(),
                        total_records: processedData.length,
                        data_type: 'merge_analysis_candidates',
                        system_version: 'Multi Sector Analyzer v2.0'
                    },
                    summary: {
                        total_candidates: allData.length,
                        analyzed_persons: window.analysisData?.analysis?.sector_specific?.merge_analysis?.analyzed_persons || 0,
                        person_columns_found: window.analysisData?.analysis?.sector_specific?.merge_analysis?.person_columns_found || 0,
                        number_columns_found: window.analysisData?.analysis?.sector_specific?.merge_analysis?.number_columns_found || 0
                    },
                    data: processedData
                }, null, 2);

                // Download
                const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = 'merge_analysis_all_data.json';
                link.click();

                console.log('JSON export completed successfully - exported', allData.length, 'records');
                alert('تم تصدير ' + allData.length + ' سجل بنجاح');
            } catch (error) {
                console.error('Error exporting JSON:', error);
                alert('حدث خطأ أثناء تصدير ملف JSON');
            }
        }

        // Accuracy Report Export Functions
        function exportAccuracyReportToCSV() {
            try {
                let accuracyData = [];
                let fullDataset = [];
                if (window.analysisData && window.analysisData.analysis && window.analysisData.analysis.sector_specific && window.analysisData.analysis.sector_specific.accuracy_analysis) {
                    // Use full dataset for export if available, otherwise fall back to detailed report
                    fullDataset = window.analysisData.analysis.sector_specific.accuracy_analysis.export_data || [];
                    accuracyData = window.analysisData.analysis.sector_specific.accuracy_analysis.detailed_report || [];
                }

                if (fullDataset.length === 0 && accuracyData.length === 0) {
                    alert('لا توجد بيانات تقرير الدقة متاحة للتصدير');
                    return;
                }

                // Use full dataset if available, otherwise use detailed report
                const exportData = fullDataset.length > 0 ? fullDataset : accuracyData;

                // Create CSV content
                let csvContent = 'رقم الصف,الكمية,صحة الكمية,القيمة الجمركية,صحة القيمة,بلد المنشأ,صحة المنشأ,نسبة الدقة,الحالة,المشاكل\n';

                exportData.forEach((record, index) => {
                    let rowData = {};
                    let issues = 'لا توجد مشاكل';

                    if (fullDataset.length > 0) {
                        // Full dataset format (DataFrame records)
                        rowData = {
                            row_index: index + 1,
                            quantity: record['الكمية'] || record['العدد'] || record['الكميه'] || '',
                            value: record['القيمة الجمركية'] || record['القيمة'] || record['القيمه الجمركيه'] || '',
                            origin: record['بلد المنشأ'] || record['بلد المنشا'] || record['رمز بلد المنشأ'] || ''
                        };

                        // For full dataset, we don't have validation results, so we'll mark as 'غير محدد'
                        rowData.quantity_valid = true; // Assume valid for full export
                        rowData.value_valid = true;
                        rowData.origin_valid = true;
                        rowData.accuracy_percentage = 100; // Assume 100% for full export
                        rowData.status = 'مصدر كامل البيانات';
                    } else {
                        // Detailed report format
                        issues = record.issues ? record.issues.join('; ') : 'لا توجد مشاكل';
                        rowData = {
                            row_index: record.row_index,
                            quantity: record.data.quantity || '',
                            quantity_valid: record.data.quantity_valid,
                            value: record.data.value || '',
                            value_valid: record.data.value_valid,
                            origin: record.data.origin || '',
                            origin_valid: record.data.origin_valid,
                            accuracy_percentage: record.accuracy_percentage,
                            status: record.status
                        };
                    }

                    const row = [
                        rowData.row_index,
                        rowData.quantity,
                        rowData.quantity_valid ? 'صحيح' : 'خطأ',
                        rowData.value,
                        rowData.value_valid ? 'صحيح' : 'خطأ',
                        rowData.origin,
                        rowData.origin_valid ? 'صحيح' : 'خطأ',
                        rowData.accuracy_percentage + '%',
                        rowData.status,
                        issues
                    ];
                    csvContent += row.map(field => `"${field}"`).join(',') + '\n';
                });

                // Download CSV
                const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = 'accuracy_report.csv';
                link.click();

                alert('تم تصدير تقرير الدقة بنجاح');
            } catch (error) {
                console.error('Error exporting accuracy report to CSV:', error);
                alert('حدث خطأ أثناء تصدير ملف CSV');
            }
        }

        function exportAccuracyReportToExcel() {
            try {
                let accuracyData = [];
                let fullDataset = [];
                if (window.analysisData && window.analysisData.analysis && window.analysisData.analysis.sector_specific && window.analysisData.analysis.sector_specific.accuracy_analysis) {
                    fullDataset = window.analysisData.analysis.sector_specific.accuracy_analysis.export_data || [];
                    accuracyData = window.analysisData.analysis.sector_specific.accuracy_analysis.detailed_report || [];
                }

                if (fullDataset.length === 0 && accuracyData.length === 0) {
                    alert('لا توجد بيانات تقرير الدقة متاحة للتصدير');
                    return;
                }

                const exportData = fullDataset.length > 0 ? fullDataset : accuracyData;

                // Create Excel-compatible HTML table
                let htmlContent = `
                    <html>
                    <head>
                        <meta charset="utf-8">
                        <title>تقرير تحليل الدقة</title>
                    </head>
                    <body>
                        <table border="1">
                            <thead>
                                <tr>
                                    <th>رقم الصف</th>
                                    <th>الكمية</th>
                                    <th>صحة الكمية</th>
                                    <th>القيمة الجمركية</th>
                                    <th>صحة القيمة</th>
                                    <th>بلد المنشأ</th>
                                    <th>صحة المنشأ</th>
                                    <th>نسبة الدقة</th>
                                    <th>الحالة</th>
                                    <th>المشاكل</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                exportData.forEach((record, index) => {
                    let rowData = {};
                    let issues = 'لا توجد مشاكل';

                    if (fullDataset.length > 0) {
                        // Full dataset format (DataFrame records)
                        rowData = {
                            row_index: index + 1,
                            quantity: record['الكمية'] || record['العدد'] || record['الكميه'] || '',
                            value: record['القيمة الجمركية'] || record['القيمة'] || record['القيمه الجمركيه'] || '',
                            origin: record['بلد المنشأ'] || record['بلد المنشا'] || record['رمز بلد المنشأ'] || ''
                        };

                        // For full dataset, we don't have validation results, so we'll mark as 'غير محدد'
                        rowData.quantity_valid = true; // Assume valid for full export
                        rowData.value_valid = true;
                        rowData.origin_valid = true;
                        rowData.accuracy_percentage = 100; // Assume 100% for full export
                        rowData.status = 'مصدر كامل البيانات';
                    } else {
                        // Detailed report format
                        issues = record.issues ? record.issues.join('; ') : 'لا توجد مشاكل';
                        rowData = {
                            row_index: record.row_index,
                            quantity: record.data.quantity || '',
                            quantity_valid: record.data.quantity_valid,
                            value: record.data.value || '',
                            value_valid: record.data.value_valid,
                            origin: record.data.origin || '',
                            origin_valid: record.data.origin_valid,
                            accuracy_percentage: record.accuracy_percentage,
                            status: record.status
                        };
                    }

                    htmlContent += `
                        <tr>
                            <td>${rowData.row_index}</td>
                            <td>${rowData.quantity}</td>
                            <td>${rowData.quantity_valid ? 'صحيح' : 'خطأ'}</td>
                            <td>${rowData.value}</td>
                            <td>${rowData.value_valid ? 'صحيح' : 'خطأ'}</td>
                            <td>${rowData.origin}</td>
                            <td>${rowData.origin_valid ? 'صحيح' : 'خطأ'}</td>
                            <td>${rowData.accuracy_percentage}%</td>
                            <td>${rowData.status}</td>
                            <td>${issues}</td>
                        </tr>
                    `;
                });

                htmlContent += `
                            </tbody>
                        </table>
                    </body>
                    </html>
                `;

                // Download as HTML (Excel can open HTML files)
                const blob = new Blob([htmlContent], { type: 'application/vnd.ms-excel;charset=utf-8;' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = 'accuracy_report.xls';
                link.click();

                alert('تم تصدير تقرير الدقة كملف Excel بنجاح');
            } catch (error) {
                console.error('Error exporting accuracy report to Excel:', error);
                alert('حدث خطأ أثناء تصدير ملف Excel');
            }
        }

        function exportAccuracyReportToPDF() {
            try {
                let accuracyData = [];
                let fullDataset = [];
                if (window.analysisData && window.analysisData.analysis && window.analysisData.analysis.sector_specific && window.analysisData.analysis.sector_specific.accuracy_analysis) {
                    fullDataset = window.analysisData.analysis.sector_specific.accuracy_analysis.export_data || [];
                    accuracyData = window.analysisData.analysis.sector_specific.accuracy_analysis.detailed_report || [];
                }

                if (fullDataset.length === 0 && accuracyData.length === 0) {
                    alert('لا توجد بيانات تقرير الدقة متاحة للتصدير');
                    return;
                }

                const exportData = fullDataset.length > 0 ? fullDataset : accuracyData;

                // Create HTML content for PDF
                let htmlContent = `
                    <!DOCTYPE html>
                    <html lang="ar" dir="rtl">
                    <head>
                        <meta charset="UTF-8">
                        <title>تقرير تحليل الدقة</title>
                        <style>
                            body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
                            table { width: 100%; border-collapse: collapse; margin: 20px 0; font-size: 12px; }
                            th, td { border: 1px solid #ddd; padding: 6px; text-align: center; }
                            th { background-color: #f2f2f2; font-weight: bold; }
                            h1 { text-align: center; color: #333; }
                            .summary { background-color: #e9ecef; padding: 10px; margin-bottom: 20px; border-radius: 5px; }
                            @media print { body { margin: 0; } }
                        </style>
                    </head>
                    <body>
                        <h1>تقرير تحليل الدقة - البيانات الجمركية</h1>
                        <div class="summary">
                            <strong>إجمالي السجلات:</strong> ${accuracyData.length} |
                            <strong>تاريخ التصدير:</strong> ${new Date().toLocaleString('ar')}
                        </div>
                        <table>
                            <thead>
                                <tr>
                                    <th>رقم الصف</th>
                                    <th>الكمية</th>
                                    <th>صحة الكمية</th>
                                    <th>القيمة الجمركية</th>
                                    <th>صحة القيمة</th>
                                    <th>بلد المنشأ</th>
                                    <th>صحة المنشأ</th>
                                    <th>نسبة الدقة</th>
                                    <th>الحالة</th>
                                    <th>المشاكل</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                exportData.forEach((record, index) => {
                    let rowData = {};
                    let issues = 'لا توجد مشاكل';

                    if (fullDataset.length > 0) {
                        // Full dataset format (DataFrame records)
                        rowData = {
                            row_index: index + 1,
                            quantity: record['الكمية'] || record['العدد'] || record['الكميه'] || '',
                            value: record['القيمة الجمركية'] || record['القيمة'] || record['القيمه الجمركيه'] || '',
                            origin: record['بلد المنشأ'] || record['بلد المنشا'] || record['رمز بلد المنشأ'] || ''
                        };

                        // For full dataset, we don't have validation results, so we'll mark as 'غير محدد'
                        rowData.quantity_valid = true; // Assume valid for full export
                        rowData.value_valid = true;
                        rowData.origin_valid = true;
                        rowData.accuracy_percentage = 100; // Assume 100% for full export
                        rowData.status = 'مصدر كامل البيانات';
                    } else {
                        // Detailed report format
                        issues = record.issues ? record.issues.join('; ') : 'لا توجد مشاكل';
                        rowData = {
                            row_index: record.row_index,
                            quantity: record.data.quantity || '',
                            quantity_valid: record.data.quantity_valid,
                            value: record.data.value || '',
                            value_valid: record.data.value_valid,
                            origin: record.data.origin || '',
                            origin_valid: record.data.origin_valid,
                            accuracy_percentage: record.accuracy_percentage,
                            status: record.status
                        };
                    }

                    htmlContent += `
                        <tr>
                            <td>${rowData.row_index}</td>
                            <td>${rowData.quantity}</td>
                            <td>${rowData.quantity_valid ? 'صحيح' : 'خطأ'}</td>
                            <td>${rowData.value}</td>
                            <td>${rowData.value_valid ? 'صحيح' : 'خطأ'}</td>
                            <td>${rowData.origin}</td>
                            <td>${rowData.origin_valid ? 'صحيح' : 'خطأ'}</td>
                            <td>${rowData.accuracy_percentage}%</td>
                            <td>${rowData.status}</td>
                            <td>${issues}</td>
                        </tr>
                    `;
                });

                htmlContent += `
                        </tbody>
                        </table>
                        <div style="text-align: center; margin-top: 20px; font-size: 11px; color: #666;">
                            تم إنشاء هذا التقرير بواسطة نظام تحليل البيانات الجمركية - ${new Date().toLocaleString('ar')}
                        </div>
                    </body>
                    </html>
                `;

                // Create blob and download
                const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8;' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = 'accuracy_report.html';
                link.click();

                console.log('PDF export completed successfully - exported', exportData.length, 'records');
                alert(`تم تصدير تقرير الدقة كملف HTML - يمكن طباعته كـ PDF من المتصفح\nتم تصدير ${exportData.length} سجل`);
            } catch (error) {
                console.error('Error exporting accuracy report to PDF:', error);
                alert('حدث خطأ أثناء تصدير ملف PDF');
            }
        }

        // Sorting functionality for merge table
        let mergeTableSortColumn = -1;
        let mergeTableSortDirection = 'asc';

        function sortMergeTable(columnIndex) {
            console.log('Sorting merge table by column:', columnIndex);

            const table = document.getElementById('personTable');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Toggle sort direction if same column
            if (mergeTableSortColumn === columnIndex) {
                mergeTableSortDirection = mergeTableSortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                mergeTableSortColumn = columnIndex;
                mergeTableSortDirection = 'asc';
            }

            // Update sort icons
            updateMergeTableSortIcons(columnIndex);

            // Sort rows
            rows.sort((a, b) => {
                const aValue = getMergeTableCellValue(a, columnIndex);
                const bValue = getMergeTableCellValue(b, columnIndex);

                let result = 0;

                // Handle different column types
                switch (columnIndex) {
                    case 0: // Full Name - text
                    case 2: // Basic Numbers - text
                    case 3: // Manual Numbers - text
                    case 4: // Document Number - text
                    case 7: // Note - text
                        result = aValue.localeCompare(bValue, 'ar');
                        break;
                    case 1: // Birth Date - date
                        result = compareDates(aValue, bValue);
                        break;
                    case 5: // Basic Numbers Count - number
                    case 6: // Manual Numbers Count - number
                        result = parseInt(aValue) - parseInt(bValue);
                        break;
                    default:
                        result = aValue.localeCompare(bValue, 'ar');
                }

                return mergeTableSortDirection === 'asc' ? result : -result;
            });

            // Reorder rows in table
            rows.forEach(row => tbody.appendChild(row));

            console.log('Merge table sorted successfully');
        }

        // Sorting functionality for column analysis table
        let columnTableSortColumn = -1;
        let columnTableSortDirection = 'asc';

        function sortTable(columnIndex) {
            console.log('Sorting column analysis table by column:', columnIndex);

            const table = document.getElementById('columnsTable');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Toggle sort direction if same column
            if (columnTableSortColumn === columnIndex) {
                columnTableSortDirection = columnTableSortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                columnTableSortColumn = columnIndex;
                columnTableSortDirection = 'asc';
            }

            // Update sort icons
            updateColumnTableSortIcons(columnIndex);

            // Sort rows
            rows.sort((a, b) => {
                const aValue = getColumnTableCellValue(a, columnIndex);
                const bValue = getColumnTableCellValue(b, columnIndex);

                let result = 0;

                // Handle different column types
                switch (columnIndex) {
                    case 0: // Column Name - text
                        result = aValue.localeCompare(bValue, 'ar');
                        break;
                    case 1: // Type - text
                        result = aValue.localeCompare(bValue, 'ar');
                        break;
                    case 2: // Unique Values - number
                    case 3: // Missing Count - number
                    case 5: // Duplicates 2 - number
                    case 6: // Duplicates 3+ - number
                        result = parseInt(aValue.replace(/[^\d]/g, '')) - parseInt(bValue.replace(/[^\d]/g, ''));
                        break;
                    case 4: // Missing Percentage - percentage
                        result = parseFloat(aValue.replace('%', '')) - parseFloat(bValue.replace('%', ''));
                        break;
                    default:
                        result = aValue.localeCompare(bValue, 'ar');
                }

                return columnTableSortDirection === 'asc' ? result : -result;
            });

            // Reorder rows in table
            rows.forEach(row => tbody.appendChild(row));

            console.log('Column analysis table sorted successfully');
        }

        function getColumnTableCellValue(row, columnIndex) {
            const cells = row.querySelectorAll('td');
            if (cells.length > columnIndex) {
                let text = cells[columnIndex].textContent.trim();

                // Clean up text for sorting
                text = text.replace(/[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F1E0}-\u{1F1FF}]/gu, ''); // Remove emojis
                text = text.replace(/^\d+\.\s*/, ''); // Remove numbering
                text = text.replace(/\s+/g, ' ').trim(); // Normalize spaces

                return text || '';
            }
            return '';
        }

        function updateColumnTableSortIcons(activeColumn) {
            // Reset all sort icons
            const headers = document.querySelectorAll('#columnsTable thead th');
            headers.forEach((header, index) => {
                const icon = header.querySelector('.sort-icon');
                if (icon) {
                    icon.style.opacity = '0.5';
                    header.classList.remove('sort-asc', 'sort-desc');
                }
            });

            // Set active sort icon
            if (activeColumn >= 0) {
                const activeHeader = headers[activeColumn];
                const icon = activeHeader.querySelector('.sort-icon');
                if (icon) {
                    icon.style.opacity = '1';
                    activeHeader.classList.add(columnTableSortDirection === 'asc' ? 'sort-asc' : 'sort-desc');
                }
            }
        }

        function getMergeTableCellValue(row, columnIndex) {
            const cells = row.querySelectorAll('td');
            if (cells.length > columnIndex) {
                let text = cells[columnIndex].textContent.trim();

                // Clean up text for sorting
                text = text.replace(/[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F1E0}-\u{1F1FF}]/gu, ''); // Remove emojis
                text = text.replace(/^\d+\.\s*/, ''); // Remove numbering
                text = text.replace(/\s+/g, ' ').trim(); // Normalize spaces

                return text || '';
            }
            return '';
        }

        function compareDates(dateA, dateB) {
            // Handle various date formats
            const parseDate = (dateStr) => {
                if (!dateStr || dateStr === 'غير محدد') return new Date(0);

                // Try different date formats
                const formats = [
                    /^(\d{4})-(\d{2})-(\d{2})$/,  // YYYY-MM-DD
                    /^(\d{2})\/(\d{2})\/(\d{4})$/, // DD/MM/YYYY
                    /^(\d{2})-(\d{2})-(\d{4})$/,  // DD-MM-YYYY
                    /^(\d{4})\/(\d{2})\/(\d{2})$/, // YYYY/MM/DD
                    /^(\d{2})\.(\d{2})\.(\d{4})$/  // DD.MM.YYYY
                ];

                for (const format of formats) {
                    const match = dateStr.match(format);
                    if (match) {
                        const [, year, month, day] = match.map(Number);
                        return new Date(year, month - 1, day);
                    }
                }

                return new Date(0); // Invalid date
            };

            const a = parseDate(dateA);
            const b = parseDate(dateB);

            return a.getTime() - b.getTime();
        }

        function updateMergeTableSortIcons(activeColumn) {
            // Reset all sort icons
            const headers = document.querySelectorAll('#personTable thead th');
            headers.forEach((header, index) => {
                const icon = header.querySelector('.sort-icon');
                if (icon) {
                    icon.style.opacity = '0.5';
                    header.classList.remove('sort-asc', 'sort-desc');
                }
            });

            // Set active sort icon
            if (activeColumn >= 0) {
                const activeHeader = headers[activeColumn];
                const icon = activeHeader.querySelector('.sort-icon');
                if (icon) {
                    icon.style.opacity = '1';
                    activeHeader.classList.add(mergeTableSortDirection === 'asc' ? 'sort-asc' : 'sort-desc');
                }
            }
        }

        // Initialize analysis criteria dropdown
        async function initializeAnalysisCriteria() {
            try {
                console.log('Initializing analysis criteria dropdown...');

                // Get the detected sector from analysis data
                const analysisDataElement = document.getElementById('analysisData');
                if (!analysisDataElement) {
                    console.warn('No analysis data element found');
                    return;
                }

                const analysisData = JSON.parse(analysisDataElement.textContent);
                const detectedSector = analysisData.detected_sector;

                console.log('Detected sector:', detectedSector);

                // Fetch custom criteria from API
                const response = await fetch('/api/custom-criteria');
                const data = await response.json();

                if (!data.success) {
                    console.error('Failed to fetch custom criteria:', data.message);
                    return;
                }

                const criteriaSelect = document.getElementById('analysisCriteria');
                if (!criteriaSelect) {
                    console.error('Analysis criteria select element not found');
                    return;
                }

                // Clear existing options except the default "all" option
                criteriaSelect.innerHTML = '<option value="all">جميع المعايير</option>';

                // Filter criteria to exclude those for the current sector
                const filteredCriteria = data.criteria.filter(criterion => {
                    // Keep criteria that are not for the detected sector
                    return criterion.sector !== detectedSector;
                });

                console.log('Filtered criteria count:', filteredCriteria.length);

                // Add filtered criteria to dropdown
                filteredCriteria.forEach(criterion => {
                    const option = document.createElement('option');
                    option.value = criterion.id;
                    option.textContent = `${criterion.name} (${criterion.sector === 'all' ? 'عام' : criterion.sector})`;
                    criteriaSelect.appendChild(option);
                });

                console.log('Analysis criteria dropdown populated successfully');

            } catch (error) {
                console.error('Error initializing analysis criteria:', error);
            }
        }

        // Initialize charts for distributions
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Page loaded, initializing components...');

            // Show loading indicator
            showPageLoading();

            try {
                // Initialize table sorting with error handling
                const sortableHeaders = document.querySelectorAll('.sortable');
                sortableHeaders.forEach((header, index) => {
                    header.addEventListener('click', () => {
                        try {
                            sortTable(index);
                        } catch (error) {
                            console.error('Error sorting table:', error);
                        }
                    });
                });

                // Initialize merge table sorting with error handling
                const mergeSortableHeaders = document.querySelectorAll('#personTable .sortable');
                mergeSortableHeaders.forEach((header, index) => {
                    header.addEventListener('click', () => {
                        try {
                            sortMergeTable(index);
                        } catch (error) {
                            console.error('Error sorting merge table:', error);
                        }
                    });
                });

                // Initialize analysis criteria dropdown
                setTimeout(() => {
                    try {
                        initializeAnalysisCriteria();
                    } catch (error) {
                        console.error('Error initializing analysis criteria:', error);
                    }
                }, 100);

                // Initialize distribution charts with timeout and error handling
                setTimeout(() => {
                    try {
                        initializeDistributionCharts();
                    } catch (error) {
                        console.error('Error initializing distribution charts:', error);
                        showChartError('فشل في تحميل الرسوم البيانية الأساسية');
                    }
                }, 500);

                // Initialize interactive charts with timeout and error handling
                setTimeout(() => {
                    try {
                        if (typeof Chart !== 'undefined') {
                            initializeInteractiveCharts();
                        } else {
                            console.error('❌ Chart.js not loaded');
                            showChartError('مكتبة الرسوم البيانية غير محملة');
                        }
                    } catch (error) {
                        console.error('Error initializing interactive charts:', error);
                        showChartError('فشل في تحميل الرسوم البيانية التفاعلية');
                    }
                }, 1500);

                // Initialize merge analysis pagination with error handling
                setTimeout(() => {
                    try {
                        initializeMergePagination();
                    } catch (error) {
                        console.error('Error initializing merge pagination:', error);
                    }
                }, 100);

                // Initialize PersonRecord table with error handling
                setTimeout(() => {
                    try {
                        initializePersonRecordTable();
                    } catch (error) {
                        console.error('Error initializing PersonRecord table:', error);
                        showTableError('فشل في تحميل جدول PersonRecord');
                    }
                }, 200);

                // Initialize progress bars with error handling
                setTimeout(() => {
                    try {
                        initializeProgressBars();
                    } catch (error) {
                        console.error('Error initializing progress bars:', error);
                    }
                }, 100);

                // Hide loading indicator after all initializations
                setTimeout(() => {
                    hidePageLoading();
                }, 3000);

            } catch (error) {
                console.error('Critical error during page initialization:', error);
                hidePageLoading();
                showPageError('حدث خطأ أثناء تحميل الصفحة');
            }
        });

        // Helper functions for loading and error handling
        function showPageLoading() {
            const loadingDiv = document.createElement('div');
            loadingDiv.id = 'pageLoadingIndicator';
            loadingDiv.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(255,255,255,0.9); z-index: 9999; display: flex; align-items: center; justify-content: center;">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <h4 class="mt-3 text-primary">جاري تحميل النتائج...</h4>
                        <p class="text-muted">يرجى الانتظار حتى انتهاء تحميل جميع المكونات</p>
                    </div>
                </div>
            `;
            document.body.appendChild(loadingDiv);
        }

        function hidePageLoading() {
            const loadingDiv = document.getElementById('pageLoadingIndicator');
            if (loadingDiv) {
                loadingDiv.style.display = 'none';
                setTimeout(() => loadingDiv.remove(), 500);
            }
        }

        function showPageError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.innerHTML = `
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>خطأ في التحميل:</strong> ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            const container = document.querySelector('.container');
            if (container) {
                container.insertBefore(errorDiv, container.firstChild);
            }
        }

        function showChartError(message) {
            const chartContainers = document.querySelectorAll('.chart-container, .chart-wrapper-large');
            chartContainers.forEach(container => {
                container.innerHTML = `
                    <div class="alert alert-warning text-center">
                        <i class="fas fa-chart-bar me-2"></i>
                        ${message}
                    </div>
                `;
            });
        }

        function showTableError(message) {
            const tableContainer = document.querySelector('#personTable').parentNode;
            if (tableContainer) {
                const errorDiv = document.createElement('div');
                errorDiv.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-table me-2"></i>
                        ${message}
                    </div>
                `;
                tableContainer.insertBefore(errorDiv, tableContainer.firstChild);
            }
        }

        // Optimized distribution charts initialization
        function initializeDistributionCharts() {
            console.log('📊 Initializing distribution charts...');

            const analysisDataElement = document.getElementById('analysisData');
            if (!analysisDataElement) {
                console.warn('No analysis data element found');
                return;
            }

            const analysisData = JSON.parse(analysisDataElement.textContent);
            const distributions = analysisData.analysis && analysisData.analysis.distributions ? analysisData.analysis.distributions : {};

            if (Object.keys(distributions).length === 0) {
                console.log('No distribution data available');
                return;
            }

            // Limit the number of charts to prevent performance issues
            const maxCharts = 10;
            const distributionKeys = Object.keys(distributions).slice(0, maxCharts);

            distributionKeys.forEach((columnName, index) => {
                try {
                    const chartId = `chart-${index + 1}`;
                    const canvas = document.getElementById(chartId);

                    if (!canvas) {
                        console.warn(`Canvas ${chartId} not found`);
                        return;
                    }

                    const ctx = canvas.getContext('2d');
                    const data = distributions[columnName];

                    if (!data || Object.keys(data).length === 0) {
                        console.warn(`No data for column ${columnName}`);
                        return;
                    }

                    // Prepare data for chart (limit to top 15 values)
                    const entries = Object.entries(data);
                    const sortedEntries = entries.sort((a, b) => b[1] - a[1]).slice(0, 15);

                    const labels = sortedEntries.map(entry => {
                        const label = String(entry[0]);
                        return label.length > 20 ? label.substring(0, 17) + '...' : label;
                    });
                    const values = sortedEntries.map(entry => entry[1]);

                    // Create chart with error handling
                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: labels,
                            datasets: [{
                                label: columnName,
                                data: values,
                                backgroundColor: 'rgba(102, 126, 234, 0.8)',
                                borderColor: 'rgba(102, 126, 234, 1)',
                                borderWidth: 2,
                                borderRadius: 8
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: false
                                },
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            const total = values.reduce((a, b) => a + b, 0);
                                            const percentage = total > 0 ? (context.parsed.y / total * 100).toFixed(1) : 0;
                                            return `${context.label}: ${context.parsed.y} (${percentage}%)`;
                                        }
                                    }
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            },
                            animation: {
                                duration: 500, // Reduced animation time
                                easing: 'easeInOutQuart'
                            }
                        }
                    });

                    console.log(`✅ Chart created for ${columnName}`);

                } catch (error) {
                    console.error(`Error creating chart for ${columnName}:`, error);
                }
            });

            console.log('📊 Distribution charts initialization completed');
        }

        // Initialize progress bars with data attributes
        function initializeProgressBars() {
            console.log('Initializing progress bars...');
            const progressBars = document.querySelectorAll('.progress-bar[data-percentage]');

            progressBars.forEach(bar => {
                const percentage = parseFloat(bar.getAttribute('data-percentage')) || 0;
                const clampedPercentage = Math.max(0, Math.min(100, percentage)); // Ensure between 0-100
                bar.style.width = clampedPercentage + '%';
                bar.setAttribute('aria-valuenow', clampedPercentage);
            });

            console.log('Progress bars initialized:', progressBars.length, 'bars');
        }

        // PersonRecord Table Functions
        function initializePersonRecordTable() {
            console.log('Initializing PersonRecord table...');

            try {
                // Get data from global analysisData first (more reliable)
                let mergeData = null;

                if (window.analysisData && window.analysisData.analysis && window.analysisData.analysis.sector_specific && window.analysisData.analysis.sector_specific.merge_analysis) {
                    mergeData = window.analysisData.analysis.sector_specific.merge_analysis.merge_candidates || [];
                    console.log('Merge data from global analysisData:', mergeData.length);
                }

                // If no data from global, try script tag as fallback
                if ((!mergeData || mergeData.length === 0)) {
                    const mergeDataElement = document.getElementById('mergeAnalysisData');
                    if (mergeDataElement) {
                        try {
                            const scriptData = JSON.parse(mergeDataElement.textContent);
                            if (scriptData && scriptData.length > 0) {
                                mergeData = scriptData;
                                console.log('Merge data from script tag:', mergeData.length);
                            }
                        } catch (e) {
                            console.error('Error parsing merge data from script tag:', e);
                        }
                    }
                }

                console.log('Final merge data length:', mergeData ? mergeData.length : 'null');

                if (!mergeData || mergeData.length === 0) {
                    console.log('No merge data available');
                    // Show message in table
                    const tbody = document.querySelector("#personTable tbody");
                    if (tbody) {
                        tbody.innerHTML = `
                            <tr>
                                <td colspan="7" class="text-center">
                                    <div class="alert alert-warning mb-0">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <strong>لا توجد بيانات مرشحين للدمج متاحة</strong>
                                        <br>
                                        <small class="text-muted">تحقق من السجلات للتأكد من وجود بيانات الدمج</small>
                                    </div>
                                </td>
                            </tr>
                        `;
                    }
                    return;
                }

                // Limit data size to prevent performance issues
                const maxRecords = 1000; // Limit to 1000 records for performance
                if (mergeData.length > maxRecords) {
                    console.warn(`Limiting merge data from ${mergeData.length} to ${maxRecords} records for performance`);
                    mergeData = mergeData.slice(0, maxRecords);
                }

                populatePersonRecordTable(mergeData);
                console.log('PersonRecord table populated with', mergeData.length, 'records');

                // Show success message
                const successMessage = document.createElement('div');
                successMessage.className = 'alert alert-success mt-3';
                successMessage.innerHTML = `
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>تم تحميل البيانات بنجاح!</strong>
                    تم العثور على ${mergeData.length} مرشح للدمج
                `;
                const tableContainer = document.querySelector("#personTable").parentNode;
                if (tableContainer) {
                    tableContainer.insertBefore(successMessage, document.querySelector("#personTable"));
                }

                // Remove success message after 3 seconds
                setTimeout(() => {
                    if (successMessage && successMessage.parentNode) {
                        successMessage.remove();
                    }
                }, 3000);

            } catch (error) {
                console.error('Critical error in initializePersonRecordTable:', error);

                // Show error message in table
                const tbody = document.querySelector("#personTable tbody");
                if (tbody) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="7" class="text-center">
                                <div class="alert alert-danger mb-0">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>خطأ في تحميل بيانات الجدول</strong>
                                    <br>
                                    <small class="text-muted">حدث خطأ أثناء معالجة البيانات. يرجى إعادة تحميل الصفحة.</small>
                                </div>
                            </td>
                        </tr>
                    `;
                }
            }
        }

        // Global variables for pagination
        let currentPage = 1;
        let pageSize = 50;
        let totalRecords = 0;
        let filteredData = [];

        function populatePersonRecordTable(data) {
            console.log('Populating table with data:', data.length, 'records');

            try {
                // Store data globally for pagination
                filteredData = data;
                totalRecords = data.length;

                // Update pagination info
                updatePaginationInfo();

                // Display current page
                displayCurrentPage();

                console.log('Table populated successfully with pagination');
            } catch (error) {
                console.error('Error in populatePersonRecordTable:', error);
                // Show error in table
                const tbody = document.querySelector("#personTable tbody");
                if (tbody) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="7" class="text-center">
                                <div class="alert alert-danger mb-0">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>خطأ في عرض البيانات</strong>
                                    <br>
                                    <small class="text-muted">حدث خطأ أثناء عرض البيانات في الجدول</small>
                                </div>
                            </td>
                        </tr>
                    `;
                }
            }
        }

        function updatePaginationInfo() {
            const totalPages = Math.ceil(totalRecords / pageSize);
            const startRecord = (currentPage - 1) * pageSize + 1;
            const endRecord = Math.min(currentPage * pageSize, totalRecords);

            // Update total count display
            const totalCountElement = document.getElementById('mergeTotalCount');
            if (totalCountElement) {
                totalCountElement.textContent = `من أصل ${totalRecords}`;
            }

            // Update info div
            const tbody = document.querySelector("#personTable tbody");
            if (tbody) {
                // Remove any existing info div
                const existingInfo = tbody.parentNode.querySelector('.table-info');
                if (existingInfo) {
                    existingInfo.remove();
                }

                const infoDiv = document.createElement("div");
                infoDiv.className = 'table-info';
                infoDiv.innerHTML = `<p style="text-align: center; margin-bottom: 20px; font-weight: bold; color: #007bff;">عرض ${startRecord} - ${endRecord} من ${totalRecords} سجل</p>`;
                tbody.parentNode.insertBefore(infoDiv, tbody);
            }

            // Update pagination controls
            updatePaginationControls();
        }

        function displayCurrentPage() {
            const tbody = document.querySelector("#personTable tbody");
            if (!tbody) {
                console.error('Table body not found');
                return;
            }

            try {
                tbody.innerHTML = '';

                const startIndex = (currentPage - 1) * pageSize;
                const endIndex = Math.min(startIndex + pageSize, totalRecords);
                const pageData = filteredData.slice(startIndex, endIndex);

                // Process records in chunks to prevent UI blocking
                const chunkSize = 10;
                let currentChunk = 0;

                function processChunk() {
                    const chunkStart = currentChunk * chunkSize;
                    const chunkEnd = Math.min(chunkStart + chunkSize, pageData.length);
                    const chunk = pageData.slice(chunkStart, chunkEnd);

                    chunk.forEach((candidate, index) => {
                        try {
                            const globalIndex = startIndex + chunkStart + index;

                            // Use the correct data structure from merge_candidates
                            const insuranceNumbers = candidate.insurance_numbers || [];
                            const manualNumbers = candidate.manual_numbers || [];
                            const allBasicNumbers = [...insuranceNumbers];

                            // Extract birth date from additional_info (simplified)
                            let birthDate = 'غير محدد';
                            let personName = candidate.person_name || 'غير محدد';

                            // Simplified date extraction
                            if (candidate.person_name) {
                                const dateMatch = candidate.person_name.match(/\d{4}-\d{2}-\d{2}|\d{2}\/\d{2}\/\d{4}|\d{2}-\d{2}-\d{4}/);
                                if (dateMatch) {
                                    birthDate = dateMatch[0];
                                }
                            }

                            if (birthDate === 'غير محدد' && candidate.additional_info && Array.isArray(candidate.additional_info)) {
                                for (const info of candidate.additional_info) {
                                    if (info && typeof info === 'string' && info !== 'غير محدد' && info.trim() !== '') {
                                        const dateMatch = info.match(/\d{4}-\d{2}-\d{2}|\d{2}\/\d{2}\/\d{4}|\d{2}-\d{2}-\d{4}/);
                                        if (dateMatch) {
                                            birthDate = dateMatch[0];
                                            break;
                                        }
                                    }
                                }
                            }

                            // Determine note (simplified logic)
                            let note = '';
                            let noteClass = 'note-review';
                            if (insuranceNumbers.length > 1) {
                                note = '🚨 تضارب أرقام أساسية';
                                noteClass = 'note-conflict';
                            } else if (manualNumbers.length > 0 && allBasicNumbers.length > 0) {
                                note = '⚠️ يحتاج دمج';
                                noteClass = 'note-merge';
                            } else if (allBasicNumbers.length === 1 && manualNumbers.length === 1) {
                                note = '✅ طبيعي';
                                noteClass = 'note-normal';
                            } else {
                                note = '❓ يحتاج مراجعة';
                                noteClass = 'note-review';
                            }

                            const row = document.createElement("tr");
                            row.innerHTML = `
                                <td class="person-name">${personName || '<span class="text-muted">غير محدد</span>'}</td>
                                <td>${birthDate !== 'غير محدد' ? birthDate : '<span class="text-muted">غير محدد</span>'}</td>
                                <td class="insurance-numbers">${allBasicNumbers.length > 0 ? allBasicNumbers.join(' • ') : '<span class="text-muted">لا يوجد</span>'}</td>
                                <td class="manual-numbers">${manualNumbers.length > 0 ? manualNumbers.join(' • ') : '<span class="text-muted">لا يوجد</span>'}</td>
                                <td class="document-number">${candidate.document_number || '<span class="text-muted">غير محدد</span>'}</td>
                                <td><span class="count-info">${allBasicNumbers.length}</span></td>
                                <td><span class="count-info">${manualNumbers.length}</span></td>
                                <td><span class="merge-reason">${note}</span></td>
                            `;
                            tbody.appendChild(row);

                        } catch (error) {
                            console.error('Error processing candidate record:', error, candidate);
                            // Add error row
                            const errorRow = document.createElement("tr");
                            errorRow.innerHTML = `
                                <td colspan="8" class="text-center text-danger">
                                    <small>خطأ في معالجة السجل</small>
                                </td>
                            `;
                            tbody.appendChild(errorRow);
                        }
                    });

                    currentChunk++;

                    // Process next chunk or finish
                    if (currentChunk * chunkSize < pageData.length) {
                        setTimeout(processChunk, 10); // Small delay to prevent UI blocking
                    } else {
                        console.log('Page', currentPage, 'displayed with', pageData.length, 'records');
                    }
                }

                // Start processing chunks
                processChunk();

            } catch (error) {
                console.error('Error in displayCurrentPage:', error);
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="text-center">
                            <div class="alert alert-danger mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>خطأ في عرض الصفحة</strong>
                                <br>
                                <small class="text-muted">حدث خطأ أثناء عرض البيانات</small>
                            </div>
                        </td>
                    </tr>
                `;
            }
        }

        function updatePaginationControls() {
            const paginationElement = document.getElementById('mergePagination');
            if (!paginationElement) return;

            const totalPages = Math.ceil(totalRecords / pageSize);
            let paginationHtml = '';

            // Previous button
            paginationHtml += `
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${currentPage - 1})" aria-label="السابق">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            `;

            // Page numbers
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(1)">1</a></li>`;
                if (startPage > 2) {
                    paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                paginationHtml += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                    </li>
                `;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${totalPages})">${totalPages}</a></li>`;
            }

            // Next button
            paginationHtml += `
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${currentPage + 1})" aria-label="التالي">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            `;

            paginationElement.innerHTML = paginationHtml;
        }

        function changePage(page) {
            const totalPages = Math.ceil(totalRecords / pageSize);
            if (page < 1 || page > totalPages) return;

            // Save current scroll position
            const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;

            currentPage = page;
            displayCurrentPage();
            updatePaginationInfo();

            // Restore scroll position after a short delay to ensure DOM is updated
            setTimeout(() => {
                window.scrollTo(0, scrollPosition);
            }, 10);
        }

        function changeMergePageSize() {
            const pageSizeSelect = document.getElementById('mergePageSize');
            if (pageSizeSelect) {
                pageSize = parseInt(pageSizeSelect.value);
                currentPage = 1; // Reset to first page
                displayCurrentPage();
                updatePaginationInfo();
            }
        }

        function jumpToPage(pageNum) {
            const page = parseInt(pageNum);
            if (page && page > 0) {
                changePage(page);
            }
        }

        function updatePaginationInfo() {
            const totalPages = Math.ceil(totalRecords / pageSize);
            const startRecord = (currentPage - 1) * pageSize + 1;
            const endRecord = Math.min(currentPage * pageSize, totalRecords);

            // Update total count display
            const totalCountElement = document.getElementById('mergeTotalCount');
            if (totalCountElement) {
                totalCountElement.textContent = `من أصل ${totalRecords}`;
            }

            // Update current page and total pages display
            const currentPageElement = document.getElementById('currentPageNum');
            const totalPagesElement = document.getElementById('totalPagesNum');
            if (currentPageElement) currentPageElement.textContent = currentPage;
            if (totalPagesElement) totalPagesElement.textContent = totalPages;

            // Update page jumper max value
            const pageJumper = document.getElementById('pageJumper');
            if (pageJumper) {
                pageJumper.max = totalPages;
                pageJumper.value = currentPage;
            }

            // Update info div
            const tbody = document.querySelector("#personTable tbody");
            if (tbody) {
                // Remove any existing info div
                const existingInfo = tbody.parentNode.querySelector('.table-info');
                if (existingInfo) {
                    existingInfo.remove();
                }

                const infoDiv = document.createElement("div");
                infoDiv.className = 'table-info';
                infoDiv.innerHTML = `<p style="text-align: center; margin-bottom: 20px; font-weight: bold; color: #007bff;">عرض ${startRecord} - ${endRecord} من ${totalRecords} سجل (صفحة ${currentPage} من ${totalPages})</p>`;
                tbody.parentNode.insertBefore(infoDiv, tbody);
            }

            // Update pagination controls
            updatePaginationControls();
        }
        // Export functions for column analysis table
        function exportTableToCSV() {
            exportTableAsCSV('columnsTable', 'column_analysis.csv');
        }

        function exportTableToExcel() {
            exportTableAsCSV('columnsTable', 'column_analysis.xlsx');
        }

        function exportTableToPDF() {
            alert('PDF تصدير غير متاح حالياً. يرجى استخدام تصدير CSV أو Excel.');
        }

        // Export functions for merge analysis table
        function exportMergeAnalysisToCSV() {
            exportTableAsCSV('personTable', 'merge_analysis.csv');
        }

        function exportMergeAnalysisToExcel() {
            exportTableAsCSV('personTable', 'merge_analysis.xlsx');
        }

        function exportMergeAnalysisToPDF() {
            alert('PDF تصدير غير متاح حالياً. يرجى استخدام تصدير CSV أو Excel.');
        }

        function exportMergeAnalysisToJSON() {
            const table = document.getElementById('personTable');
            if (!table) {
                alert('الجدول غير موجود');
                return;
            }
            const data = [];
            const headers = [];
            const headerCells = table.querySelectorAll('thead th');
            headerCells.forEach(cell => headers.push(cell.innerText.trim()));

            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const rowData = {};
                const cells = row.querySelectorAll('td');
                cells.forEach((cell, index) => {
                    if (headers[index]) {
                        rowData[headers[index]] = cell.innerText.trim();
                    }
                });
                if (Object.keys(rowData).length > 0) {
                    data.push(rowData);
                }
            });

            const jsonContent = JSON.stringify(data, null, 2);
            const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
            const link = document.createElement('a');
            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', 'merge_analysis.json');
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }

        // Helper function for CSV export
        function exportTableAsCSV(tableId, filename) {
            const table = document.getElementById(tableId);
            if (!table) {
                alert('الجدول غير موجود');
                return;
            }
            let csv = [];
            const rows = table.querySelectorAll('tr');
            for (let i = 0; i < rows.length; i++) {
                const row = [];
                const cols = rows[i].querySelectorAll('td, th');
                for (let j = 0; j < cols.length; j++) {
                    // Clean the text and escape quotes
                    let text = cols[j].innerText.trim().replace(/"/g, '""');
                    row.push('"' + text + '"');
                }
                csv.push(row.join(','));
            }
            const csvContent = csv.join('\n');
            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }

        // Quality Criteria Modal Functions
        function showCriterionDetails(criterion) {
            console.log('Showing details for criterion:', criterion);

            // Set progress bar width for overall modal
            if (criterion === 'overall') {
                const progressBar = document.querySelector('#overallModal .progress-bar');
                if (progressBar) {
                    const width = progressBar.getAttribute('data-width');
                    if (width) {
                        progressBar.style.width = width + '%';
                    }
                }
            }

            // Show the appropriate modal
            const modalId = criterion + 'Modal';
            const modal = new bootstrap.Modal(document.getElementById(modalId));
            modal.show();
        }

        // Export criterion data
        function exportCriterionData(criterion) {
            console.log('Exporting data for criterion:', criterion);

            // For now, show a placeholder message
            // In the future, this will call the Flask route for exporting criterion-specific reports
            alert('تصدير تقرير ' + criterion + ' قيد التطوير. سيتم إضافته قريباً.');
        }

    </script>
</body>
</html>

                        <!-- Merge Analysis for Insurance Data -->
                {% if analysis_data.analysis.sector_specific and analysis_data.analysis.sector_specific.get('merge_analysis') and analysis_data.analysis.sector_specific.merge_analysis.total_candidates > 0 %}
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-users-cog me-2"></i>
                            تحليل الدمج - الأشخاص ذوي الأرقام المتعددة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>تم العثور على {{ analysis_data.analysis.sector_specific.get('merge_analysis', {}).get('total_candidates', 0) }} مرشح للدمج</strong>
                            <br>
                            <small>أشخاص لديهم نفس البيانات الأساسية ولكن أرقام تأمينية/يدوية مختلفة</small>
                        </div>

                        <!-- Search and Filter Controls -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" id="mergeSearchInput" class="form-control" placeholder="البحث في الجدول..." onkeyup="filterMergeTable()">
                                    <button class="btn btn-outline-secondary" type="button" onclick="clearMergeSearch()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="btn-group" role="group">
                                        <select id="mergeNoteFilter" class="form-select form-select-sm me-2" onchange="filterMergeTable()">
                                            <option value="">جميع الملاحظات</option>
                                            <option value="تضارب">🚨 تضارب أرقام أساسية</option>
                                            <option value="يحتاج دمج">⚠️ يحتاج دمج</option>
                                            <option value="طبيعي">✅ طبيعي</option>
                                            <option value="مراجعة">❓ يحتاج مراجعة</option>
                                        </select>
                                        <button type="button" class="btn btn-sm btn-outline-info" onclick="resetMergeFilters()">
                                            <i class="fas fa-undo me-1"></i>
                                            إعادة تعيين
                                        </button>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <label class="form-label me-2 mb-0">عرض:</label>
                                        <select id="mergePageSize" class="form-select form-select-sm" style="width: auto;" onchange="changeMergePageSize()">
                                            <option value="10">10</option>
                                            <option value="25" selected>25</option>
                                            <option value="50">50</option>
                                            <option value="100">100</option>
                                        </select>
                                        <span class="ms-2 text-muted" id="mergeTotalCount">من أصل 0</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Export Controls -->
                        <div class="d-flex justify-content-center mb-3">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="exportMergeAnalysisToCSV()" title="تصدير البيانات كملف CSV">
                                    <i class="fas fa-file-csv me-1"></i>
                                    تصدير CSV
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-success" onclick="exportMergeAnalysisToExcel()" title="تصدير البيانات كملف Excel">
                                    <i class="fas fa-file-excel me-1"></i>
                                    تصدير Excel
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="exportMergeAnalysisToPDF()" title="تصدير البيانات كملف PDF">
                                    <i class="fas fa-file-pdf me-1"></i>
                                    تصدير PDF
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportMergeAnalysisToJSON()" title="تصدير البيانات كملف JSON">
                                    <i class="fas fa-file-code me-1"></i>
                                    تصدير JSON
                                </button>
                            </div>
                        </div>

                        <!-- جدول PersonRecord ديناميكي مع ترتيب -->
                        <div class="table-responsive" style="overflow-x: auto;">
                            <table id="personTable" class="table table-striped table-hover" style="min-width: 1400px; table-layout: fixed;">
                                <thead class="table-dark">
                                    <tr>
                                        <th class="sortable" onclick="sortMergeTable(0)" style="width: 220px; min-width: 220px;">
                                            <i class="fas fa-user me-2"></i>الاسم الكامل
                                            <i class="fas fa-sort sort-icon"></i>
                                        </th>
                                        <th class="sortable" onclick="sortMergeTable(1)" style="width: 120px; min-width: 120px;">
                                            <i class="fas fa-calendar-alt me-2"></i>تاريخ الميلاد
                                            <i class="fas fa-sort sort-icon"></i>
                                        </th>
                                        <th class="sortable" onclick="sortMergeTable(2)" style="width: 160px; min-width: 160px;">
                                            <i class="fas fa-id-card me-2"></i>الأرقام الأساسية
                                            <i class="fas fa-sort sort-icon"></i>
                                        </th>
                                        <th class="sortable" onclick="sortMergeTable(3)" style="width: 180px; min-width: 180px;">
                                            <i class="fas fa-edit me-2"></i>الأرقام اليدوية
                                            <i class="fas fa-sort sort-icon"></i>
                                        </th>
                                        <th class="sortable" onclick="sortMergeTable(4)" style="width: 140px; min-width: 140px;">
                                            <i class="fas fa-file-alt me-2"></i>رقم الوثيقة
                                            <i class="fas fa-sort sort-icon"></i>
                                        </th>
                                        <th class="sortable text-center" onclick="sortMergeTable(5)" style="width: 80px; min-width: 80px;">
                                            <i class="fas fa-hashtag me-2"></i>عدد الأرقام الأساسية
                                            <i class="fas fa-sort sort-icon"></i>
                                        </th>
                                        <th class="sortable text-center" onclick="sortMergeTable(6)" style="width: 80px; min-width: 80px;">
                                            <i class="fas fa-hashtag me-2"></i>عدد الأرقام اليدوية
                                            <i class="fas fa-sort sort-icon"></i>
                                        </th>
                                        <th class="sortable text-center" onclick="sortMergeTable(7)" style="width: 200px; min-width: 200px;">
                                            <i class="fas fa-exclamation-triangle me-2"></i>الملاحظة
                                            <i class="fas fa-sort sort-icon"></i>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم تعبئة الجدول تلقائيًا بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination Controls -->
                        <nav aria-label="Merge Analysis Pagination" class="mt-3">
                            <div class="d-flex justify-content-between align-items-center flex-wrap">
                                <div class="pagination-info mb-2 mb-md-0">
                                    <small class="text-muted">
                                        صفحة <strong id="currentPageNum">1</strong> من <strong id="totalPagesNum">1</strong>
                                    </small>
                                </div>
                                <ul class="pagination pagination-sm justify-content-center mb-0" id="mergePagination">
                                    <!-- Pagination will be generated by JavaScript -->
                                </ul>
                                <div class="pagination-jumper mb-2 mb-md-0">
                                    <small class="text-muted me-2">الانتقال إلى:</small>
                                    <input type="number" id="pageJumper" class="form-control form-control-sm d-inline-block" style="width: 60px;" min="1" onchange="jumpToPage(this.value)">
                                </div>
                            </div>
                        </nav>

                        <!-- Debug Info Container -->
                        <div id="mergeDebugInfo" style="display: block;">
                            <div class="alert alert-info mt-3">
                                <h6>معلومات التصحيح - تحليل الدمج:</h6>
                                <p><strong>هل البيانات موجودة؟</strong> {{ (analysis_data.analysis.sector_specific.get('merge_analysis') is not none) | string }}</p>
                                <p><strong>عدد المرشحين:</strong> {{ analysis_data.analysis.sector_specific.get('merge_analysis', {}).get('total_candidates', 0) }}</p>
                                <p><strong>عدد المرشحين في القائمة:</strong> {{ analysis_data.analysis.sector_specific.get('merge_analysis', {}).get('merge_candidates', [])|length }}</p>
                                <p><strong>مفاتيح البيانات:</strong> {{ analysis_data.analysis.sector_specific.keys() | list if analysis_data.analysis.sector_specific else 'لا توجد بيانات' }}</p>
                            </div>
                        </div>

                        <div class="mt-3">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="p-3 bg-primary bg-opacity-10 rounded">
                                        <div class="fw-bold text-primary h4 mb-1">{{ analysis_data.analysis.sector_specific.get('merge_analysis', {}).get('total_candidates', 0) }}</div>
                                        <small class="text-muted">إجمالي المرشحين</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="p-3 bg-success bg-opacity-10 rounded">
                                        <div class="fw-bold text-success h4 mb-1">{{ analysis_data.analysis.sector_specific.get('merge_analysis', {}).get('analyzed_persons', 0) }}</div>
                                        <small class="text-muted">الأشخاص المحللين</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="p-3 bg-info bg-opacity-10 rounded">
                                        <div class="fw-bold text-info h4 mb-1">{{ analysis_data.analysis.sector_specific.get('merge_analysis', {}).get('person_columns_found', 0) }}</div>
                                        <small class="text-muted">أعمدة الشخص المكتشفة</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="p-3 bg-warning bg-opacity-10 rounded">
                                        <div class="fw-bold text-warning h4 mb-1">{{ analysis_data.analysis.sector_specific.get('merge_analysis', {}).get('number_columns_found', 0) }}</div>
                                        <small class="text-muted">أعمدة الأرقام المكتشفة</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% else %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            لم يتم العثور على أشخاص يحتاجون دمج أرقامهم
                        </div>
                        {% endif %}
                    </div>
                </div>

                        <!-- PersonRecord Data Table - HIDDEN -->
                        <div class="card mb-4" style="display: none;">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-users me-2"></i>
                                    بيانات PersonRecord - أمثلة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover table-sky">
                                        <thead>
                                            <tr>
                                                <th><i class="fas fa-user me-2"></i>الاسم الكامل</th>
                                                <th><i class="fas fa-calendar-alt me-2"></i>تاريخ الميلاد</th>
                                                <th><i class="fas fa-id-card me-2"></i>الأرقام الأساسية</th>
                                                <th><i class="fas fa-edit me-2"></i>الأرقام اليدوية</th>
                                                <th><i class="fas fa-hashtag me-2"></i>عدد الأرقام الأساسية</th>
                                                <th><i class="fas fa-hashtag me-2"></i>عدد الأرقام اليدوية</th>
                                                <th><i class="fas fa-exclamation-triangle me-2"></i>الملاحظة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td class="person-name">محمد عمر محمد ناصر</td>
                                                <td>1981-06-11</td>
                                                <td class="insurance-numbers">295924 – 287748</td>
                                                <td class="manual-numbers">1956 – 264529 – 293893 – 246540</td>
                                                <td><span class="count-info">2</span></td>
                                                <td><span class="count-info">4</span></td>
                                                <td><span class="merge-reason">🚨 تضارب أرقام أساسية</span></td>
                                            </tr>
                                            <tr>
                                                <td class="person-name">أحمد محمد علي</td>
                                                <td>1990-01-01</td>
                                                <td class="insurance-numbers">123456</td>
                                                <td class="manual-numbers">111111</td>
                                                <td><span class="count-info">1</span></td>
                                                <td><span class="count-info">1</span></td>
                                                <td><span class="merge-reason">✅ طبيعي</span></td>
                                            </tr>
                                            <tr>
                                                <td class="person-name">فاطمة سالم حسن</td>
                                                <td>1985-05-05</td>
                                                <td class="insurance-numbers">234567</td>
                                                <td class="manual-numbers">222222 – 333333 – 444444</td>
                                                <td><span class="count-info">1</span></td>
                                                <td><span class="count-info">3</span></td>
                                                <td><span class="merge-reason">⚠️ يحتاج دمج</span></td>
                                            </tr>
                                            <tr>
                                                <td class="person-name">علي أحمد محمد</td>
                                                <td>1975-10-10</td>
                                                <td class="insurance-numbers">345678 – 456789 – 567890</td>
                                                <td class="manual-numbers">555555</td>
                                                <td><span class="count-info">3</span></td>
                                                <td><span class="count-info">1</span></td>
                                                <td><span class="merge-reason">🚨 تضارب أرقام أساسية</span></td>
                                            </tr>
                                            <tr>
                                                <td class="person-name">سارة يوسف أحمد</td>
                                                <td>1995-12-12</td>
                                                <td class="insurance-numbers"><span class="text-muted">لا يوجد</span></td>
                                                <td class="manual-numbers">666666 – 777777</td>
                                                <td><span class="count-info">0</span></td>
                                                <td><span class="count-info">2</span></td>
                                                <td><span class="merge-reason">❓ يحتاج مراجعة</span></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="mt-3">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>شرح الجدول:</strong>
                                        <ul class="mb-0 mt-2">
                                            <li><strong>الأرقام الأساسية:</strong> الأرقام التأمينية أو الآلية (بدون نصوص)</li>
                                            <li><strong>الأرقام اليدوية:</strong> الأرقام اليدوية المرتبطة (بدون نصوص)</li>
                                            <li><strong>الملاحظة:</strong> تحديد حالة السجل بناءً على عدد الأرقام</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Column Analysis -->
                        {% if analysis_data.analysis and analysis_data.analysis.column_analysis %}
                        <div class="card mb-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-table me-2"></i>
                                    تحليل الأعمدة
                                </h5>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="exportTableToCSV()">
                                        <i class="fas fa-file-csv me-1"></i>
                                        تصدير CSV
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="exportTableToExcel()">
                                        <i class="fas fa-file-excel me-1"></i>
                                        تصدير Excel
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="exportTableToPDF()">
                                        <i class="fas fa-file-pdf me-1"></i>
                                        تصدير PDF
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table id="columnsTable" class="table table-striped table-hover table-sky">
                                        <thead>
                                            <tr>
                                                <th class="sortable">
                                                    اسم العمود
                                                    <i class="fas fa-sort sort-icon"></i>
                                                </th>
                                                <th class="sortable">
                                                    النوع
                                                    <i class="fas fa-sort sort-icon"></i>
                                                </th>
                                                <th class="sortable">
                                                    القيم الفريدة
                                                    <i class="fas fa-sort sort-icon"></i>
                                                </th>
                                                <th class="sortable">
                                                    القيم المفقودة
                                                    <i class="fas fa-sort sort-icon"></i>
                                                </th>
                                                <th class="sortable">
                                                    نسبة المفقود
                                                    <i class="fas fa-sort sort-icon"></i>
                                                </th>
                                                <th class="sortable">
                                                    مكرر مرتين
                                                    <i class="fas fa-sort sort-icon"></i>
                                                </th>
                                                <th class="sortable">
                                                    مكرر 3+ مرات
                                                    <i class="fas fa-sort sort-icon"></i>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for col_name, col_data in analysis_data.analysis.column_analysis.items() %}
                                            {% set dup_data = {'duplicates_2': 0, 'duplicates_3_plus': 0} %}
                                            {% if analysis_data.column_duplicates and col_name in analysis_data.column_duplicates %}
                                                {% set dup_data = analysis_data.column_duplicates.get(col_name, {'duplicates_2': 0, 'duplicates_3_plus': 0}) %}
                                            {% endif %}
                                            <tr>
                                                <td class="fw-bold">
                                                    <strong>{{ col_name }}</strong>
                                                    <br><small class="text-muted">({{ col_name }})</small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary">
                                                        <i class="fas fa-font me-1"></i>
                                                        {{ col_data.type }}
                                                    </span>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge bg-info">
                                                        {{ "{:,}".format(col_data.unique_values) }}
                                                    </span>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge bg-warning text-dark">
                                                        {{ "{:,}".format(col_data.missing_count) }}
                                                    </span>
                                                    <br><small class="text-muted">Debug missing: {{ col_data.missing_count }}</small>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="progress flex-grow-1 me-2" style="height: 20px;">
                                                            <div class="progress-bar bg-danger" role="progressbar" data-percentage="{{ col_data.missing_percentage }}" aria-valuenow="{{ col_data.missing_percentage }}" aria-valuemin="0" aria-valuemax="100" style="width: 0%;">
                                                            </div>
                                                        </div>
                                                        <span class="badge bg-light text-dark">
                                                            {{ "%.1f"|format(col_data.missing_percentage) }}%
                                                        </span>
                                                    </div>
                                                    <small class="text-muted">Debug: {{ col_data.missing_percentage }}</small>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge bg-warning text-dark">
                                                        {{ "{:,}".format(dup_data.duplicates_2) }}
                                                    </span>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge bg-warning text-dark">
                                                        {{ "{:,}".format(dup_data.duplicates_3_plus) }}
                                                    </span>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Distributions -->
                        {% if analysis_data.analysis and analysis_data.analysis.distributions %}
                        <div class="distributions-section">
                            <div class="text-center mb-4">
                                <h4 class="mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    توزيعات البيانات
                                </h4>
                                <p class="text-muted mt-2">عرض توزيع القيم في كل عمود بشكل بصري</p>
                            </div>
                            <div class="row">
                                {% for col_name, distribution in analysis_data.analysis.distributions.items() %}
                                <div class="col-12 mb-5">
                                    <div class="card chart-card">
                                        <div class="card-header chart-header">
                                            <h5 class="mb-0 text-center">
                                                <i class="fas fa-chart-pie me-2"></i>
                                                {{ col_name }}
                                            </h5>
                                        </div>
                                        <div class="card-body">
                                            <!-- Chart Canvas - Full Width -->
                                            <div class="chart-wrapper-large mb-4">
                                                <canvas id="chart-{{ loop.index }}" class="chart-canvas-large"></canvas>
                                            </div>
                                        </div>

                                        <!-- Distribution Table and Stats in separate row -->
                                        <div class="row mt-3">
                                            <div class="col-md-8">
                                                <!-- Distribution Table -->
                                                <div class="table-responsive">
                                                    <table class="table table-sm table-striped table-hover distribution-table">
                                                        <thead>
                                                            <tr>
                                                                <th class="fw-bold">القيمة</th>
                                                                <th class="fw-bold text-center">العدد</th>
                                                                <th class="fw-bold text-center">النسبة (%)</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {% set total = distribution.values() | sum %}
                                                            {% for value, count in distribution.items() | sort(attribute='1', reverse=true) | list %}
                                                            {% if loop.index <= 15 %}
                                                            <tr>
                                                                <td class="text-truncate fw-medium" style="max-width: 250px;" title="{{ value }}">
                                                                    {% if value|length > 30 %}
                                                                        {{ value[:27] }}...
                                                                    {% else %}
                                                                        {{ value }}
                                                                    {% endif %}
                                                                </td>
                                                                <td class="text-center">
                                                                    <span class="badge bg-primary fs-6 px-3 py-1">{{ "{:,}".format(count) }}</span>
                                                                </td>
                                                                <td class="text-center">
                                                                    <span class="badge bg-success fs-6 px-3 py-1">{{ "%.1f"|format((count / total) * 100) }}</span>
                                                                </td>
                                                            </tr>
                                                            {% endif %}
                                                            {% endfor %}
                                                            {% if distribution|length > 15 %}
                                                            <tr class="table-light">
                                                                <td colspan="3" class="text-center text-muted small">
                                                                    و {{ distribution|length - 15 }} قيم أخرى...
                                                                </td>
                                                            </tr>
                                                            {% endif %}
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>

                                            <div class="col-md-4">
                                                <!-- Summary Statistics -->
                                                <div class="stats-summary-large">
                                                    <h6 class="text-center mb-3 fw-bold">إحصائيات العمود</h6>
                                                    <div class="row text-center">
                                                        <div class="col-12 mb-3">
                                                            <div class="p-3 bg-primary bg-opacity-10 rounded">
                                                                <div class="fw-bold text-primary h4 mb-1">{{ "{:,}".format(total) }}</div>
                                                                <small class="text-muted">إجمالي القيم</small>
                                                            </div>
                                                        </div>
                                                        <div class="col-12 mb-3">
                                                            <div class="p-3 bg-success bg-opacity-10 rounded">
                                                                <div class="fw-bold text-success h4 mb-1">{{ distribution|length }}</div>
                                                                <small class="text-muted">قيم فريدة</small>
                                                            </div>
                                                        </div>
                                                        <div class="col-12">
                                                            <div class="p-3 bg-info bg-opacity-10 rounded">
                                                                <div class="fw-bold text-info h4 mb-1">
                                                                    {% set max_count = distribution.values() | list | sort(reverse=true) | first %}
                                                                    {{ "%.1f"|format((max_count / total) * 100) }}%
                                                                </div>
                                                                <small class="text-muted">أكبر نسبة</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
