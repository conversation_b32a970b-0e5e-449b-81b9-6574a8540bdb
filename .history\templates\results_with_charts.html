<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نتائج التحليل - نظام تحليل البيانات</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #63a0dd;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(252, 248, 248, 0.1);
            margin-bottom: 20px;
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: rgb(248, 242, 242);
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }

        .interactive-charts-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin: 30px 0;
        }

        .chart-controls {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .chart-controls .form-select {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
        }

        .chart-controls .form-select option {
            background: #667eea;
            color: white;
        }

        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            height: 350px;
        }

        .chart-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .chart-card:hover {
            transform: translateY(-5px);
        }

        .btn-outline-light {
            border: 2px solid rgba(255,255,255,0.5);
            color: white;
        }

        .btn-outline-light:hover {
            background: rgba(255,255,255,0.2);
            border-color: white;
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">
                <i class="fas fa-chart-line me-2"></i>
                نظام تحليل جميع القطاعات
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-home me-1"></i>
                    الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            نتائج تحليل البيانات
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- Session ID -->
                        <div class="alert alert-info">
                            <i class="fas fa-id-badge me-2"></i>
                            <strong>معرف الجلسة:</strong> {{ session_id }}
                        </div>

                        <!-- Basic Info -->
                        {% if analysis_data %}
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h5><i class="fas fa-table me-2"></i>عدد الصفوف</h5>
                                        <h3>{{ analysis_data.basic_info.rows if analysis_data.basic_info else 'غير متاح' }}</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h5><i class="fas fa-columns me-2"></i>عدد الأعمدة</h5>
                                        <h3>{{ analysis_data.basic_info.columns if analysis_data.basic_info else 'غير متاح' }}</h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Interactive Column Charts Section -->
                        <div class="interactive-charts-section">
                            <div class="card-header border-0" style="background: transparent;">
                                <h4 class="mb-0 text-white text-center fw-bold">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    الرسوم البيانية التفاعلية للأعمدة
                                </h4>
                                <p class="mb-0 text-white-50 text-center mt-2">تحليل بصري تفاعلي لجميع أعمدة البيانات مع إمكانيات تخصيص متقدمة</p>
                            </div>
                            <div class="card-body p-4">
                                <!-- Chart Controls -->
                                <div class="chart-controls">
                                    <div class="row mb-3">
                                        <div class="col-md-3">
                                            <label for="chartType" class="form-label text-white fw-bold">
                                                <i class="fas fa-chart-bar me-2"></i>نوع الرسم البياني:
                                            </label>
                                            <select id="chartType" class="form-select" onchange="updateAllCharts()">
                                                <option value="bar">📊 أعمدة عمودية</option>
                                                <option value="horizontalBar">📈 أعمدة أفقية</option>
                                                <option value="pie">🥧 دائري</option>
                                                <option value="doughnut">🍩 دائري مفرغ</option>
                                                <option value="line">📉 خطي</option>
                                                <option value="radar">🎯 رادار</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="colorScheme" class="form-label text-white fw-bold">
                                                <i class="fas fa-palette me-2"></i>نظام الألوان:
                                            </label>
                                            <select id="colorScheme" class="form-select" onchange="updateAllCharts()">
                                                <option value="default">🎨 افتراضي</option>
                                                <option value="vibrant">🌈 ألوان زاهية</option>
                                                <option value="pastel">🌸 ألوان هادئة</option>
                                                <option value="dark">🌙 ألوان داكنة</option>
                                                <option value="gradient">✨ متدرج</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="maxValues" class="form-label text-white fw-bold">
                                                <i class="fas fa-list-ol me-2"></i>عدد القيم:
                                            </label>
                                            <select id="maxValues" class="form-select" onchange="updateAllCharts()">
                                                <option value="5">أفضل 5 قيم</option>
                                                <option value="10" selected>أفضل 10 قيم</option>
                                                <option value="15">أفضل 15 قيمة</option>
                                                <option value="20">أفضل 20 قيمة</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label text-white fw-bold">
                                                <i class="fas fa-cog me-2"></i>خيارات إضافية:
                                            </label>
                                            <div class="d-flex gap-2">
                                                <button type="button" class="btn btn-outline-light btn-sm" onclick="updateAllCharts()">
                                                    <i class="fas fa-sync me-1"></i>تحديث
                                                </button>
                                                <button type="button" class="btn btn-outline-light btn-sm" onclick="exportAllCharts()">
                                                    <i class="fas fa-download me-1"></i>تصدير
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Charts Container -->
                                <div id="interactiveChartsContainer" class="row">
                                    <!-- Charts will be generated here by JavaScript -->
                                </div>
                                
                                <!-- Export Options -->
                                <div class="text-center mt-4">
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-outline-light" onclick="exportChartsPNG()">
                                            <i class="fas fa-image me-1"></i>تصدير PNG
                                        </button>
                                        <button type="button" class="btn btn-outline-light" onclick="exportChartsPDF()">
                                            <i class="fas fa-file-pdf me-1"></i>تصدير PDF
                                        </button>
                                        <button type="button" class="btn btn-outline-light" onclick="printCharts()">
                                            <i class="fas fa-print me-1"></i>طباعة
                                        </button>
                                        <button type="button" class="btn btn-outline-light" onclick="shareCharts()">
                                            <i class="fas fa-share me-1"></i>مشاركة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Original Analysis Results -->
                        {% if analysis_data %}
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-table me-2"></i>
                                    تحليل الأعمدة
                                </h5>
                            </div>
                            <div class="card-body">
                                {% if analysis_data.analysis and analysis_data.analysis.column_analysis %}
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>اسم العمود</th>
                                                <th>النوع</th>
                                                <th>القيم الفريدة</th>
                                                <th>القيم المفقودة</th>
                                                <th>نسبة المفقود</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for col_name, col_data in analysis_data.analysis.column_analysis.items() %}
                                            <tr>
                                                <td><strong>{{ col_name }}</strong></td>
                                                <td><span class="badge bg-secondary">{{ col_data.type }}</span></td>
                                                <td>{{ col_data.unique_values }}</td>
                                                <td>{{ col_data.missing_count }}</td>
                                                <td>{{ "%.1f"|format(col_data.missing_percentage) }}%</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                {% else %}
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    لا توجد بيانات تحليل الأعمدة متاحة
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Interactive Charts Data and Functions
        let interactiveCharts = {};
        
        // Sample data for demonstration
        const sampleData = {
            'العمر': {'25': 8, '28': 12, '30': 15, '32': 10, '35': 5, '29': 7, '31': 6, '27': 9, '33': 4, '26': 8},
            'المدينة': {'الرياض': 20, 'جدة': 18, 'الدمام': 12, 'مكة': 8, 'المدينة': 6},
            'القسم': {'تقنية المعلومات': 15, 'المحاسبة': 12, 'التسويق': 10, 'الإدارة': 8, 'الموارد البشرية': 5},
            'التقييم': {'ممتاز': 18, 'جيد جداً': 15, 'جيد': 12, 'مقبول': 5}
        };
        
        // Color schemes
        const colorSchemes = {
            default: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'],
            vibrant: ['#FF3366', '#33CCFF', '#FFCC33', '#33FF99', '#CC33FF', '#FF6633', '#3366FF', '#FFFF33', '#FF33CC', '#33FFCC'],
            pastel: ['#FFB3BA', '#BAFFC9', '#BAE1FF', '#FFFFBA', '#FFDFBA', '#E0BBE4', '#957DAD', '#D291BC', '#FEC8D8', '#FFDFD3'],
            dark: ['#2C3E50', '#34495E', '#7F8C8D', '#95A5A6', '#BDC3C7', '#ECF0F1', '#E74C3C', '#E67E22', '#F39C12', '#F1C40F'],
            gradient: ['rgba(255, 99, 132, 0.8)', 'rgba(54, 162, 235, 0.8)', 'rgba(255, 206, 86, 0.8)', 'rgba(75, 192, 192, 0.8)', 'rgba(153, 102, 255, 0.8)']
        };
        
        function initializeInteractiveCharts() {
            console.log('🎨 Initializing interactive column charts...');
            
            // Get data from Flask template or use sample data
            let distributionsData;
            {% if analysis_data.analysis and analysis_data.analysis.distributions %}
            distributionsData = {{ analysis_data.analysis.distributions | tojson }};
            {% else %}
            distributionsData = sampleData;
            {% endif %}
            
            if (!distributionsData || Object.keys(distributionsData).length === 0) {
                console.log('❌ No distributions data found, using sample data');
                distributionsData = sampleData;
            }
            
            generateInteractiveCharts(distributionsData);
        }
        
        function generateInteractiveCharts(distributionsData) {
            const container = document.getElementById('interactiveChartsContainer');
            if (!container) return;
            
            container.innerHTML = '';
            
            Object.keys(distributionsData).forEach((columnName, index) => {
                const columnData = distributionsData[columnName];
                
                const chartHTML = `
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="chart-card h-100">
                            <div class="card-header bg-light border-0 py-3">
                                <h6 class="mb-0 text-center fw-bold text-dark">
                                    <i class="fas fa-chart-line me-2 text-primary"></i>
                                    ${columnName}
                                </h6>
                                <div class="text-center mt-2">
                                    <span class="badge bg-primary me-1">${Object.keys(columnData).length} قيمة فريدة</span>
                                    <span class="badge bg-secondary">نص</span>
                                </div>
                            </div>
                            <div class="card-body p-3">
                                <div class="chart-container">
                                    <canvas id="interactiveChart_${index}"></canvas>
                                </div>
                                
                                <!-- Column Stats -->
                                <div class="mt-3 p-2 bg-light rounded">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <small class="text-muted d-block">القيم الفريدة</small>
                                            <strong class="text-primary">${Object.keys(columnData).length}</strong>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted d-block">المفقودة</small>
                                            <strong class="text-danger">0</strong>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted d-block">النوع</small>
                                            <strong class="text-success">نص</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                container.insertAdjacentHTML('beforeend', chartHTML);
                
                // Create the chart
                setTimeout(() => {
                    createInteractiveChart(`interactiveChart_${index}`, columnData, columnName);
                }, 100);
            });
        }
        
        function createInteractiveChart(canvasId, data, title) {
            const canvas = document.getElementById(canvasId);
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            
            // Prepare data
            const entries = Object.entries(data);
            const maxValues = parseInt(document.getElementById('maxValues')?.value || 10);
            const sortedEntries = entries.sort((a, b) => b[1] - a[1]).slice(0, maxValues);
            
            const labels = sortedEntries.map(entry => String(entry[0]));
            const values = sortedEntries.map(entry => entry[1]);
            
            const selectedScheme = document.getElementById('colorScheme')?.value || 'default';
            const colors = colorSchemes[selectedScheme] || colorSchemes.default;
            
            const chartType = document.getElementById('chartType')?.value || 'bar';
            
            // Destroy existing chart if exists
            if (interactiveCharts[canvasId]) {
                interactiveCharts[canvasId].destroy();
            }
            
            // Create new chart
            interactiveCharts[canvasId] = new Chart(ctx, {
                type: chartType === 'horizontalBar' ? 'bar' : chartType,
                data: {
                    labels: labels,
                    datasets: [{
                        label: title,
                        data: values,
                        backgroundColor: colors.slice(0, values.length),
                        borderColor: colors.slice(0, values.length),
                        borderWidth: 2,
                        borderRadius: chartType === 'bar' || chartType === 'horizontalBar' ? 8 : 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: chartType === 'horizontalBar' ? 'y' : 'x',
                    plugins: {
                        legend: {
                            display: chartType === 'pie' || chartType === 'doughnut' || chartType === 'radar'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const total = values.reduce((a, b) => a + b, 0);
                                    const percentage = ((context.parsed.y || context.parsed) / total * 100).toFixed(1);
                                    return `${context.label}: ${context.parsed.y || context.parsed} (${percentage}%)`;
                                }
                            }
                        }
                    },
                    scales: chartType === 'pie' || chartType === 'doughnut' || chartType === 'radar' ? {} : {
                        y: {
                            beginAtZero: true
                        }
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeInOutQuart'
                    }
                }
            });
        }
        
        function updateAllCharts() {
            // Get current data
            let distributionsData;
            {% if analysis_data.analysis and analysis_data.analysis.distributions %}
            distributionsData = {{ analysis_data.analysis.distributions | tojson }};
            {% else %}
            distributionsData = sampleData;
            {% endif %}
            
            if (!distributionsData || Object.keys(distributionsData).length === 0) {
                distributionsData = sampleData;
            }
            
            Object.keys(distributionsData).forEach((columnName, index) => {
                const columnData = distributionsData[columnName];
                createInteractiveChart(`interactiveChart_${index}`, columnData, columnName);
            });
        }
        
        // Export functions
        function exportChartsPNG() {
            alert('🖼️ سيتم تصدير الرسوم البيانية كصور PNG');
        }
        
        function exportChartsPDF() {
            alert('📄 سيتم إنشاء تقرير PDF شامل');
        }
        
        function printCharts() {
            alert('🖨️ سيتم طباعة الرسوم البيانية');
        }
        
        function shareCharts() {
            alert('📤 سيتم مشاركة الرسوم البيانية');
        }
        
        function exportAllCharts() {
            alert('💾 سيتم تصدير جميع الرسوم البيانية');
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Page loaded, initializing charts...');
            
            // Wait for Chart.js to load
            setTimeout(() => {
                if (typeof Chart !== 'undefined') {
                    initializeInteractiveCharts();
                } else {
                    console.error('❌ Chart.js not loaded');
                    document.getElementById('interactiveChartsContainer').innerHTML = `
                        <div class="col-12">
                            <div class="alert alert-danger text-center">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>خطأ في تحميل مكتبة الرسوم البيانية</strong>
                                <p class="mb-0 mt-2">يرجى إعادة تحميل الصفحة أو التحقق من الاتصال بالإنترنت.</p>
                            </div>
                        </div>
                    `;
                }
            }, 1000);
        });
    </script>
</body>
</html>
