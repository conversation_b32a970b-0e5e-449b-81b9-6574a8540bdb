#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام تحليل جميع القطاعات - النسخة المتقدمة
Multi-Sector Data Analysis System - Advanced Version
"""

import os
import pandas as pd
import numpy as np
from flask import Flask, request, jsonify, render_template, send_file, redirect, url_for, flash, session
from flask_cors import CORS
import json
import threading
import time
from datetime import datetime, timedelta
import logging
import traceback
from werkzeug.utils import secure_filename
from werkzeug.security import generate_password_hash, check_password_hash
import uuid
from concurrent.futures import ThreadPoolExecutor, as_completed
import gc
import hashlib
import secrets
import re
from functools import wraps
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

# استيراد الإعدادات والمكونات
from config import *
from sector_manager import (
    SectorManager, BaseSectorAnalyzer, CustomsAnalyzer, InsuranceAnalyzer,
    EducationAnalyzer, HealthAnalyzer, FinanceAnalyzer, HRAnalyzer, GeneralAnalyzer
)
from backup_system import BackupSystem, create_backup_now, get_backup_list, get_backup_statistics
from performance_optimizer import performance_optimizer, cached, time_operation, optimize_dataframe
from ml_predictor import MLPredictor, analyze_column_ml

# إضافة معالج آمن للتعامل مع أخطاء الترميز
class SafeStreamHandler(logging.StreamHandler):
    def emit(self, record):
        try:
            # محاولة تسجيل الرسالة بالطريقة العادية
            super().emit(record)
        except (UnicodeEncodeError, UnicodeDecodeError, LookupError):
            # في حالة فشل الترميز، تنظيف الرسالة من جميع الأحرف غير ASCII
            try:
                # إزالة جميع الأحرف غير ASCII بما في ذلك الرموز التعبيرية
                clean_msg = ''.join(char for char in str(record.msg) if ord(char) < 128)
                if clean_msg.strip():  # التأكد من أن الرسالة ليست فارغة
                    record.msg = clean_msg
                    super().emit(record)
                else:
                    # إذا كانت الرسالة فارغة بعد التنظيف، إنشاء رسالة آمنة
                    safe_msg = f"[{record.levelname}] Message filtered due to encoding issues"
                    record.msg = safe_msg
                    super().emit(record)
            except Exception:
                # في حالة الفشل التام، تجاهل الرسالة
                pass

# إعداد السجلات مع المعالج الآمن
safe_handler = SafeStreamHandler()
safe_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))

# إزالة المعالجات الافتراضية وإضافة المعالج الآمن
root_logger = logging.getLogger()
for handler in root_logger.handlers[:]:
    root_logger.removeHandler(handler)

root_logger.addHandler(safe_handler)
root_logger.setLevel(logging.DEBUG)

logger = logging.getLogger(__name__)

# تم إصلاح مشكلة الترميز - إزالة الرموز التعبيرية من رسائل السجل
logger.info("=== بدء نظام تحليل جميع القطاعات ===")

# اختبار المعالج الآمن
try:
    logger.info("اختبار: رسالة عادية باللغة العربية")
    logger.info("اختبار: رسالة مع رمز تعبيري [SEARCH]")
    logger.info("اختبار: تم الاختبار بنجاح")
except Exception as test_e:
    logger.error(f"خطأ في اختبار المعالج الآمن: {test_e}")

# إضافة معالج لجميع الـ loggers الأخرى
for name in ['werkzeug', 'sector_manager', 'flask.app', 'flask']:
    other_logger = logging.getLogger(name)
    for handler in other_logger.handlers[:]:
        other_logger.removeHandler(handler)
    other_logger.addHandler(safe_handler)
    other_logger.setLevel(logging.INFO)

# تعطيل الرموز التعبيرية في werkzeug
import os
os.environ['WERKZEUG_RUN_MAIN'] = 'true'

# إنشاء تطبيق Flask
app = Flask(__name__)
CORS(app)

# إعدادات التطبيق
app.config['UPLOAD_FOLDER'] = os.path.join(os.path.dirname(os.path.abspath(__file__)), UPLOAD_FOLDER)
app.config['MAX_CONTENT_LENGTH'] = MAX_FILE_SIZE

# إعدادات Flask للجلسات مع تحسينات الأمان
app.config['SECRET_KEY'] = SESSION_SETTINGS['secret_key']
app.config['PERMANENT_SESSION_LIFETIME'] = SESSION_SETTINGS['permanent_session_lifetime']
app.config['SESSION_COOKIE_SECURE'] = SESSION_SETTINGS['session_cookie_secure']
app.config['SESSION_COOKIE_HTTPONLY'] = SESSION_SETTINGS['session_cookie_httponly']
app.config['SESSION_COOKIE_SAMESITE'] = SESSION_SETTINGS['session_cookie_samesite']

# إعدادات أمان إضافية
app.config['SESSION_PROTECTION'] = 'strong'

# التأكد من وجود مجلد الرفع
if not os.path.exists(app.config['UPLOAD_FOLDER']):
    os.makedirs(app.config['UPLOAD_FOLDER'])

# المتغيرات العامة
analysis_progress = {}
analysis_results = {}
active_sessions = {}
sector_manager = SectorManager()
backup_system = BackupSystem()
ml_predictor = MLPredictor()

# متغيرات الأمان
rate_limit_store = {}
failed_login_attempts = {}
account_lockouts = {}
security_logger = logging.getLogger('security')

# إعداد سجل الأمان
security_handler = logging.FileHandler('logs/security.log', encoding='utf-8')
security_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
security_logger.addHandler(security_handler)
security_logger.setLevel(logging.INFO)

def clear_analysis_data():
    """مسح جميع بيانات التحليل المخزنة"""
    global analysis_progress, analysis_results, active_sessions
    analysis_progress.clear()
    analysis_results.clear()
    active_sessions.clear()
    logger.info("[SUCCESS] تم مسح جميع بيانات التحليل")

def cleanup_expired_sessions():
    """تنظيف الجلسات المنتهية الصلاحية"""
    conn = get_db_connection()
    if not conn:
        return

    try:
        cursor = conn.cursor()
        current_time = datetime.now().isoformat()

        # تحديث الجلسات المنتهية الصلاحية
        cursor.execute("""
            UPDATE user_sessions
            SET is_active = 0, logout_time = ?
            WHERE is_active = 1 AND session_expiry < ?
        """, (current_time, current_time))

        expired_count = cursor.rowcount
        conn.commit()

        if expired_count > 0:
            logger.info(f"[CLEANUP] تم تنظيف {expired_count} جلسة منتهية الصلاحية")

    except Exception as e:
        logger.error(f"[ERROR] خطأ في تنظيف الجلسات المنتهية: {e}")
    finally:
        conn.close()

# دوال المصادقة والمستخدمين
def hash_password(password):
    """تشفير كلمة المرور"""
    return generate_password_hash(password, method='pbkdf2:sha256', salt_length=16)

def verify_password(password_hash, password):
    """التحقق من كلمة المرور"""
    return check_password_hash(password_hash, password)

@cached(ttl=60)  # Cache for 1 minute
@time_operation("user_lookup_by_username")
def get_user_by_username(username):
    """الحصول على بيانات المستخدم بالاسم"""
    conn = get_db_connection()
    if not conn:
        return None

    try:
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM users WHERE username = ? AND is_active = 1", (username,))
        user = cursor.fetchone()

        if user:
            columns = [desc[0] for desc in cursor.description]
            return dict(zip(columns, user))
        return None
    except Exception as e:
        logger.error(f"[ERROR] خطأ في الحصول على بيانات المستخدم: {e}")
        return None
    finally:
        conn.close()

@cached(ttl=60)  # Cache for 1 minute
@time_operation("user_lookup_by_id")
def get_user_by_id(user_id):
    """الحصول على بيانات المستخدم بالمعرف"""
    conn = get_db_connection()
    if not conn:
        return None

    try:
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM users WHERE user_id = ? AND is_active = 1", (user_id,))
        user = cursor.fetchone()

        if user:
            columns = [desc[0] for desc in cursor.description]
            return dict(zip(columns, user))
        return None
    except Exception as e:
        logger.error(f"[ERROR] خطأ في الحصول على بيانات المستخدم: {e}")
        return None
    finally:
        conn.close()

def create_user_session(user_id, ip_address, user_agent, remember_me=False):
    """إنشاء جلسة مستخدم جديدة"""
    session_id = str(uuid.uuid4())
    conn = get_db_connection()
    if not conn:
        return None

    try:
        cursor = conn.cursor()

        # حساب تاريخ انتهاء الجلسة
        if remember_me:
            # 30 يوم للجلسات الدائمة
            from datetime import timedelta
            expiry_date = datetime.now() + timedelta(days=30)
        else:
            # 24 ساعة للجلسات العادية
            from datetime import timedelta
            expiry_date = datetime.now() + timedelta(hours=24)

        cursor.execute("""
            INSERT INTO user_sessions (session_id, user_id, ip_address, user_agent, login_time, is_active, session_expiry, remember_me)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (session_id, user_id, ip_address, user_agent, datetime.now().isoformat(), 1, expiry_date.isoformat(), 1 if remember_me else 0))

        # تحديث وقت آخر دخول للمستخدم
        cursor.execute("""
            UPDATE users SET last_login = ? WHERE user_id = ?
        """, (datetime.now().isoformat(), user_id))

        conn.commit()
        logger.info(f"[SESSION] تم إنشاء جلسة جديدة: {session_id} (تذكرني: {remember_me}, تنتهي: {expiry_date.isoformat()})")
        return session_id
    except Exception as e:
        logger.error(f"[ERROR] خطأ في إنشاء جلسة المستخدم: {e}")
        return None
    finally:
        conn.close()

def get_user_permissions(user_role, sector_access=None):
    """الحصول على صلاحيات المستخدم"""
    if user_role not in USER_ROLES:
        return []

    permissions = USER_ROLES[user_role]['permissions']

    # إذا كان لدى المستخدم صلاحية "all"، أعطِه جميع الصلاحيات
    if 'all' in permissions:
        return ['upload', 'analyze', 'view', 'export', 'manage_users', 'manage_sector', 'admin']

    return permissions

def log_user_activity(user_id, action, resource_type=None, resource_id=None, details=None, ip_address=None):
    """تسجيل نشاط المستخدم"""
    conn = get_db_connection()
    if not conn:
        return

    try:
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO activity_log (user_id, action, resource_type, resource_id, details, ip_address)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (user_id, action, resource_type, resource_id, details, ip_address))
        conn.commit()
    except Exception as e:
        logger.error(f"[ERROR] خطأ في تسجيل نشاط المستخدم: {e}")
    finally:
        conn.close()

def validate_user_session(session_id):
    """التحقق من صحة جلسة المستخدم وتنظيف الجلسات المنتهية"""
    if not session_id:
        return False

    conn = get_db_connection()
    if not conn:
        return False

    try:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT session_expiry, is_active FROM user_sessions
            WHERE session_id = ? AND is_active = 1
        """, (session_id,))

        result = cursor.fetchone()
        if not result:
            return False

        session_expiry = result[0]
        if session_expiry:
            # التحقق من انتهاء صلاحية الجلسة
            expiry_date = datetime.fromisoformat(session_expiry)
            if datetime.now() > expiry_date:
                # إنهاء الجلسة المنتهية الصلاحية
                cursor.execute("""
                    UPDATE user_sessions SET is_active = 0, logout_time = ?
                    WHERE session_id = ?
                """, (datetime.now().isoformat(), session_id))
                conn.commit()
                logger.info(f"[SESSION] تم إنهاء الجلسة المنتهية الصلاحية: {session_id}")
                return False

        return True
    except Exception as e:
        logger.error(f"[ERROR] خطأ في التحقق من الجلسة: {e}")
        return False
    finally:
        conn.close()

def login_required(f):
    """ديكوراتور للتحقق من تسجيل الدخول"""
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session or 'session_id' not in session:
            flash('يجب تسجيل الدخول أولاً', 'warning')
            return redirect(url_for('login'))

        # التحقق من صحة الجلسة
        if not validate_user_session(session['session_id']):
            session.clear()
            flash('انتهت صلاحية جلستك، يرجى تسجيل الدخول مرة أخرى', 'warning')
            return redirect(url_for('login'))

        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

def require_permission(permission):
    """ديكوراتور للتحقق من الصلاحيات"""
    def decorator(f):
        def decorated_function(*args, **kwargs):
            if 'user_id' not in session:
                flash('يجب تسجيل الدخول أولاً', 'warning')
                return redirect(url_for('login'))

            user = get_user_by_id(session['user_id'])
            if not user:
                flash('المستخدم غير موجود', 'danger')
                return redirect(url_for('logout'))

            permissions = get_user_permissions(user['role'], user.get('sector_access'))
            if permission not in permissions:
                flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
                return redirect(url_for('index'))

            return f(*args, **kwargs)
        decorated_function.__name__ = f.__name__
        return decorated_function
    return decorator

# دوال مساعدة
def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def create_pdf_report(analysis_data, session_id):
    """إنشاء تقرير PDF لنتائج التحليل"""
    try:
        from io import BytesIO

        # إنشاء buffer للـ PDF
        buffer = BytesIO()

        # إنشاء مستند PDF
        doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)

        # قائمة العناصر
        elements = []

        # أنماط النص
        styles = getSampleStyleSheet()

        # نمط العنوان الرئيسي
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=1,  # وسط
            textColor=colors.darkblue
        )

        # نمط العناوين الفرعية
        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=20,
            textColor=colors.darkgreen
        )

        # نمط النص العادي
        normal_style = styles['Normal']

        # عنوان التقرير
        title = Paragraph("تقرير تحليل البيانات", title_style)
        elements.append(title)
        elements.append(Spacer(1, 12))

        # معلومات الملف
        file_info = analysis_data.get('file_info', {})
        file_name = file_info.get('name', 'غير محدد')
        file_rows = file_info.get('rows', 0)
        file_columns = file_info.get('columns', 0)

        file_info_text = f"""
        <b>معلومات الملف:</b><br/>
        اسم الملف: {file_name}<br/>
        عدد الصفوف: {file_rows:,}<br/>
        عدد الأعمدة: {file_columns}<br/>
        تاريخ التحليل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}<br/>
        معرف الجلسة: {session_id}
        """

        elements.append(Paragraph(file_info_text, normal_style))
        elements.append(Spacer(1, 20))

        # معلومات القطاع
        detected_sector = analysis_data.get('detected_sector', 'غير محدد')
        confidence = analysis_data.get('confidence', 0)

        sector_info = f"""
        <b>معلومات القطاع:</b><br/>
        القطاع المكتشف: {detected_sector}<br/>
        نسبة الثقة: {confidence:.1%}
        """

        elements.append(Paragraph(sector_info, normal_style))
        elements.append(Spacer(1, 20))

        # الإحصائيات الأساسية
        analysis = analysis_data.get('analysis', {})
        basic_stats = analysis.get('basic_stats', {})

        if basic_stats:
            elements.append(Paragraph("الإحصائيات الأساسية", heading_style))

            stats_data = [
                ['المقياس', 'القيمة'],
                ['إجمالي الصفوف', f"{basic_stats.get('total_rows', 0):,}"],
                ['إجمالي الأعمدة', f"{basic_stats.get('total_columns', 0):,}"],
                ['القيم المفقودة', f"{basic_stats.get('missing_values', 0):,}"],
                ['جودة البيانات', f"{basic_stats.get('data_quality_score', 0):.1f}%"]
            ]

            stats_table = Table(stats_data, colWidths=[200, 150])
            stats_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            elements.append(stats_table)
            elements.append(Spacer(1, 20))

        # تحليل الأعمدة
        column_analysis = analysis.get('column_analysis', {})
        if column_analysis:
            elements.append(Paragraph("تحليل الأعمدة", heading_style))

            # إنشاء جدول للأعمدة
            column_data = [['اسم العمود', 'النوع', 'فريد', 'مفقود', 'نسبة المفقود']]
            column_data[0] = [Paragraph(cell, styles['Heading4']) for cell in column_data[0]]

            for col_name, col_info in list(column_analysis.items())[:20]:  # أول 20 عمود فقط
                column_data.append([
                    str(col_name)[:30],  # تقصير الاسم إذا كان طويلاً
                    col_info.get('type', 'غير محدد'),
                    f"{col_info.get('unique_values', 0):,}",
                    f"{col_info.get('missing_count', 0):,}",
                    f"{col_info.get('missing_percentage', 0):.1f}%"
                ])

            if len(column_analysis) > 20:
                column_data.append(['...', '...', '...', '...', '...'])

            col_table = Table(column_data, colWidths=[120, 80, 60, 60, 80])
            col_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('FONTSIZE', (0, 1), (-1, -1), 8),
            ]))

            elements.append(col_table)
            elements.append(Spacer(1, 20))

        # تحليل الدقة (للبيانات الجمركية)
        sector_specific = analysis.get('sector_specific', {})
        if sector_specific and 'accuracy_analysis' in sector_specific:
            accuracy_data = sector_specific['accuracy_analysis']
            elements.append(Paragraph("تحليل دقة البيانات الجمركية", heading_style))

            # إحصائيات الدقة
            summary = accuracy_data.get('summary', {})
            if summary:
                accuracy_stats_data = [
                    ['المقياس', 'القيمة'],
                    ['إجمالي السجلات المفحصة', f"{summary.get('total_records', 0):,}"],
                    ['السجلات الصحيحة', f"{summary.get('valid_records', 0):,}"],
                    ['السجلات ذات المشاكل', f"{summary.get('problematic_records', 0):,}"],
                    ['نسبة الدقة العامة', f"{summary.get('accuracy_percentage', 0):.1f}%"]
                ]

                accuracy_table = Table(accuracy_stats_data, colWidths=[200, 150])
                accuracy_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.lightblue),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))

                elements.append(accuracy_table)
                elements.append(Spacer(1, 15))

            # تفاصيل المشاكل المكتشفة
            detailed_report = accuracy_data.get('detailed_report', [])
            if detailed_report:
                elements.append(Paragraph("تفاصيل المشاكل المكتشفة", ParagraphStyle(
                    'CustomSubHeading',
                    parent=styles['Heading3'],
                    fontSize=14,
                    spaceAfter=10,
                    textColor=colors.darkred
                )))

                # إنشاء جدول للتفاصيل (أول 20 سجل فقط للـ PDF)
                problem_data = [['الرقم التسلسلي', 'الكمية', 'القيمة', 'بلد المنشأ', 'المشاكل']]
                problem_data[0] = [Paragraph(cell, styles['Heading4']) for cell in problem_data[0]]

                for record in detailed_report[:20]:  # أول 20 سجل فقط
                    issues_text = ', '.join(record.get('issues', []))
                    if len(issues_text) > 50:  # تقصير النص الطويل
                        issues_text = issues_text[:47] + "..."

                    problem_data.append([
                        str(record.get('record_number', '')),
                        str(record.get('quantity', '')),
                        str(record.get('value', '')),
                        str(record.get('origin', '')),
                        issues_text
                    ])

                if len(detailed_report) > 20:
                    problem_data.append(['...', '...', '...', '...', '...'])

                problem_table = Table(problem_data, colWidths=[60, 60, 80, 80, 120])
                problem_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 8),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('FONTSIZE', (0, 1), (-1, -1), 7),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE')
                ]))

                elements.append(problem_table)
                elements.append(Spacer(1, 15))

                if len(detailed_report) > 20:
                    elements.append(Paragraph(f"ملاحظة: يتم عرض أول 20 سجل من أصل {len(detailed_report)} سجل", styles['Italic']))
                    elements.append(Spacer(1, 10))

            elements.append(Spacer(1, 20))

        # تحليل الدقة للبيانات الجمركية
        sector_specific = analysis.get('sector_specific', {})
        if sector_specific and 'accuracy_analysis' in sector_specific:
            accuracy_data = sector_specific['accuracy_analysis']
            elements.append(Paragraph("تحليل دقة البيانات الجمركية", heading_style))

            # إحصائيات الدقة الإجمالية
            summary = accuracy_data.get('summary', {}).get('overall_accuracy', {})
            if summary:
                summary_data = [
                    ['المقياس', 'القيمة'],
                    ['إجمالي السجلات المحللة', f"{summary.get('total_records', 0):,}"],
                    ['سجلات دقيقة', f"{summary.get('accurate_records', 0):,}"],
                    ['سجلات مشكوك فيها', f"{summary.get('suspicious_records', 0):,}"],
                    ['سجلات خاطئة', f"{summary.get('error_records', 0):,}"],
                    ['معدل الدقة الإجمالي', f"{summary.get('accuracy_rate', 0):.1f}%"]
                ]

                summary_table = Table(summary_data, colWidths=[200, 150])
                summary_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))

                elements.append(summary_table)
                elements.append(Spacer(1, 20))

            # تفاصيل السجلات (أول 20 سجل فقط للـ PDF)
            detailed_report = accuracy_data.get('detailed_report', [])
            if detailed_report:
                elements.append(Paragraph("تفاصيل السجلات المحللة (عينة)", styles['Heading3']))

                # إنشاء جدول للتفاصيل
                detail_data = [['رقم الصف', 'الكمية', 'القيمة', 'المنشأ', 'الدقة', 'الحالة']]
                detail_data[0] = [Paragraph(cell, styles['Heading4']) for cell in detail_data[0]]

                for record in detailed_report[:20]:  # أول 20 سجل فقط
                    status_text = {
                        'دقيق': 'دقيق',
                        'مشكوك فيه': 'مشكوك',
                        'خطأ': 'خطأ'
                    }.get(record.get('status', ''), record.get('status', ''))

                    detail_data.append([
                        str(record.get('row_index', '')),
                        str(record.get('data', {}).get('quantity', ''))[:20],  # تقصير النص
                        str(record.get('data', {}).get('value', ''))[:20],
                        str(record.get('data', {}).get('origin', ''))[:20],
                        f"{record.get('accuracy_percentage', 0):.1f}%",
                        status_text
                    ])

                if len(detailed_report) > 20:
                    detail_data.append(['...', '...', '...', '...', '...', '...'])

                detail_table = Table(detail_data, colWidths=[50, 80, 80, 80, 60, 70])
                detail_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 8),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('FONTSIZE', (0, 1), (-1, -1), 7),
                ]))

                elements.append(detail_table)
                elements.append(Spacer(1, 20))

        # تحليل الدقة للبيانات الجمركية
        sector_specific = analysis.get('sector_specific', {})
        if sector_specific and 'accuracy_analysis' in sector_specific:
            accuracy_data = sector_specific['accuracy_analysis']

            elements.append(Paragraph("تحليل دقة البيانات الجمركية", heading_style))
            elements.append(Spacer(1, 12))

            # إحصائيات الدقة
            summary = accuracy_data.get('summary', {}).get('overall_accuracy', {})
            accuracy_rate = summary.get('accuracy_rate', 0)

            accuracy_stats = f"""
            <b>إحصائيات الدقة الإجمالية:</b><br/>
            إجمالي السجلات المحللة: {summary.get('total_records', 0):,}<br/>
            السجلات الدقيقة: {summary.get('accurate_records', 0):,} ({summary.get('accurate_records', 0)/max(1, summary.get('total_records', 1))*100:.1f}%)<br/>
            السجلات المشكوك فيها: {summary.get('suspicious_records', 0):,} ({summary.get('suspicious_records', 0)/max(1, summary.get('total_records', 1))*100:.1f}%)<br/>
            السجلات الخاطئة: {summary.get('error_records', 0):,} ({summary.get('error_records', 0)/max(1, summary.get('total_records', 1))*100:.1f}%)<br/>
            معدل الدقة الإجمالي: {accuracy_rate:.1f}%
            """

            elements.append(Paragraph(accuracy_stats, normal_style))
            elements.append(Spacer(1, 12))

            # جدول تفصيلي لأول 20 سجل
            detailed_report = accuracy_data.get('detailed_report', [])
            if detailed_report:
                elements.append(Paragraph("تفاصيل التحليل (عينة من السجلات):", styles['Heading3']))
                elements.append(Spacer(1, 6))

                # إنشاء جدول للتفاصيل
                detail_data = [['الصف', 'الكمية', 'القيمة', 'بلد المنشأ', 'الدقة', 'الحالة', 'المشاكل']]
                detail_data[0] = [Paragraph(cell, styles['Heading4']) for cell in detail_data[0]]

                for record in detailed_report[:20]:  # أول 20 سجل فقط
                    data = record.get('data', {})
                    issues = record.get('issues', [])
                    issues_text = ', '.join(issues) if issues else 'لا توجد مشاكل'

                    row = [
                        str(record.get('row_index', '')),
                        str(data.get('quantity', ''))[:20],  # تقصير النص
                        str(data.get('value', ''))[:20],
                        str(data.get('origin', ''))[:20],
                        f"{record.get('accuracy_percentage', 0):.1f}%",
                        record.get('status', ''),
                        issues_text[:50]  # تقصير النص
                    ]
                    detail_data.append(row)

                if len(detailed_report) > 20:
                    detail_data.append(['...', '...', '...', '...', '...', '...', '...'])

                detail_table = Table(detail_data, colWidths=[40, 60, 60, 60, 50, 60, 100])
                detail_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 8),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 6),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('FONTSIZE', (0, 1), (-1, -1), 6),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ]))

                elements.append(detail_table)
                elements.append(Spacer(1, 12))

                if len(detailed_report) > 20:
                    elements.append(Paragraph(f"ملاحظة: تم عرض 20 سجل من أصل {len(detailed_report)} سجل. للحصول على التقرير الكامل، يرجى مراجعة الواجهة الإلكترونية.", styles['Italic']))
                    elements.append(Spacer(1, 6))

            elements.append(Spacer(1, 20))

        # التوصيات
        recommendations = analysis.get('recommendations', [])
        if recommendations:
            elements.append(Paragraph("التوصيات", heading_style))

            for rec in recommendations[:10]:  # أول 10 توصيات
                elements.append(Paragraph(f"• {rec}", normal_style))
                elements.append(Spacer(1, 6))

            elements.append(Spacer(1, 20))

        # تحليل الدقة للبيانات الجمركية
        sector_specific = analysis.get('sector_specific', {})
        if sector_specific and 'accuracy_analysis' in sector_specific:
            accuracy_data = sector_specific['accuracy_analysis']
            elements.append(Paragraph("تحليل دقة البيانات الجمركية", heading_style))

            # إحصائيات الدقة
            summary = accuracy_data.get('summary', {}).get('overall_accuracy', {})
            if summary:
                accuracy_stats = f"""
                <b>إحصائيات الدقة الإجمالية:</b><br/>
                إجمالي السجلات المحللة: {summary.get('total_records', 0):,}<br/>
                السجلات الدقيقة: {summary.get('accurate_records', 0):,} ({summary.get('accurate_records', 0)/max(1, summary.get('total_records', 1))*100:.1f}%)<br/>
                السجلات المشكوك فيها: {summary.get('suspicious_records', 0):,} ({summary.get('suspicious_records', 0)/max(1, summary.get('total_records', 1))*100:.1f}%)<br/>
                السجلات الخاطئة: {summary.get('error_records', 0):,} ({summary.get('error_records', 0)/max(1, summary.get('total_records', 1))*100:.1f}%)<br/>
                معدل الدقة الإجمالي: {summary.get('accuracy_rate', 0):.1f}%
                """

                elements.append(Paragraph(accuracy_stats, normal_style))
                elements.append(Spacer(1, 15))

            # جدول تفصيلي للدقة (أول 20 سجل فقط للـ PDF)
            detailed_report = accuracy_data.get('detailed_report', [])
            if detailed_report:
                elements.append(Paragraph("تفاصيل التحليل (عينة من السجلات):", styles['Heading3']))
                elements.append(Spacer(1, 10))

                # إنشاء جدول للتفاصيل
                accuracy_table_data = [
                    ['الصف', 'الكمية', 'القيمة', 'بلد المنشأ', 'نسبة الدقة', 'الحالة', 'المشاكل']
                ]

                for record in detailed_report[:20]:  # أول 20 سجل فقط
                    issues_text = ', '.join(record.get('issues', [])) if record.get('issues') else 'لا توجد مشاكل'
                    if len(issues_text) > 50:  # تقصير النص الطويل
                        issues_text = issues_text[:47] + '...'

                    row = [
                        str(record.get('row_index', '')),
                        str(record.get('data', {}).get('quantity', ''))[:20],  # تقصير النصوص
                        str(record.get('data', {}).get('value', ''))[:20],
                        str(record.get('data', {}).get('origin', ''))[:20],
                        f"{record.get('accuracy_percentage', 0):.1f}%",
                        record.get('status', ''),
                        issues_text
                    ]
                    accuracy_table_data.append(row)

                # إنشاء الجدول
                accuracy_table = Table(accuracy_table_data, colWidths=[40, 60, 60, 60, 60, 60, 100])
                accuracy_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('FONTSIZE', (0, 1), (-1, -1), 8),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ]))

                elements.append(accuracy_table)
                elements.append(Spacer(1, 15))

                if len(detailed_report) > 20:
                    elements.append(Paragraph(f"ملاحظة: تم عرض 20 سجل من أصل {len(detailed_report)} سجل. للحصول على التقرير الكامل، يرجى مراجعة الواجهة الإلكترونية.", styles['Italic']))
                    elements.append(Spacer(1, 10))

        # تحليل الدقة للبيانات الجمركية
        sector_specific = analysis.get('sector_specific', {})
        accuracy_analysis = sector_specific.get('accuracy_analysis')
        if accuracy_analysis:
            elements.append(Paragraph("تقرير تحليل الدقة - البيانات الجمركية", heading_style))

            # إحصائيات الدقة الإجمالية
            summary = accuracy_analysis.get('summary', {}).get('overall_accuracy', {})
            if summary:
                summary_data = [
                    ['المقياس', 'القيمة'],
                    ['إجمالي السجلات المحللة', f"{summary.get('total_records', 0):,}"],
                    ['سجلات دقيقة', f"{summary.get('accurate_records', 0):,}"],
                    ['سجلات مشكوك فيها', f"{summary.get('suspicious_records', 0):,}"],
                    ['سجلات خاطئة', f"{summary.get('error_records', 0):,}"],
                    ['معدل الدقة الإجمالي', f"{summary.get('accuracy_rate', 0):.1f}%"]
                ]

                summary_table = Table(summary_data, colWidths=[200, 150])
                summary_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))

                elements.append(summary_table)
                elements.append(Spacer(1, 15))

            # تفاصيل التحليل لأول 20 سجل فقط (للـ PDF)
            detailed_report = accuracy_analysis.get('detailed_report', [])
            if detailed_report:
                elements.append(Paragraph("تفاصيل التحليل (عينة من أول 20 سجل)", styles['Heading3']))

                # إنشاء جدول للتفاصيل
                detail_data = [['الصف', 'الكمية', 'القيمة', 'بلد المنشأ', 'الدقة', 'الحالة', 'المشاكل']]

                for record in detailed_report[:20]:  # أول 20 سجل فقط
                    issues = record.get('issues', [])
                    issues_text = ', '.join(issues) if issues else 'لا توجد مشاكل'

                    row = [
                        str(record.get('row_index', '')),
                        str(record.get('data', {}).get('quantity', ''))[:20],  # تقصير النص
                        str(record.get('data', {}).get('value', ''))[:20],
                        str(record.get('data', {}).get('origin', ''))[:15],
                        f"{record.get('accuracy_percentage', 0):.1f}%",
                        record.get('status', ''),
                        issues_text[:50]  # تقصير النص
                    ]
                    detail_data.append(row)

                # إنشاء الجدول مع أحجام أعمدة مناسبة
                col_widths = [40, 80, 80, 70, 60, 70, 120]
                detail_table = Table(detail_data, colWidths=col_widths, repeatRows=1)

                detail_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 8),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('FONTSIZE', (0, 1), (-1, -1), 7),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ]))

                elements.append(detail_table)
                elements.append(Spacer(1, 15))

                # إضافة ملاحظة إذا كان هناك المزيد من السجلات
                if len(detailed_report) > 20:
                    note_text = f"ملاحظة: يحتوي التقرير على {len(detailed_report)} سجل كاملاً. تم عرض أول 20 سجل فقط في هذا التقرير المختصر."
                    elements.append(Paragraph(note_text, styles['Italic']))
                    elements.append(Spacer(1, 10))

        # تذييل التقرير
        footer_text = f"""
        <i>تم إنشاء هذا التقرير بواسطة نظام تحليل جميع القطاعات في {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</i>
        """

        elements.append(Paragraph(footer_text, styles['Italic']))

        # إنشاء الـ PDF
        doc.build(elements)

        # إرجاع البيانات
        buffer.seek(0)
        return buffer.getvalue()

    except Exception as e:
        logger.error(f"[PDF_ERROR] خطأ في إنشاء تقرير PDF: {e}")
        return None

# دوال الأمان والحماية
def rate_limit(max_requests=10, window_seconds=60):
    """ديكوراتور للحد من معدل الطلبات"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            client_ip = request.remote_addr
            current_time = time.time()

            if client_ip not in rate_limit_store:
                rate_limit_store[client_ip] = []

            # تنظيف الطلبات القديمة
            rate_limit_store[client_ip] = [
                req_time for req_time in rate_limit_store[client_ip]
                if current_time - req_time < window_seconds
            ]

            # التحقق من الحد
            if len(rate_limit_store[client_ip]) >= max_requests:
                security_logger.warning(f"[RATE_LIMIT] IP {client_ip} exceeded rate limit for {request.endpoint}")
                return jsonify({'error': 'تم تجاوز حد معدل الطلبات. يرجى المحاولة لاحقاً.'}), 429

            # إضافة الطلب الحالي
            rate_limit_store[client_ip].append(current_time)

            return f(*args, **kwargs)
        return decorated_function
    return decorator

def validate_password_strength(password):
    """التحقق من قوة كلمة المرور"""
    if len(password) < 8:
        return False, "كلمة المرور يجب أن تكون 8 أحرف على الأقل"

    if not re.search(r'[A-Z]', password):
        return False, "كلمة المرور يجب أن تحتوي على حرف كبير على الأقل"

    if not re.search(r'[a-z]', password):
        return False, "كلمة المرور يجب أن تحتوي على حرف صغير على الأقل"

    if not re.search(r'\d', password):
        return False, "كلمة المرور يجب أن تحتوي على رقم على الأقل"

    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        return False, "كلمة المرور يجب أن تحتوي على رمز خاص على الأقل"

    return True, "كلمة المرور قوية"

def sanitize_input(text):
    """تنظيف المدخلات من الأحرف الخطرة"""
    if not text:
        return text

    # إزالة أحرف HTML الخطرة
    text = re.sub(r'<[^>]*>', '', text)

    # إزالة أحرف التحكم
    text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)

    # تحديد طول النص
    if len(text) > 1000:
        text = text[:1000] + "..."

    return text.strip()

def validate_file_content(file_path):
    """التحقق من محتوى الملف للأمان"""
    try:
        # التحقق من حجم الملف
        if os.path.getsize(file_path) > MAX_FILE_SIZE:
            return False, "حجم الملف كبير جداً"

        # التحقق من نوع الملف
        import magic
        mime = magic.Magic(mime=True)
        file_mime = mime.from_file(file_path)

        allowed_mimes = [
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/csv',
            'application/csv'
        ]

        if file_mime not in allowed_mimes:
            return False, f"نوع الملف غير مدعوم: {file_mime}"

        return True, "الملف آمن"

    except Exception as e:
        return False, f"خطأ في التحقق من الملف: {str(e)}"

def log_security_event(event_type, user_id=None, ip_address=None, details=None):
    """تسجيل حدث أمني"""
    event_data = {
        'event_type': event_type,
        'user_id': user_id,
        'ip_address': ip_address or request.remote_addr,
        'user_agent': request.headers.get('User-Agent', ''),
        'endpoint': request.endpoint,
        'method': request.method,
        'timestamp': datetime.now().isoformat(),
        'details': details
    }

    security_logger.info(f"[SECURITY_EVENT] {json.dumps(event_data, ensure_ascii=False)}")

def check_account_lockout(username):
    """التحقق من قفل الحساب"""
    if username in account_lockouts:
        lockout_time = account_lockouts[username]
        if datetime.now() < lockout_time:
            remaining_time = (lockout_time - datetime.now()).seconds // 60
            return True, f"الحساب مقفل لمدة {remaining_time} دقيقة"
        else:
            # إزالة القفل إذا انتهت مدته
            del account_lockouts[username]

    return False, None

def record_failed_login(username):
    """تسجيل محاولة تسجيل دخول فاشلة"""
    if username not in failed_login_attempts:
        failed_login_attempts[username] = []

    failed_login_attempts[username].append(datetime.now())

    # الاحتفاظ بآخر 5 محاولات فقط
    failed_login_attempts[username] = failed_login_attempts[username][-5:]

    # قفل الحساب إذا تجاوز 5 محاولات في 15 دقيقة
    recent_attempts = [attempt for attempt in failed_login_attempts[username]
                      if datetime.now() - attempt < timedelta(minutes=15)]

    if len(recent_attempts) >= 5:
        account_lockouts[username] = datetime.now() + timedelta(minutes=30)
        security_logger.warning(f"[ACCOUNT_LOCKOUT] Account locked for user: {username}")

def clear_failed_login_attempts(username):
    """مسح محاولات التسجيل الدخول الفاشلة"""
    if username in failed_login_attempts:
        del failed_login_attempts[username]
    if username in account_lockouts:
        del account_lockouts[username]

def add_security_headers(response):
    """إضافة رؤوس الأمان للاستجابة"""
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Strict-Transport-Security'] = 'max-age=********; includeSubDomains'
    response.headers['Content-Security-Policy'] = "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src 'self' data: https:; font-src 'self' https://cdnjs.cloudflare.com;"
    response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
    return response

def get_db_connection(max_retries=5, retry_delay=1.0):
    """إنشاء اتصال بقاعدة البيانات"""
    logger.debug(f"[DB] محاولة الاتصال بقاعدة البيانات: {DATABASE_TYPE}")

    for attempt in range(max_retries):
        try:
            if DATABASE_TYPE.lower() == 'sqlite':
                import sqlite3
                db_path = os.path.join(SQLITE_CONFIG['path'], SQLITE_CONFIG['database'])
                logger.debug(f"[DB] مسار قاعدة البيانات: {db_path}")

                if not os.path.exists(SQLITE_CONFIG['path']):
                    os.makedirs(SQLITE_CONFIG['path'])
                    logger.info(f"[DB] تم إنشاء مجلد قاعدة البيانات: {SQLITE_CONFIG['path']}")

                conn = sqlite3.connect(db_path, timeout=60.0)
                conn.execute('PRAGMA journal_mode=WAL')
                conn.execute('PRAGMA synchronous=NORMAL')

                logger.debug("[SUCCESS] تم إنشاء اتصال SQLite بنجاح")
                return conn

        except Exception as e:
            logger.warning(f"[DB] محاولة {attempt + 1}/{max_retries} فشلت: {e}")
            if attempt < max_retries - 1:
                logger.info(f"[DB] إعادة المحاولة خلال {retry_delay} ثانية")
                time.sleep(retry_delay)
            else:
                logger.error(f"[ERROR] فشل الاتصال بقاعدة البيانات بعد {max_retries} محاولات: {e}")
                logger.error(f"[ERROR] تفاصيل الخطأ: {traceback.format_exc()}")
                return None

def create_database_tables():
    """إنشاء جداول قاعدة البيانات"""
    conn = get_db_connection()
    if not conn:
        return False

    try:
        cursor = conn.cursor()

        # جدول المستخدمين
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                user_id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE,
                password_hash TEXT NOT NULL,
                full_name TEXT,
                role TEXT NOT NULL DEFAULT 'viewer',
                sector_access TEXT,  -- JSON array of allowed sectors
                is_active INTEGER DEFAULT 1,
                last_login TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # جدول جلسات المستخدمين
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_sessions (
                session_id TEXT PRIMARY KEY,
                user_id INTEGER,
                ip_address TEXT,
                user_agent TEXT,
                login_time TEXT DEFAULT CURRENT_TIMESTAMP,
                logout_time TEXT,
                is_active INTEGER DEFAULT 1,
                session_expiry TEXT,
                remember_me INTEGER DEFAULT 0,
                FOREIGN KEY (user_id) REFERENCES users (user_id)
            )
        """)

        # إضافة الحقول الجديدة إذا لم تكن موجودة (للتحديثات)
        try:
            cursor.execute("ALTER TABLE user_sessions ADD COLUMN session_expiry TEXT")
            logger.info("[DB] تم إضافة حقل session_expiry")
        except Exception as e:
            logger.debug(f"[DB] حقل session_expiry موجود بالفعل أو خطأ: {e}")

        try:
            cursor.execute("ALTER TABLE user_sessions ADD COLUMN remember_me INTEGER DEFAULT 0")
            logger.info("[DB] تم إضافة حقل remember_me")
        except Exception as e:
            logger.debug(f"[DB] حقل remember_me موجود بالفعل أو خطأ: {e}")

        # جدول إعدادات المستخدمين
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_settings (
                user_id INTEGER PRIMARY KEY,
                theme TEXT DEFAULT 'light',
                language TEXT DEFAULT 'ar',
                show_charts INTEGER DEFAULT 1,
                show_tables INTEGER DEFAULT 1,
                auto_refresh INTEGER DEFAULT 0,
                export_format TEXT DEFAULT 'excel',
                items_per_page INTEGER DEFAULT 50,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (user_id)
            )
        """)

        # جدول سجل العمليات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS activity_log (
                log_id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action TEXT NOT NULL,
                resource_type TEXT,
                resource_id TEXT,
                details TEXT,
                ip_address TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (user_id)
            )
        """)

        # جدول القطاعات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sectors (
                sector_id TEXT PRIMARY KEY,
                sector_name_ar TEXT,
                sector_name_en TEXT,
                description TEXT,
                icon TEXT,
                color TEXT,
                is_active INTEGER DEFAULT 1,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # جدول الجلسات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS analysis_sessions (
                session_id TEXT PRIMARY KEY,
                user_id INTEGER,
                sector_id TEXT,
                file_name TEXT,
                file_size INTEGER,
                upload_time TEXT,
                status TEXT,
                progress INTEGER DEFAULT 0,
                total_rows INTEGER,
                total_columns INTEGER,
                detected_sector TEXT,
                confidence REAL,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (user_id)
            )
        """)

        # جدول الأعمدة
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS data_columns (
                session_id TEXT,
                column_name TEXT,
                data_type TEXT,
                is_numeric INTEGER DEFAULT 0,
                unique_count INTEGER,
                null_count INTEGER,
                PRIMARY KEY (session_id, column_name)
            )
        """)

        # جدول نتائج التحليل
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS analysis_results (
                session_id TEXT,
                result_type TEXT,
                result_key TEXT,
                result_value TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (session_id, result_type, result_key)
            )
        """)

        # جدول توزيعات البيانات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS data_distributions (
                session_id TEXT,
                column_name TEXT,
                value TEXT,
                count INTEGER,
                percentage REAL,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (session_id, column_name, value)
            )
        """)

        # إدراج مستخدم افتراضي (admin)
        cursor.execute("""
            INSERT OR REPLACE INTO users (username, email, password_hash, full_name, role, sector_access, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, ('admin', '<EMAIL>', 'pbkdf2:sha256:1000000$l0kj5I9Hfoo1lFoQ$86be1214dd5022d41ed81069a66b35cc21a056bf3f80af3b9e5c077db56c1a12', 'مدير النظام', 'admin', '["all"]', 1))

        # إدراج إعدادات افتراضية للمستخدم الافتراضي
        cursor.execute("""
            INSERT OR IGNORE INTO user_settings (user_id, theme, language, show_charts, show_tables, auto_refresh, export_format, items_per_page)
            SELECT 1, 'light', 'ar', 1, 1, 0, 'excel', 50
            WHERE NOT EXISTS (SELECT 1 FROM user_settings WHERE user_id = 1)
        """)

        conn.commit()
        logger.info("[SUCCESS] تم إنشاء جداول قاعدة البيانات بنجاح")
        return True

    except Exception as e:
        logger.error(f"[ERROR] خطأ في إنشاء جداول قاعدة البيانات: {e}")
        return False
    finally:
        conn.close()

def improve_column_names(df, sample_size=100):
    """تحسين أسماء الأعمدة وإنشاء أسماء وصفية للأعمدة غير المسماة"""
    improved_columns = {}
    logger.info(f"[INFO] بدء تحسين أسماء الأعمدة: {list(df.columns)}")

    # قاموس الأسماء المحسنة للأعمدة الجمركية
    customs_column_names = {
        0: "رمز المنفذ",
        1: "المنفذ",
        2: "رقم البيان",
        3: "نوع البيان",
        4: "اسم المستورد",
        5: "الرقم الضريبي",
        6: "الشخص المسؤول عن الجانب المالي",
        7: "رقمه الضريبي",
        8: "اسم المصدر",
        9: "مرجع المصرح",
        10: "سنة المرجع",
        11: "الرقم الضريبي للمخلص",
        12: "اسم المخلص",
        13: "الحقل 18",
        14: "ع.ص",
        15: "رقم الصنف",
        16: "اسم السلعة",
        17: "البند التعريفي",
        18: "الماركة",
        19: "الباركود",
        20: "عدد الطرود",
        21: "الوحدة",
        22: "الوزن الصافي",
        23: "الوزن القائم",
        24: "الوحدات الإضافية",
        25: "الوحدات الإضافية A",
        26: "رمز بلد المنشأ",
        27: "رمز العملة",
        28: "القيمة بالأجنبي",
        29: "معدل الصرف",
        30: "معدل التعديل",
        31: "القيمة أساس الاحتساب",
        32: "القيمة الجمركية",
        33: "القيمة الجمركية بالريال الكامل",
        34: "ر.ج. الموسع",
        35: "ر.ج. الإضافي",
        36: "بلد التصدير",
        37: "رقم القعادة",
        38: "الموديل",
        39: "الطراز",
        40: "رقم المحرك",
        41: "الترسيم",
        42: "الاتفاقيات",
        43: "منفذ الدخول",
        44: "اسم منفذ الدخول",
        45: "المنافسة وتاريخه",
        46: "رقم البوليصة",
        47: "عدد الإيصالات",
        48: "أول إيصال",
        49: "تاريخه",
        50: "آخر إيصال",
        51: "تاريخه A",
        52: "رقم التصفية",
        53: "رقم الإيصال",
        54: "تاريخ التصفية",
        55: "تاريخ الإيصال",
        56: "تاريخ التسجيل"
    }

    for col in df.columns:
        original_name = str(col).strip()
        logger.info(f"[INFO] معالجة العمود: '{original_name}'")

        # إذا كان الاسم الأصلي جيد، احتفظ به
        if not original_name.startswith('Unnamed:') and len(original_name) > 2:
            improved_columns[col] = original_name
            logger.info(f"[SUCCESS] الاحتفاظ بالاسم الأصلي: '{original_name}'")
            continue

        # استخراج رقم العمود من Unnamed:X
        try:
            if ':' in original_name:
                col_num = int(original_name.split(':')[-1])
            else:
                col_num = int(original_name.replace('Unnamed:', '')) if 'Unnamed:' in original_name else 0
        except:
            col_num = 0

        # استخدام الاسم المحسن من القاموس إذا كان متوفراً
        if col_num in customs_column_names:
            suggested_name = customs_column_names[col_num]
            logger.info(f"[SUCCESS] استخدام الاسم المحسن من القاموس: '{suggested_name}' للعمود رقم {col_num}")
        else:
            # محاولة كشف نوع البيانات من العينة
            sample_data = df[col].dropna().head(sample_size)
            suggested_name = f"العمود {col_num + 1}"  # افتراضي

            if not sample_data.empty:
                sample_values = [str(val).strip() for val in sample_data.head(10) if str(val).strip()]
                logger.info(f"[DATA] عينة البيانات للعمود {col_num + 1}: {sample_values[:3]}...")

                # فحص إذا كانت البيانات رقمية
                try:
                    numeric_count = 0
                    for val in sample_values[:5]:
                        if val.replace('.', '').replace(',', '').replace('-', '').replace(' ', '').isdigit():
                            numeric_count += 1

                    if numeric_count >= 3:  # معظم القيم رقمية
                        if any('.' in val for val in sample_values if val.replace('.', '').replace(',', '').replace('-', '').replace(' ', '').isdigit()):
                            suggested_name = f"قيمة رقمية {col_num + 1}"
                        else:
                            suggested_name = f"رقم {col_num + 1}"
                    else:
                        # فحص النصوص
                        text_lengths = [len(val) for val in sample_values if len(val) > 0]
                        if text_lengths and sum(text_lengths) / len(text_lengths) > 20:
                            suggested_name = f"نص طويل {col_num + 1}"
                        else:
                            suggested_name = f"نص {col_num + 1}"
                except Exception as e:
                    logger.warning(f"[WARNING] خطأ في كشف نوع البيانات للعمود {col_num + 1}: {e}")
                    suggested_name = f"بيانات {col_num + 1}"

        improved_columns[col] = suggested_name
        logger.info(f"[UPDATE] تم تحسين الاسم من '{original_name}' إلى '{suggested_name}' (العمود رقم {col_num})")

    logger.info(f"[SUCCESS] تم تحسين أسماء {len(improved_columns)} عمود")
    print(f"[DEBUG] تم تحسين أسماء {len(improved_columns)} عمود")
    print(f"[DEBUG] أمثلة من الأسماء المحسنة: {dict(list(improved_columns.items())[:5])}")
    return improved_columns

def detect_column_types(df, sample_size=10000):
    """كشف أنواع الأعمدة مع دعم محسن للأحرف العربية"""
    column_types = {}

    arabic_keywords = ['ال', 'اسم', 'رقم', 'تاريخ', 'رمز', 'بلد', 'عملة', 'قيمة', 'وزن', 'عدد', 'سنة', 'مرجع']

    for col in df.columns:
        sample_data = df[col].dropna().head(sample_size)

        if sample_data.empty:
            column_types[col] = 'text'
            continue

        # كشف النوع التلقائي
        detected_type = 'text'

        # فحص البيانات لتجنب المشاكل مع السلاسل الطويلة
        valid_samples = []
        for val in sample_data:
            val_str = str(val).strip()
            # تجاهل القيم الفارغة أو السلاسل الطويلة جداً
            if val_str and len(val_str) < 50 and not val_str.count('.') > 10:
                valid_samples.append(val_str)

        if len(valid_samples) > 0:
            try:
                numeric_sample = pd.to_numeric(valid_samples, errors='coerce')
                if numeric_sample.notna().sum() > len(valid_samples) * 0.8:
                    detected_type = 'number'
            except Exception as e:
                logger.debug(f"خطأ في كشف نوع العمود {col}: {e}")
                pass

        # فحص إذا كان العمود عربي
        is_arabic_column = any(keyword in str(col) for keyword in arabic_keywords)

        if is_arabic_column and detected_type == 'number':
            column_types[col] = 'number'
        else:
            column_types[col] = detected_type

    return column_types

def analyze_column_duplicates(df, column_name, sample_size=10000):
    """تحليل القيم المتكررة في العمود"""
    try:
        # الحصول على عينة من البيانات
        sample_data = df[column_name].dropna().head(sample_size)

        if sample_data.empty:
            logger.debug(f"[DATA] العمود {column_name}: البيانات فارغة")
            return {'duplicates_2': 0, 'duplicates_3_plus': 0}

        # حساب التكرارات
        value_counts = sample_data.value_counts()
        logger.debug(f"[DATA] العمود {column_name}: إجمالي القيم الفريدة = {len(value_counts)}")

        # القيم المتكررة مرتين فقط
        duplicates_2 = (value_counts == 2).sum()

        # القيم المتكررة أكثر من مرتين
        duplicates_3_plus = (value_counts >= 3).sum()

        # إحصائيات إضافية للتحقق
        total_values = len(sample_data)
        unique_values = len(value_counts)
        max_count = value_counts.max() if not value_counts.empty else 0

        logger.debug(f"[DATA] العمود {column_name}: إجمالي القيم = {total_values}, فريدة = {unique_values}, أكبر تكرار = {max_count}")
        logger.debug(f"[DATA] العمود {column_name}: مكرر مرتين = {duplicates_2}, مكرر 3+ = {duplicates_3_plus}")

        return {
            'duplicates_2': int(duplicates_2),
            'duplicates_3_plus': int(duplicates_3_plus)
        }

    except Exception as e:
        logger.warning(f"خطأ في تحليل التكرارات للعمود {column_name}: {e}")
        return {'duplicates_2': 0, 'duplicates_3_plus': 0}

def is_valid_credit_card(number):
    """التحقق من صحة رقم البطاقة الائتمانية باستخدام خوارزمية Luhn"""
    try:
        # إزالة المسافات والشرطات
        number = str(number).replace(' ', '').replace('-', '')

        # التأكد من أن الرقم يحتوي على أرقام فقط
        if not number.isdigit():
            return False

        # عكس الرقم
        reversed_number = number[::-1]

        # حساب مجموع الأرقام مع تطبيق خوارزمية Luhn
        total = 0
        for i, digit in enumerate(reversed_number):
            digit = int(digit)
            if i % 2 == 1:  # كل رقم ثاني
                digit *= 2
                if digit > 9:
                    digit -= 9
            total += digit

        # الرقم صحيح إذا كان المجموع قابلاً للقسمة على 10
        return total % 10 == 0
    except:
        return False

def detect_person_duplicates_with_multiple_numbers(df, sample_size=50000):
    """كشف الأشخاص الذين لديهم أرقام تأمينية/يدوية متعددة مختلفة مع تصنيف الأرقام"""
    try:
        logger.info("[PERSON_DUPLICATES] بدء كشف الأشخاص ذوي الأرقام المتعددة")

        # البحث عن الأعمدة المطلوبة
        person_columns = {}
        number_columns = []

        # أعمدة تحديد الشخص - محدثة بأسماء أكثر شيوعاً
        person_keywords = {
            'name': [
                'اسم', 'الاسم', 'name', 'الاسم الكامل', 'اسم المؤمن', 'اسم الشخص',
                'الاسم الرباعي', 'اسم المستفيد', 'اسم المالك', 'اسم العميل',
                'اسم المستخدم', 'اسم المسجل', 'اسم المتقدم', 'اسم الطالب',
                'اسم المريض', 'اسم الموظف', 'اسم المدير', 'اسم المدير التنفيذي'
            ],
            'birth_date': [
                'تاريخ الميلاد', 'birth_date', 'birth', 'تاريخ الولادة', 'تاريخ ميلاد',
                'تاريخ الميلاد الهجري', 'تاريخ الميلاد الميلادي', 'تاريخ الإصدار',
                'تاريخ الإنشاء', 'تاريخ التسجيل', 'تاريخ الانضمام', 'تاريخ التوظيف'
            ],
            'id': [
                'البطاقة', 'الرقم التعريفي', 'id', 'رقم الهوية', 'رقم البطاقة',
                'رقم التعريف', 'رقم الهوية الوطنية', 'رقم الإقامة', 'رقم الجواز',
                'رقم الرخصة', 'رقم التسجيل', 'رقم العضوية', 'رقم الملف',
                'رقم السجل', 'رقم الطلب', 'رقم المرجع', 'رقم الشحنة'
            ],
            'mother': [
                'الأم', 'mother', 'أم', 'اسم الأم', 'اسم الوالدة', 'اسم الأب',
                'father', 'الأب', 'اسم الوالد', 'اسم الأبوين'
            ],
            'city': [
                'المدينة', 'city', 'مدينة', 'مدينة السكن', 'مدينة الإقامة',
                'المحافظة', 'المنطقة', 'الحي', 'القرية', 'المكان',
                'مكان السكن', 'مكان الإقامة', 'العنوان', 'عنوان السكن'
            ]
        }

        # أعمدة الأرقام مع تصنيفها - محدثة بأسماء أكثر شيوعاً
        number_keywords = {
            'insurance': [
                'تأميني', 'insurance', 'تأمين', 'D', 'العمود d', 'column d',
                'الرقم التأميني', 'رقم التأمين', 'رقم الوثيقة', 'رقم البوليصة',
                'الرقم الأساسي', 'الرقم الرئيسي', 'الرقم الآلي', 'رقم الآلة',
                'رقم الجهاز', 'رقم النظام', 'رقم السجل', 'رقم الملف',
                'رقم السياسة', 'رقم الضمان', 'رقم الغطاء', 'رقم المطالبة',
                'رقم التعويض', 'رقم الدفع', 'رقم الحساب', 'رقم العقد',
                'رقم الاتفاقية', 'رقم الشحنة', 'رقم الطلبية', 'رقم الفاتورة',
                'رقم المنشأة', 'رقم السجل التجاري', 'رقم الترخيص', 'رقم الرخصة',
                'رقم الشهادة', 'رقم الإجازة', 'رقم التصريح', 'رقم الإذن'
            ],
            'manual': [
                'يدوي', 'manual', 'يدوية', 'C', 'العمود c', 'column c',
                'الرقم اليدوي', 'رقم يدوي', 'رقم إضافي', 'رقم مرتبط',
                'رقم ثانوي', 'رقم مساعد', 'رقم فرعي', 'رقم بديل',
                'رقم الهاتف', 'رقم الجوال', 'رقم المنزل', 'رقم العمل',
                'رقم الاتصال', 'رقم الفاكس', 'رقم البريد', 'رقم الرمز',
                'رقم الكود', 'رقم المرجع', 'رقم التسلسل', 'رقم الترتيب',
                'رقم الحساب البنكي', 'رقم البنك', 'رقم الحساب', 'رقم الآيبان',
                'رقم البطاقة الائتمانية', 'رقم البطاقة', 'رقم الشيك', 'رقم التحويل'
            ]
        }

        # دالة لتحويل حرف العمود إلى رقم الفهرسة
        def column_letter_to_index(letter):
            """تحويل حرف العمود (A, B, C, D) إلى رقم الفهرسة (0, 1, 2, 3)"""
            if letter.isalpha() and len(letter) == 1:
                return ord(letter.upper()) - ord('A')
            return -1

        # العثور على الأعمدة المناسبة
        logger.info(f"[PERSON_DUPLICATES] البدء في البحث عن الأعمدة في {len(df.columns)} عمود")
        logger.info(f"[PERSON_DUPLICATES] أسماء الأعمدة: {list(df.columns)}")

        for idx, col in enumerate(df.columns):
            col_str = str(col).lower()
            col_name = str(col).strip()
            logger.debug(f"[PERSON_DUPLICATES] فحص العمود {idx}: '{col}' (الاسم المعالج: '{col_str}')")

            # البحث عن أعمدة تحديد الشخص
            for key, keywords in person_keywords.items():
                if any(keyword in col_str for keyword in keywords):
                    if key not in person_columns:
                        person_columns[key] = col
                        logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود {key} (لتحديد الشخص): {col}")
                    break

            # البحث عن أعمدة الأرقام مع تصنيفها
            for number_type, keywords in number_keywords.items():
                column_found = False

                detection_method = ""

                # التحقق من الكلمات المفتاحية في اسم العمود
                if any(keyword in col_str for keyword in keywords):
                    column_found = True
                    detection_method = "كلمات مفتاحية"

                # التحقق من حرف العمود (مثل D للتأميني، C لليدوي)
                if number_type == 'insurance' and ('d' in col_name.lower() or 'العمود d' in col_name.lower()):
                    column_found = True
                    detection_method = "حرف العمود D"
                elif number_type == 'manual' and ('c' in col_name.lower() or 'العمود c' in col_name.lower()):
                    column_found = True
                    detection_method = "حرف العمود C"

                # التحقق من فهرسة العمود (D=3, C=2, E=4, B=1, A=0)
                if number_type == 'insurance' and idx == 3:  # العمود D
                    column_found = True
                    detection_method = "فهرسة العمود D"
                elif number_type == 'insurance' and idx == 4:  # العمود E
                    column_found = True
                    detection_method = "فهرسة العمود E"
                elif number_type == 'manual' and idx == 2:  # العمود C
                    column_found = True
                    detection_method = "فهرسة العمود C"
                elif number_type == 'manual' and idx == 1:  # العمود B
                    column_found = True
                    detection_method = "فهرسة العمود B"
                elif number_type == 'manual' and idx == 0:  # العمود A
                    column_found = True
                    detection_method = "فهرسة العمود A"

                if column_found:
                    number_columns.append({
                        'column': col,
                        'type': number_type,
                        'type_arabic': {
                            'insurance': 'تأميني',
                            'manual': 'يدوي'
                        }.get(number_type, 'غير محدد'),
                        'column_index': idx,
                        'column_letter': chr(ord('A') + idx) if idx < 26 else f"A{chr(ord('A') + idx - 26)}",
                        'detection_method': detection_method
                    })
                    logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم {number_type} (تأميني/يدوي): {col} - فهرس: {idx} - حرف: {chr(ord('A') + idx)} - طريقة الكشف: {detection_method}")
                    break

        # إذا لم نجد عمود اسم بالطرق التقليدية، نبحث عن أعمدة تحتوي على نصوص طويلة (أسماء)
        if 'name' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود اسم بالطرق التقليدية، نبحث عن أعمدة تحتوي على نصوص...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد النصوص الطويلة في العينة (أسماء عادة ما تكون 3-50 حرف)
                        text_count = 0
                        arabic_text_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            if 3 <= len(val_str) <= 50:  # طول مناسب للأسماء
                                text_count += 1
                                # فحص إذا كان النص بالعربية
                                if any('\u0600' <= c <= '\u06FF' for c in val_str):
                                    arabic_text_count += 1

                        # إذا كان معظم القيم (60%+) نصوص طويلة، اعتبره عمود اسم
                        if text_count >= len(sample_data) * 0.6 and len(sample_data) >= 3:
                            person_columns['name'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود اسم تلقائياً: {col} - فهرس: {idx} ({text_count}/{len(sample_data)} نصوص)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود الاسم {col}: {e}")
                    continue

        # إذا لم نجد عمود تاريخ ميلاد بالطرق التقليدية، نبحث عن أعمدة تحتوي على تواريخ
        if 'birth_date' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود تاريخ ميلاد بالطرق التقليدية، نبحث عن أعمدة تحتوي على تواريخ...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد التواريخ في العينة
                        date_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط التاريخ الشائعة
                            import re
                            # أنماط تاريخ شائعة: YYYY-MM-DD, DD/MM/YYYY, DD-MM-YYYY, إلخ
                            date_patterns = [
                                r'\d{4}-\d{2}-\d{2}',  # 2023-12-25
                                r'\d{2}/\d{2}/\d{4}',  # 25/12/2023
                                r'\d{2}-\d{2}-\d{4}',  # 25-12-2023
                                r'\d{4}/\d{2}/\d{2}',  # 2023/12/25
                                r'\d{2}\.\d{2}\.\d{4}', # 25.12.2023
                            ]

                            is_date = False
                            for pattern in date_patterns:
                                if re.match(pattern, val_str):
                                    is_date = True
                                    break

                            # فحص إذا كانت القيمة تحتوي على كلمة "تاريخ" أو "birth"
                            if 'تاريخ' in val_str.lower() or 'birth' in val_str.lower() or 'date' in val_str.lower():
                                is_date = True

                            if is_date:
                                date_count += 1

                        # إذا كان معظم القيم (50%+) تواريخ، اعتبره عمود تاريخ ميلاد
                        if date_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['birth_date'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود تاريخ ميلاد تلقائياً: {col} - فهرس: {idx} ({date_count}/{len(sample_data)} تواريخ)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود التاريخ {col}: {e}")
                    continue

        # إذا لم نجد عمود مدينة بالطرق التقليدية، نبحث عن أعمدة تحتوي على أسماء أماكن
        if 'city' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود مدينة بالطرق التقليدية، نبحث عن أعمدة تحتوي على أسماء أماكن...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأسماء التي تبدو كأماكن
                        location_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص إذا كان النص يحتوي على أسماء مدن شائعة أو كلمات مكان
                            location_keywords = [
                                'الرياض', 'جدة', 'مكة', 'المدينة', 'الدمام', 'الخبر', 'الظهران',
                                'أبها', 'تبوك', 'حائل', 'القصيم', 'جازان', 'نجران', 'الباحة',
                                'الشارقة', 'دبي', 'أبوظبي', 'عجمان', 'رأس الخيمة', 'الفجيرة',
                                'الكويت', 'الدوحة', 'المنامة', 'مسقط', 'القاهرة', 'الإسكندرية',
                                'حي', 'شارع', 'طريق', 'منطقة', 'مدينة', 'قرية', 'محافظة'
                            ]

                            is_location = False
                            for keyword in location_keywords:
                                if keyword in val_str:
                                    is_location = True
                                    break

                            # فحص إذا كان النص قصيراً ويبدو كاسم مكان (3-30 حرف)
                            if 3 <= len(val_str) <= 30 and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أحرف عربية
                                if any('\u0600' <= c <= '\u06FF' for c in val_str):
                                    is_location = True

                            if is_location:
                                location_count += 1

                        # إذا كان معظم القيم (40%+) تبدو كأماكن، اعتبره عمود مدينة
                        if location_count >= len(sample_data) * 0.4 and len(sample_data) >= 3:
                            person_columns['city'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود مدينة تلقائياً: {col} - فهرس: {idx} ({location_count}/{len(sample_data)} أماكن)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود المدينة {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم هوية بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام هوية
        if 'id' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم هوية بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام هوية...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام هوية
                        id_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام الهوية الشائعة
                            # أرقام الهوية السعودية: 10 أرقام
                            # أرقام الإقامة: 10 أرقام
                            # أرقام البطاقات: 9-12 رقم
                            if val_str.isdigit():
                                val_len = len(val_str)
                                if 9 <= val_len <= 12:  # طول مناسب لأرقام الهوية
                                    id_count += 1
                                elif val_len == 10 and val_str.startswith(('1', '2')):  # أرقام سعودية تبدأ بـ 1 أو 2
                                    id_count += 1

                        # إذا كان معظم القيم (60%+) تبدو كأرقام هوية، اعتبره عمود رقم هوية
                        if id_count >= len(sample_data) * 0.6 and len(sample_data) >= 3:
                            person_columns['id'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم هوية تلقائياً: {col} - فهرس: {idx} ({id_count}/{len(sample_data)} أرقام هوية)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الهوية {col}: {e}")
                    continue

        # إذا لم نجد عمود اسم الأم بالطرق التقليدية، نبحث عن أعمدة تحتوي على أسماء نساء
        if 'mother' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود اسم الأم بالطرق التقليدية، نبحث عن أعمدة تحتوي على أسماء نساء...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأسماء التي تبدو كأسماء نساء
                        female_name_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص إذا كان النص يحتوي على أسماء نساء شائعة
                            female_name_keywords = [
                                'فاطمة', 'مريم', 'عائشة', 'خديجة', 'زينب', 'أم', 'أم كلثوم',
                                'سعدية', 'حسناء', 'جميلة', 'سلمى', 'نور', 'ليلى', 'سارة',
                                'هند', 'أسماء', 'مريم', 'فاطمة', 'زهراء', 'عائشة', 'حفصة',
                                'أم سلمة', 'أم حبيبة', 'أم الفضل', 'أم منصور', 'أم هانئ',
                                'بنت', 'البتول', 'الزهراء', 'الكبرى', 'الصغرى'
                            ]

                            is_female_name = False
                            for keyword in female_name_keywords:
                                if keyword in val_str:
                                    is_female_name = True
                                    break

                            # فحص إذا كان النص ينتهي بـ "ة" (علامة التأنيث في العربية)
                            if val_str.endswith('ة') and 3 <= len(val_str) <= 20:
                                is_female_name = True

                            if is_female_name:
                                female_name_count += 1

                        # إذا كان معظم القيم (40%+) تبدو كأسماء نساء، اعتبره عمود اسم الأم
                        if female_name_count >= len(sample_data) * 0.4 and len(sample_data) >= 3:
                            person_columns['mother'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود اسم الأم تلقائياً: {col} - فهرس: {idx} ({female_name_count}/{len(sample_data)} أسماء نساء)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود اسم الأم {col}: {e}")
                    continue

        # إذا لم نجد عمود اسم الأب بالطرق التقليدية، نبحث عن أعمدة تحتوي على أسماء رجال
        if 'father' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود اسم الأب بالطرق التقليدية، نبحث عن أعمدة تحتوي على أسماء رجال...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأسماء التي تبدو كأسماء رجال
                        male_name_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص إذا كان النص يحتوي على أسماء رجال شائعة
                            male_name_keywords = [
                                'محمد', 'أحمد', 'علي', 'حسن', 'عمر', 'خالد', 'سعد', 'عبدالله',
                                'عبدالعزيز', 'عبدالرحمن', 'عبدالكريم', 'عبدالرزاق', 'عبدالجبار',
                                'إبراهيم', 'إسماعيل', 'يوسف', 'يعقوب', 'عيسى', 'موسى', 'هارون',
                                'سلمان', 'سعود', 'فهد', 'طلال', 'بندر', 'نايف', 'نواف', 'مشعل',
                                'أب', 'أبو', 'بن', 'الكبير', 'الأكبر', 'الصغير', 'الأصغر'
                            ]

                            is_male_name = False
                            for keyword in male_name_keywords:
                                if keyword in val_str:
                                    is_male_name = True
                                    break

                            # فحص إذا كان النص لا ينتهي بـ "ة" (ليس مؤنث)
                            if not val_str.endswith('ة') and 3 <= len(val_str) <= 20 and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أحرف عربية
                                if any('\u0600' <= c <= '\u06FF' for c in val_str):
                                    is_male_name = True

                            if is_male_name:
                                male_name_count += 1

                        # إذا كان معظم القيم (40%+) تبدو كأسماء رجال، اعتبره عمود اسم الأب
                        if male_name_count >= len(sample_data) * 0.4 and len(sample_data) >= 3:
                            person_columns['father'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود اسم الأب تلقائياً: {col} - فهرس: {idx} ({male_name_count}/{len(sample_data)} أسماء رجال)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود اسم الأب {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم مرجع بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام مرجعية
        if 'reference' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم مرجع بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام مرجعية...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام مرجعية
                        reference_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام المرجع الشائعة
                            # أرقام المرجع: تحتوي على أرقام وحروف أو رموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 5 <= val_len <= 20:  # طول مناسب لأرقام المرجع
                                        reference_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام مرجعية، اعتبره عمود رقم مرجع
                        if reference_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['reference'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم مرجع تلقائياً: {col} - فهرس: {idx} ({reference_count}/{len(sample_data)} أرقام مرجعية)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم المرجع {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم منشأة بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام منشآت
        if 'facility' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم منشأة بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام منشآت...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام منشآت
                        facility_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام المنشآت الشائعة
                            # أرقام السجل التجاري السعودي: عادة 10-15 رقم
                            # أرقام المنشآت: عادة 8-12 رقم
                            if val_str.isdigit():
                                val_len = len(val_str)
                                if 8 <= val_len <= 15:  # طول مناسب لأرقام المنشآت
                                    # فحص إذا كان يبدأ بأرقام محددة للمنشآت
                                    if val_str.startswith(('1', '2', '3', '4', '5', '6', '7', '8', '9')):
                                        facility_count += 1

                        # إذا كان معظم القيم (60%+) تبدو كأرقام منشآت، اعتبره عمود رقم منشأة
                        if facility_count >= len(sample_data) * 0.6 and len(sample_data) >= 3:
                            person_columns['facility'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم منشأة تلقائياً: {col} - فهرس: {idx} ({facility_count}/{len(sample_data)} أرقام منشآت)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم المنشأة {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم حساب بنكي بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام حسابات
        if 'bank_account' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم حساب بنكي بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام حسابات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام حسابات بنكية
                        account_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام الحسابات البنكية الشائعة
                            # أرقام الحسابات البنكية: عادة 10-20 رقم
                            # رقم الآيبان: يبدأ بـ SA ويتبعه 22 رقم
                            if val_str.isdigit():
                                val_len = len(val_str)
                                if 10 <= val_len <= 25:  # طول مناسب لأرقام الحسابات
                                    account_count += 1
                            elif val_str.upper().startswith('SA') and len(val_str) == 24:
                                # رقم آيبان سعودي
                                account_count += 1

                        # إذا كان معظم القيم (60%+) تبدو كأرقام حسابات، اعتبره عمود رقم حساب
                        if account_count >= len(sample_data) * 0.6 and len(sample_data) >= 3:
                            person_columns['bank_account'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم حساب بنكي تلقائياً: {col} - فهرس: {idx} ({account_count}/{len(sample_data)} أرقام حسابات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الحساب البنكي {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم هاتف بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام هواتف
        if 'phone' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم هاتف بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام هواتف...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام هواتف
                        phone_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام الهواتف الشائعة
                            # أرقام الهواتف السعودية: 05xxxxxxxxx أو 9665xxxxxxxxx
                            if val_str.isdigit():
                                val_len = len(val_str)
                                if val_len == 10 and val_str.startswith('05'):  # رقم سعودي محلي
                                    phone_count += 1
                                elif val_len == 12 and val_str.startswith('966'):  # رقم سعودي دولي
                                    phone_count += 1
                                elif val_len == 9 and val_str.startswith('5'):  # رقم بدون 0
                                    phone_count += 1
                            elif val_str.replace(' ', '').replace('-', '').replace('(', '').replace(')', '').isdigit():
                                # رقم مع فواصل أو مسافات
                                clean_number = val_str.replace(' ', '').replace('-', '').replace('(', '').replace(')', '')
                                if len(clean_number) >= 9:
                                    phone_count += 1

                        # إذا كان معظم القيم (60%+) تبدو كأرقام هواتف، اعتبره عمود رقم هاتف
                        if phone_count >= len(sample_data) * 0.6 and len(sample_data) >= 3:
                            person_columns['phone'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم هاتف تلقائياً: {col} - فهرس: {idx} ({phone_count}/{len(sample_data)} أرقام هواتف)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الهاتف {col}: {e}")
                    continue

        # إذا لم نجد عمود بريد إلكتروني بالطرق التقليدية، نبحث عن أعمدة تحتوي على عناوين بريد
        if 'email' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود بريد إلكتروني بالطرق التقليدية، نبحث عن أعمدة تحتوي على عناوين بريد...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد العناوين التي تبدو كعناوين بريد إلكتروني
                        email_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط عناوين البريد الإلكتروني
                            import re
                            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
                            if re.match(email_pattern, val_str):
                                email_count += 1
                            elif '@' in val_str and '.' in val_str:
                                # فحص بسيط للعناوين التي تحتوي على @ و .
                                email_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كعناوين بريد، اعتبره عمود بريد إلكتروني
                        if email_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['email'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود بريد إلكتروني تلقائياً: {col} - فهرس: {idx} ({email_count}/{len(sample_data)} عناوين بريد)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود البريد الإلكتروني {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم بطاقة ائتمانية بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام بطاقات
        if 'credit_card' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم بطاقة ائتمانية بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام بطاقات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام بطاقات ائتمانية
                        card_count = 0
                        for val in sample_data:
                            val_str = str(val).strip().replace(' ', '').replace('-', '')
                            # فحص أنماط أرقام البطاقات الائتمانية
                            # أرقام البطاقات: عادة 13-19 رقم
                            if val_str.isdigit():
                                val_len = len(val_str)
                                if 13 <= val_len <= 19:  # طول مناسب لأرقام البطاقات
                                    # فحص صحة رقم البطاقة باستخدام خوارزمية Luhn
                                    if is_valid_credit_card(val_str):
                                        card_count += 1

                        # إذا كان معظم القيم (60%+) تبدو كأرقام بطاقات، اعتبره عمود رقم بطاقة
                        if card_count >= len(sample_data) * 0.6 and len(sample_data) >= 3:
                            person_columns['credit_card'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم بطاقة ائتمانية تلقائياً: {col} - فهرس: {idx} ({card_count}/{len(sample_data)} أرقام بطاقات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم البطاقة الائتمانية {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم ضمان اجتماعي بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام ضمان
        if 'social_security' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم ضمان اجتماعي بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام ضمان...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام ضمان اجتماعي
                        ss_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام الضمان الاجتماعي
                            # أرقام الضمان السعودي: عادة 9-12 رقم
                            if val_str.isdigit():
                                val_len = len(val_str)
                                if 9 <= val_len <= 12:  # طول مناسب لأرقام الضمان
                                    # فحص إذا كان يبدأ بأرقام محددة للضمان
                                    if val_str.startswith(('1', '2')) and val_len == 9:
                                        ss_count += 1

                        # إذا كان معظم القيم (60%+) تبدو كأرقام ضمان، اعتبره عمود رقم ضمان
                        if ss_count >= len(sample_data) * 0.6 and len(sample_data) >= 3:
                            person_columns['social_security'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم ضمان اجتماعي تلقائياً: {col} - فهرس: {idx} ({ss_count}/{len(sample_data)} أرقام ضمان)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الضمان الاجتماعي {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم ترخيص بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام تراخيص
        if 'license' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم ترخيص بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام تراخيص...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام تراخيص
                        license_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام التراخيص
                            # أرقام التراخيص: عادة تحتوي على أرقام وحروف
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)

                                if has_digits and has_letters:
                                    val_len = len(val_str)
                                    if 5 <= val_len <= 15:  # طول مناسب لأرقام التراخيص
                                        license_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام تراخيص، اعتبره عمود رقم ترخيص
                        if license_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['license'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم ترخيص تلقائياً: {col} - فهرس: {idx} ({license_count}/{len(sample_data)} أرقام تراخيص)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الترخيص {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم شهادة بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام شهادات
        if 'certificate' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم شهادة بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام شهادات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام شهادات
                        cert_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام الشهادات
                            # أرقام الشهادات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام الشهادات
                                        cert_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام شهادات، اعتبره عمود رقم شهادة
                        if cert_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['certificate'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم شهادة تلقائياً: {col} - فهرس: {idx} ({cert_count}/{len(sample_data)} أرقام شهادات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الشهادة {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم شحنة بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام شحنات
        if 'shipment' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم شحنة بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام شحنات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام شحنات
                        shipment_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام الشحنات
                            # أرقام الشحنات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 8 <= val_len <= 25:  # طول مناسب لأرقام الشحنات
                                        shipment_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام شحنات، اعتبره عمود رقم شحنة
                        if shipment_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['shipment'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم شحنة تلقائياً: {col} - فهرس: {idx} ({shipment_count}/{len(sample_data)} أرقام شحنات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الشحنة {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم فاتورة بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام فواتير
        if 'invoice' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم فاتورة بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام فواتير...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام فواتير
                        invoice_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام الفواتير
                            # أرقام الفواتير: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام الفواتير
                                        invoice_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام فواتير، اعتبره عمود رقم فاتورة
                        if invoice_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['invoice'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم فاتورة تلقائياً: {col} - فهرس: {idx} ({invoice_count}/{len(sample_data)} أرقام فواتير)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الفاتورة {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم عقد بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام عقود
        if 'contract' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم عقد بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام عقود...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام عقود
                        contract_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام العقود
                            # أرقام العقود: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 7 <= val_len <= 25:  # طول مناسب لأرقام العقود
                                        contract_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام عقود، اعتبره عمود رقم عقد
                        if contract_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['contract'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم عقد تلقائياً: {col} - فهرس: {idx} ({contract_count}/{len(sample_data)} أرقام عقود)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم العقد {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم اتفاقية بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام اتفاقيات
        if 'agreement' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم اتفاقية بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام اتفاقيات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام اتفاقيات
                        agreement_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام الاتفاقيات
                            # أرقام الاتفاقيات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 8 <= val_len <= 25:  # طول مناسب لأرقام الاتفاقيات
                                        agreement_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام اتفاقيات، اعتبره عمود رقم اتفاقية
                        if agreement_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['agreement'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم اتفاقية تلقائياً: {col} - فهرس: {idx} ({agreement_count}/{len(sample_data)} أرقام اتفاقيات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الاتفاقية {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم دفع بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام دفعات
        if 'payment' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم دفع بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام دفعات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام دفعات
                        payment_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام الدفعات
                            # أرقام الدفعات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام الدفعات
                                        payment_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام دفعات، اعتبره عمود رقم دفع
                        if payment_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['payment'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم دفع تلقائياً: {col} - فهرس: {idx} ({payment_count}/{len(sample_data)} أرقام دفعات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الدفع {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم تعويض بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام تعويضات
        if 'compensation' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم تعويض بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام تعويضات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام تعويضات
                        comp_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام التعويضات
                            # أرقام التعويضات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام التعويضات
                                        comp_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام تعويضات، اعتبره عمود رقم تعويض
                        if comp_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['compensation'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم تعويض تلقائياً: {col} - فهرس: {idx} ({comp_count}/{len(sample_data)} أرقام تعويضات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم التعويض {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم غطاء بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام تغطية
        if 'coverage' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم غطاء بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام تغطية...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام تغطية
                        coverage_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام التغطية
                            # أرقام التغطية: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام التغطية
                                        coverage_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام تغطية، اعتبره عمود رقم غطاء
                        if coverage_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['coverage'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم غطاء تلقائياً: {col} - فهرس: {idx} ({coverage_count}/{len(sample_data)} أرقام تغطية)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الغطاء {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم سياسة بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام سياسات
        if 'policy' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم سياسة بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام سياسات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام سياسات
                        policy_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام السياسات
                            # أرقام السياسات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام السياسات
                                        policy_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام سياسات، اعتبره عمود رقم سياسة
                        if policy_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['policy'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم سياسة تلقائياً: {col} - فهرس: {idx} ({policy_count}/{len(sample_data)} أرقام سياسات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم السياسة {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم طلب بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام طلبات
        if 'request' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم طلب بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام طلبات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام طلبات
                        request_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام الطلبات
                            # أرقام الطلبات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام الطلبات
                                        request_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام طلبات، اعتبره عمود رقم طلب
                        if request_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['request'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم طلب تلقائياً: {col} - فهرس: {idx} ({request_count}/{len(sample_data)} أرقام طلبات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الطلب {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم مطالبة بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام مطالبات
        if 'claim' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم مطالبة بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام مطالبات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام مطالبات
                        claim_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام المطالبات
                            # أرقام المطالبات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام المطالبات
                                        claim_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام مطالبات، اعتبره عمود رقم مطالبة
                        if claim_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['claim'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم مطالبة تلقائياً: {col} - فهرس: {idx} ({claim_count}/{len(sample_data)} أرقام مطالبات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم المطالبة {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم موافقة بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام موافقات
        if 'approval' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم موافقة بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام موافقات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام موافقات
                        approval_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام الموافقات
                            # أرقام الموافقات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام الموافقات
                                        approval_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام موافقات، اعتبره عمود رقم موافقة
                        if approval_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['approval'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم موافقة تلقائياً: {col} - فهرس: {idx} ({approval_count}/{len(sample_data)} أرقام موافقات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الموافقة {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم معاملة بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام معاملات
        if 'transaction' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم معاملة بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام معاملات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام معاملات
                        transaction_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام المعاملات
                            # أرقام المعاملات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام المعاملات
                                        transaction_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام معاملات، اعتبره عمود رقم معاملة
                        if transaction_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['transaction'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم معاملة تلقائياً: {col} - فهرس: {idx} ({transaction_count}/{len(sample_data)} أرقام معاملات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم المعاملة {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم مستند بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام مستندات
        if 'document' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم مستند بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام مستندات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام مستندات
                        document_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام المستندات
                            # أرقام المستندات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام المستندات
                                        document_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام مستندات، اعتبره عمود رقم مستند
                        if document_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['document'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم مستند تلقائياً: {col} - فهرس: {idx} ({document_count}/{len(sample_data)} أرقام مستندات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم المستند {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم سجل بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام سجلات
        if 'record' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم سجل بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام سجلات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام سجلات
                        record_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام السجلات
                            # أرقام السجلات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام السجلات
                                        record_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام سجلات، اعتبره عمود رقم سجل
                        if record_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['record'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم سجل تلقائياً: {col} - فهرس: {idx} ({record_count}/{len(sample_data)} أرقام سجلات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم السجل {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إشعار بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إشعارات
        if 'notification' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إشعار بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إشعارات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إشعارات
                        notification_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام الإشعارات
                            # أرقام الإشعارات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام الإشعارات
                                        notification_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إشعارات، اعتبره عمود رقم إشعار
                        if notification_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['notification'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إشعار تلقائياً: {col} - فهرس: {idx} ({notification_count}/{len(sample_data)} أرقام إشعارات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الإشعار {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم تقرير بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام تقارير
        if 'report' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم تقرير بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام تقارير...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام تقارير
                        report_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام التقارير
                            # أرقام التقارير: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام التقارير
                                        report_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام تقارير، اعتبره عمود رقم تقرير
                        if report_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['report'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم تقرير تلقائياً: {col} - فهرس: {idx} ({report_count}/{len(sample_data)} أرقام تقارير)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم التقرير {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إجراء بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إجراءات
        if 'procedure' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إجراء بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إجراءات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إجراءات
                        procedure_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام الإجراءات
                            # أرقام الإجراءات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام الإجراءات
                                        procedure_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إجراءات، اعتبره عمود رقم إجراء
                        if procedure_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['procedure'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إجراء تلقائياً: {col} - فهرس: {idx} ({procedure_count}/{len(sample_data)} أرقام إجراءات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الإجراء {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم تحويل بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام تحويلات
        if 'transfer' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم تحويل بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام تحويلات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام تحويلات
                        transfer_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام التحويلات
                            # أرقام التحويلات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام التحويلات
                                        transfer_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام تحويلات، اعتبره عمود رقم تحويل
                        if transfer_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['transfer'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم تحويل تلقائياً: {col} - فهرس: {idx} ({transfer_count}/{len(sample_data)} أرقام تحويلات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم التحويل {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إيداع بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إيداعات
        if 'deposit' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إيداع بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إيداعات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إيداعات
                        deposit_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام الإيداعات
                            # أرقام الإيداعات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام الإيداعات
                                        deposit_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إيداعات، اعتبره عمود رقم إيداع
                        if deposit_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['deposit'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إيداع تلقائياً: {col} - فهرس: {idx} ({deposit_count}/{len(sample_data)} أرقام إيداعات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الإيداع {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم سحب بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام سحوبات
        if 'withdrawal' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم سحب بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام سحوبات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام سحوبات
                        withdrawal_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام السحوبات
                            # أرقام السحوبات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام السحوبات
                                        withdrawal_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام سحوبات، اعتبره عمود رقم سحب
                        if withdrawal_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['withdrawal'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم سحب تلقائياً: {col} - فهرس: {idx} ({withdrawal_count}/{len(sample_data)} أرقام سحوبات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم السحب {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إصدار بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إصدارات
        if 'issue' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إصدار بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إصدارات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إصدارات
                        issue_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام الإصدارات
                            # أرقام الإصدارات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام الإصدارات
                                        issue_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إصدارات، اعتبره عمود رقم إصدار
                        if issue_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['issue'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إصدار تلقائياً: {col} - فهرس: {idx} ({issue_count}/{len(sample_data)} أرقام إصدارات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الإصدار {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إلغاء بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إلغاءات
        if 'cancellation' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إلغاء بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إلغاءات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إلغاءات
                        cancellation_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام الإلغاءات
                            # أرقام الإلغاءات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام الإلغاءات
                                        cancellation_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إلغاءات، اعتبره عمود رقم إلغاء
                        if cancellation_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['cancellation'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إلغاء تلقائياً: {col} - فهرس: {idx} ({cancellation_count}/{len(sample_data)} أرقام إلغاءات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الإلغاء {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم تجديد بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام تجديدات
        if 'renewal' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم تجديد بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام تجديدات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام تجديدات
                        renewal_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام التجديدات
                            # أرقام التجديدات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام التجديدات
                                        renewal_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام تجديدات، اعتبره عمود رقم تجديد
                        if renewal_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['renewal'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم تجديد تلقائياً: {col} - فهرس: {idx} ({renewal_count}/{len(sample_data)} أرقام تجديدات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم التجديد {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادات
        if 'return' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادات
                        return_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام الإعادات
                            # أرقام الإعادات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام الإعادات
                                        return_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادات، اعتبره عمود رقم إعادة
                        if return_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['return'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة تلقائياً: {col} - فهرس: {idx} ({return_count}/{len(sample_data)} أرقام إعادات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الإعادة {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم استبدال بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام استبدالات
        if 'replacement' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم استبدال بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام استبدالات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام استبدالات
                        replacement_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام الاستبدالات
                            # أرقام الاستبدالات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام الاستبدالات
                                        replacement_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام استبدالات، اعتبره عمود رقم استبدال
                        if replacement_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['replacement'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم استبدال تلقائياً: {col} - فهرس: {idx} ({replacement_count}/{len(sample_data)} أرقام استبدالات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الاستبدال {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إصلاح بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إصلاحات
        if 'repair' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إصلاح بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إصلاحات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إصلاحات
                        repair_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام الإصلاحات
                            # أرقام الإصلاحات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام الإصلاحات
                                        repair_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إصلاحات، اعتبره عمود رقم إصلاح
                        if repair_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['repair'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إصلاح تلقائياً: {col} - فهرس: {idx} ({repair_count}/{len(sample_data)} أرقام إصلاحات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الإصلاح {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم تحقق بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام تحققات
        if 'verification' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم تحقق بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام تحققات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام تحققات
                        verification_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام التحققات
                            # أرقام التحققات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام التحققات
                                        verification_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام تحققات، اعتبره عمود رقم تحقق
                        if verification_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['verification'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم تحقق تلقائياً: {col} - فهرس: {idx} ({verification_count}/{len(sample_data)} أرقام تحققات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم التحقق {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إدارة بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إدارات
        if 'administration' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إدارة بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إدارات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إدارات
                        administration_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام الإدارات
                            # أرقام الإدارات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام الإدارات
                                        administration_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إدارات، اعتبره عمود رقم إدارة
                        if administration_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['administration'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إدارة تلقائياً: {col} - فهرس: {idx} ({administration_count}/{len(sample_data)} أرقام إدارات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الإدارة {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم تنفيذ بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام تنفيذات
        if 'execution' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم تنفيذ بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام تنفيذات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام تنفيذات
                        execution_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام التنفيذات
                            # أرقام التنفيذات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام التنفيذات
                                        execution_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام تنفيذات، اعتبره عمود رقم تنفيذ
                        if execution_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['execution'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم تنفيذ تلقائياً: {col} - فهرس: {idx} ({execution_count}/{len(sample_data)} أرقام تنفيذات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم التنفيذ {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إنجاز بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إنجازات
        if 'completion' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إنجاز بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إنجازات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إنجازات
                        completion_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام الإنجازات
                            # أرقام الإنجازات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام الإنجازات
                                        completion_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إنجازات، اعتبره عمود رقم إنجاز
                        if completion_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['completion'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إنجاز تلقائياً: {col} - فهرس: {idx} ({completion_count}/{len(sample_data)} أرقام إنجازات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الإنجاز {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إنهاء بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إنهاءات
        if 'termination' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إنهاء بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إنهاءات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إنهاءات
                        termination_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام الإنهاءات
                            # أرقام الإنهاءات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام الإنهاءات
                                        termination_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إنهاءات، اعتبره عمود رقم إنهاء
                        if termination_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['termination'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إنهاء تلقائياً: {col} - فهرس: {idx} ({termination_count}/{len(sample_data)} أرقام إنهاءات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الإنهاء {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إيقاف بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إيقافات
        if 'suspension' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إيقاف بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إيقافات...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إيقافات
                        suspension_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام الإيقافات
                            # أرقام الإيقافات: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام الإيقافات
                                        suspension_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إيقافات، اعتبره عمود رقم إيقاف
                        if suspension_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['suspension'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إيقاف تلقائياً: {col} - فهرس: {idx} ({suspension_count}/{len(sample_data)} أرقام إيقافات)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الإيقاف {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة تنشيط بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة تنشيط
        if 'reactivation' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة تنشيط بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة تنشيط...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة تنشيط
                        reactivation_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة التنشيط
                            # أرقام إعادة التنشيط: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة التنشيط
                                        reactivation_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة تنشيط، اعتبره عمود رقم إعادة تنشيط
                        if reactivation_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['reactivation'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة تنشيط تلقائياً: {col} - فهرس: {idx} ({reactivation_count}/{len(sample_data)} أرقام إعادة تنشيط)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة التنشيط {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إلغاء مؤقت بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إلغاء مؤقت
        if 'temporary_cancellation' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إلغاء مؤقت بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إلغاء مؤقت...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إلغاء مؤقت
                        temp_cancel_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام الإلغاء المؤقت
                            # أرقام الإلغاء المؤقت: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام الإلغاء المؤقت
                                        temp_cancel_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إلغاء مؤقت، اعتبره عمود رقم إلغاء مؤقت
                        if temp_cancel_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['temporary_cancellation'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إلغاء مؤقت تلقائياً: {col} - فهرس: {idx} ({temp_cancel_count}/{len(sample_data)} أرقام إلغاء مؤقت)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم الإلغاء المؤقت {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة تفعيل بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة تفعيل
        if 're_enabling' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة تفعيل بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة تفعيل...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة تفعيل
                        re_enable_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة التفعيل
                            # أرقام إعادة التفعيل: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة التفعيل
                                        re_enable_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة تفعيل، اعتبره عمود رقم إعادة تفعيل
                        if re_enable_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['re_enabling'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة تفعيل تلقائياً: {col} - فهرس: {idx} ({re_enable_count}/{len(sample_data)} أرقام إعادة تفعيل)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة التفعيل {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة تشغيل بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة تشغيل
        if 'restarting' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة تشغيل بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة تشغيل...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة تشغيل
                        restarting_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة التشغيل
                            # أرقام إعادة التشغيل: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة التشغيل
                                        restarting_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة تشغيل، اعتبره عمود رقم إعادة تشغيل
                        if restarting_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['restarting'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة تشغيل تلقائياً: {col} - فهرس: {idx} ({restarting_count}/{len(sample_data)} أرقام إعادة تشغيل)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة التشغيل {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة ربط بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة ربط
        if 'reconnection' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة ربط بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة ربط...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة ربط
                        reconnection_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الربط
                            # أرقام إعادة الربط: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الربط
                                        reconnection_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة ربط، اعتبره عمود رقم إعادة ربط
                        if reconnection_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['reconnection'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة ربط تلقائياً: {col} - فهرس: {idx} ({reconnection_count}/{len(sample_data)} أرقام إعادة ربط)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الربط {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إصدار بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إصدار
        if 'reissuance' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إصدار بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إصدار...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إصدار
                        reissuance_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإصدار
                            # أرقام إعادة الإصدار: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإصدار
                                        reissuance_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إصدار، اعتبره عمود رقم إعادة إصدار
                        if reissuance_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['reissuance'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إصدار تلقائياً: {col} - فهرس: {idx} ({reissuance_count}/{len(sample_data)} أرقام إعادة إصدار)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإصدار {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة تحديث بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة تحديث
        if 'reupdate' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة تحديث بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة تحديث...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة تحديث
                        reupdate_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة التحديث
                            # أرقام إعادة التحديث: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة التحديث
                                        reupdate_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة تحديث، اعتبره عمود رقم إعادة تحديث
                        if reupdate_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['reupdate'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة تحديث تلقائياً: {col} - فهرس: {idx} ({reupdate_count}/{len(sample_data)} أرقام إعادة تحديث)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة التحديث {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال
        if 'resending' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال
                        resending_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال
                            # أرقام إعادة الإرسال: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال
                                        resending_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال، اعتبره عمود رقم إعادة إرسال
                        if resending_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال تلقائياً: {col} - فهرس: {idx} ({resending_count}/{len(sample_data)} أرقام إعادة إرسال)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إنشاء بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إنشاء
        if 'recreation' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إنشاء بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إنشاء...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إنشاء
                        recreation_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإنشاء
                            # أرقام إعادة الإنشاء: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإنشاء
                                        recreation_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إنشاء، اعتبره عمود رقم إعادة إنشاء
                        if recreation_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['recreation'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إنشاء تلقائياً: {col} - فهرس: {idx} ({recreation_count}/{len(sample_data)} أرقام إعادة إنشاء)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإنشاء {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إصلاح بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إصلاح
        if 'refixing' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إصلاح بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إصلاح...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إصلاح
                        refixing_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإصلاح
                            # أرقام إعادة الإصلاح: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإصلاح
                                        refixing_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إصلاح، اعتبره عمود رقم إعادة إصلاح
                        if refixing_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['refixing'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إصلاح تلقائياً: {col} - فهرس: {idx} ({refixing_count}/{len(sample_data)} أرقام إعادة إصلاح)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإصلاح {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال ثاني بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثاني
        if 'resending_second' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال ثاني بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثاني...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال ثاني
                        resending_second_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الثاني
                            # أرقام إعادة الإرسال الثاني: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الثاني
                                        resending_second_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال ثاني، اعتبره عمود رقم إعادة إرسال ثاني
                        if resending_second_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_second'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال ثاني تلقائياً: {col} - فهرس: {idx} ({resending_second_count}/{len(sample_data)} أرقام إعادة إرسال ثاني)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الثاني {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال ثالث بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثالث
        if 'resending_third' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال ثالث بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثالث...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال ثالث
                        resending_third_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الثالث
                            # أرقام إعادة الإرسال الثالث: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الثالث
                                        resending_third_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال ثالث، اعتبره عمود رقم إعادة إرسال ثالث
                        if resending_third_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_third'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال ثالث تلقائياً: {col} - فهرس: {idx} ({resending_third_count}/{len(sample_data)} أرقام إعادة إرسال ثالث)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الثالث {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال رابع بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال رابع
        if 'resending_fourth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال رابع بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال رابع...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال رابع
                        resending_fourth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الرابع
                            # أرقام إعادة الإرسال الرابع: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الرابع
                                        resending_fourth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال رابع، اعتبره عمود رقم إعادة إرسال رابع
                        if resending_fourth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_fourth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال رابع تلقائياً: {col} - فهرس: {idx} ({resending_fourth_count}/{len(sample_data)} أرقام إعادة إرسال رابع)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الرابع {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال خامس بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال خامس
        if 'resending_fifth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال خامس بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال خامس...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال خامس
                        resending_fifth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الخامس
                            # أرقام إعادة الإرسال الخامس: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الخامس
                                        resending_fifth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال خامس، اعتبره عمود رقم إعادة إرسال خامس
                        if resending_fifth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_fifth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال خامس تلقائياً: {col} - فهرس: {idx} ({resending_fifth_count}/{len(sample_data)} أرقام إعادة إرسال خامس)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الخامس {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال سادس بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال سادس
        if 'resending_sixth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال سادس بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال سادس...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال سادس
                        resending_sixth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال السادس
                            # أرقام إعادة الإرسال السادس: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال السادس
                                        resending_sixth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال سادس، اعتبره عمود رقم إعادة إرسال سادس
                        if resending_sixth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_sixth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال سادس تلقائياً: {col} - فهرس: {idx} ({resending_sixth_count}/{len(sample_data)} أرقام إعادة إرسال سادس)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال السادس {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال سابع بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال سابع
        if 'resending_seventh' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال سابع بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال سابع...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال سابع
                        resending_seventh_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال السابع
                            # أرقام إعادة الإرسال السابع: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال السابع
                                        resending_seventh_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال سابع، اعتبره عمود رقم إعادة إرسال سابع
                        if resending_seventh_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_seventh'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال سابع تلقائياً: {col} - فهرس: {idx} ({resending_seventh_count}/{len(sample_data)} أرقام إعادة إرسال سابع)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال السابع {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال ثامن بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثامن
        if 'resending_eighth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال ثامن بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثامن...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال ثامن
                        resending_eighth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الثامن
                            # أرقام إعادة الإرسال الثامن: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الثامن
                                        resending_eighth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال ثامن، اعتبره عمود رقم إعادة إرسال ثامن
                        if resending_eighth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_eighth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال ثامن تلقائياً: {col} - فهرس: {idx} ({resending_eighth_count}/{len(sample_data)} أرقام إعادة إرسال ثامن)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الثامن {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال تاسع بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال تاسع
        if 'resending_ninth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال تاسع بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال تاسع...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال تاسع
                        resending_ninth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال التاسع
                            # أرقام إعادة الإرسال التاسع: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال التاسع
                                        resending_ninth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال تاسع، اعتبره عمود رقم إعادة إرسال تاسع
                        if resending_ninth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_ninth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال تاسع تلقائياً: {col} - فهرس: {idx} ({resending_ninth_count}/{len(sample_data)} أرقام إعادة إرسال تاسع)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال التاسع {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال عاشر بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال عاشر
        if 'resending_tenth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال عاشر بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال عاشر...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال عاشر
                        resending_tenth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال العاشر
                            # أرقام إعادة الإرسال العاشر: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال العاشر
                                        resending_tenth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال عاشر، اعتبره عمود رقم إعادة إرسال عاشر
                        if resending_tenth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_tenth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال عاشر تلقائياً: {col} - فهرس: {idx} ({resending_tenth_count}/{len(sample_data)} أرقام إعادة إرسال عاشر)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال العاشر {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال حادي عشر بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال حادي عشر
        if 'resending_eleventh' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال حادي عشر بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال حادي عشر...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال حادي عشر
                        resending_eleventh_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الحادي عشر
                            # أرقام إعادة الإرسال الحادي عشر: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الحادي عشر
                                        resending_eleventh_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال حادي عشر، اعتبره عمود رقم إعادة إرسال حادي عشر
                        if resending_eleventh_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_eleventh'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال حادي عشر تلقائياً: {col} - فهرس: {idx} ({resending_eleventh_count}/{len(sample_data)} أرقام إعادة إرسال حادي عشر)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الحادي عشر {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال ثاني عشر بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثاني عشر
        if 'resending_twelfth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال ثاني عشر بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثاني عشر...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال ثاني عشر
                        resending_twelfth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الثاني عشر
                            # أرقام إعادة الإرسال الثاني عشر: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الثاني عشر
                                        resending_twelfth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال ثاني عشر، اعتبره عمود رقم إعادة إرسال ثاني عشر
                        if resending_twelfth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_twelfth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال ثاني عشر تلقائياً: {col} - فهرس: {idx} ({resending_twelfth_count}/{len(sample_data)} أرقام إعادة إرسال ثاني عشر)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الثاني عشر {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال ثالث عشر بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثالث عشر
        if 'resending_thirteenth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال ثالث عشر بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثالث عشر...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال ثالث عشر
                        resending_thirteenth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الثالث عشر
                            # أرقام إعادة الإرسال الثالث عشر: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الثالث عشر
                                        resending_thirteenth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال ثالث عشر، اعتبره عمود رقم إعادة إرسال ثالث عشر
                        if resending_thirteenth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_thirteenth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال ثالث عشر تلقائياً: {col} - فهرس: {idx} ({resending_thirteenth_count}/{len(sample_data)} أرقام إعادة إرسال ثالث عشر)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الثالث عشر {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال رابع عشر بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال رابع عشر
        if 'resending_fourteenth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال رابع عشر بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال رابع عشر...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال رابع عشر
                        resending_fourteenth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الرابع عشر
                            # أرقام إعادة الإرسال الرابع عشر: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الرابع عشر
                                        resending_fourteenth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال رابع عشر، اعتبره عمود رقم إعادة إرسال رابع عشر
                        if resending_fourteenth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_fourteenth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال رابع عشر تلقائياً: {col} - فهرس: {idx} ({resending_fourteenth_count}/{len(sample_data)} أرقام إعادة إرسال رابع عشر)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الرابع عشر {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال خامس عشر بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال خامس عشر
        if 'resending_fifteenth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال خامس عشر بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال خامس عشر...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال خامس عشر
                        resending_fifteenth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الخامس عشر
                            # أرقام إعادة الإرسال الخامس عشر: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الخامس عشر
                                        resending_fifteenth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال خامس عشر، اعتبره عمود رقم إعادة إرسال خامس عشر
                        if resending_fifteenth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_fifteenth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال خامس عشر تلقائياً: {col} - فهرس: {idx} ({resending_fifteenth_count}/{len(sample_data)} أرقام إعادة إرسال خامس عشر)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الخامس عشر {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال سادس عشر بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال سادس عشر
        if 'resending_sixteenth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال سادس عشر بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال سادس عشر...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال سادس عشر
                        resending_sixteenth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال السادس عشر
                            # أرقام إعادة الإرسال السادس عشر: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال السادس عشر
                                        resending_sixteenth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال سادس عشر، اعتبره عمود رقم إعادة إرسال سادس عشر
                        if resending_sixteenth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_sixteenth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال سادس عشر تلقائياً: {col} - فهرس: {idx} ({resending_sixteenth_count}/{len(sample_data)} أرقام إعادة إرسال سادس عشر)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال السادس عشر {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال سابع عشر بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال سابع عشر
        if 'resending_seventeenth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال سابع عشر بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال سابع عشر...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال سابع عشر
                        resending_seventeenth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال السابع عشر
                            # أرقام إعادة الإرسال السابع عشر: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال السابع عشر
                                        resending_seventeenth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال سابع عشر، اعتبره عمود رقم إعادة إرسال سابع عشر
                        if resending_seventeenth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_seventeenth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال سابع عشر تلقائياً: {col} - فهرس: {idx} ({resending_seventeenth_count}/{len(sample_data)} أرقام إعادة إرسال سابع عشر)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال السابع عشر {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال ثامن عشر بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثامن عشر
        if 'resending_eighteenth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال ثامن عشر بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثامن عشر...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال ثامن عشر
                        resending_eighteenth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الثامن عشر
                            # أرقام إعادة الإرسال الثامن عشر: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الثامن عشر
                                        resending_eighteenth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال ثامن عشر، اعتبره عمود رقم إعادة إرسال ثامن عشر
                        if resending_eighteenth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_eighteenth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال ثامن عشر تلقائياً: {col} - فهرس: {idx} ({resending_eighteenth_count}/{len(sample_data)} أرقام إعادة إرسال ثامن عشر)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الثامن عشر {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال تاسع عشر بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال تاسع عشر
        if 'resending_nineteenth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال تاسع عشر بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال تاسع عشر...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال تاسع عشر
                        resending_nineteenth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال التاسع عشر
                            # أرقام إعادة الإرسال التاسع عشر: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال التاسع عشر
                                        resending_nineteenth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال تاسع عشر، اعتبره عمود رقم إعادة إرسال تاسع عشر
                        if resending_nineteenth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_nineteenth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال تاسع عشر تلقائياً: {col} - فهرس: {idx} ({resending_nineteenth_count}/{len(sample_data)} أرقام إعادة إرسال تاسع عشر)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال التاسع عشر {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال عشرون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال عشرون
        if 'resending_twentieth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال عشرون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال عشرون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال عشرون
                        resending_twentieth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال العشرون
                            # أرقام إعادة الإرسال العشرون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال العشرون
                                        resending_twentieth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال عشرون، اعتبره عمود رقم إعادة إرسال عشرون
                        if resending_twentieth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_twentieth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال عشرون تلقائياً: {col} - فهرس: {idx} ({resending_twentieth_count}/{len(sample_data)} أرقام إعادة إرسال عشرون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال العشرون {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال حادي والعشرون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال حادي والعشرون
        if 'resending_twenty_first' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال حادي والعشرون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال حادي والعشرون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال حادي والعشرون
                        resending_twenty_first_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الحادي والعشرون
                            # أرقام إعادة الإرسال الحادي والعشرون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الحادي والعشرون
                                        resending_twenty_first_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال حادي والعشرون، اعتبره عمود رقم إعادة إرسال حادي والعشرون
                        if resending_twenty_first_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_twenty_first'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال حادي والعشرون تلقائياً: {col} - فهرس: {idx} ({resending_twenty_first_count}/{len(sample_data)} أرقام إعادة إرسال حادي والعشرون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الحادي والعشرون {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال ثاني والعشرون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثاني والعشرون
        if 'resending_twenty_second' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال ثاني والعشرون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثاني والعشرون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال ثاني والعشرون
                        resending_twenty_second_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الثاني والعشرون
                            # أرقام إعادة الإرسال الثاني والعشرون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الثاني والعشرون
                                        resending_twenty_second_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال ثاني والعشرون، اعتبره عمود رقم إعادة إرسال ثاني والعشرون
                        if resending_twenty_second_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_twenty_second'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال ثاني والعشرون تلقائياً: {col} - فهرس: {idx} ({resending_twenty_second_count}/{len(sample_data)} أرقام إعادة إرسال ثاني والعشرون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الثاني والعشرون {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال ثالث والعشرون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثالث والعشرون
        if 'resending_twenty_third' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال ثالث والعشرون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثالث والعشرون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال ثالث والعشرون
                        resending_twenty_third_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الثالث والعشرون
                            # أرقام إعادة الإرسال الثالث والعشرون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الثالث والعشرون
                                        resending_twenty_third_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال ثالث والعشرون، اعتبره عمود رقم إعادة إرسال ثالث والعشرون
                        if resending_twenty_third_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_twenty_third'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال ثالث والعشرون تلقائياً: {col} - فهرس: {idx} ({resending_twenty_third_count}/{len(sample_data)} أرقام إعادة إرسال ثالث والعشرون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الثالث والعشرون {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال رابع والعشرون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال رابع والعشرون
        if 'resending_twenty_fourth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال رابع والعشرون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال رابع والعشرون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال رابع والعشرون
                        resending_twenty_fourth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الرابع والعشرون
                            # أرقام إعادة الإرسال الرابع والعشرون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الرابع والعشرون
                                        resending_twenty_fourth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال رابع والعشرون، اعتبره عمود رقم إعادة إرسال رابع والعشرون
                        if resending_twenty_fourth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_twenty_fourth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال رابع والعشرون تلقائياً: {col} - فهرس: {idx} ({resending_twenty_fourth_count}/{len(sample_data)} أرقام إعادة إرسال رابع والعشرون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الرابع والعشرون {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال خامس والعشرون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال خامس والعشرون
        if 'resending_twenty_fifth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال خامس والعشرون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال خامس والعشرون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال خامس والعشرون
                        resending_twenty_fifth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الخامس والعشرون
                            # أرقام إعادة الإرسال الخامس والعشرون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الخامس والعشرون
                                        resending_twenty_fifth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال خامس والعشرون، اعتبره عمود رقم إعادة إرسال خامس والعشرون
                        if resending_twenty_fifth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_twenty_fifth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال خامس والعشرون تلقائياً: {col} - فهرس: {idx} ({resending_twenty_fifth_count}/{len(sample_data)} أرقام إعادة إرسال خامس والعشرون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الخامس والعشرون {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال سادس والعشرون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال سادس والعشرون
        if 'resending_twenty_sixth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال سادس والعشرون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال سادس والعشرون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال سادس والعشرون
                        resending_twenty_sixth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال السادس والعشرون
                            # أرقام إعادة الإرسال السادس والعشرون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال السادس والعشرون
                                        resending_twenty_sixth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال سادس والعشرون، اعتبره عمود رقم إعادة إرسال سادس والعشرون
                        if resending_twenty_sixth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_twenty_sixth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال سادس والعشرون تلقائياً: {col} - فهرس: {idx} ({resending_twenty_sixth_count}/{len(sample_data)} أرقام إعادة إرسال سادس والعشرون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال السادس والعشرون {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال سابع والعشرون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال سابع والعشرون
        if 'resending_twenty_seventh' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال سابع والعشرون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال سابع والعشرون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال سابع والعشرون
                        resending_twenty_seventh_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال السابع والعشرون
                            # أرقام إعادة الإرسال السابع والعشرون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال السابع والعشرون
                                        resending_twenty_seventh_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال سابع والعشرون، اعتبره عمود رقم إعادة إرسال سابع والعشرون
                        if resending_twenty_seventh_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_twenty_seventh'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال سابع والعشرون تلقائياً: {col} - فهرس: {idx} ({resending_twenty_seventh_count}/{len(sample_data)} أرقام إعادة إرسال سابع والعشرون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال السابع والعشرون {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال ثامن والعشرون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثامن والعشرون
        if 'resending_twenty_eighth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال ثامن والعشرون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثامن والعشرون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال ثامن والعشرون
                        resending_twenty_eighth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الثامن والعشرون
                            # أرقام إعادة الإرسال الثامن والعشرون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الثامن والعشرون
                                        resending_twenty_eighth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال ثامن والعشرون، اعتبره عمود رقم إعادة إرسال ثامن والعشرون
                        if resending_twenty_eighth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_twenty_eighth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال ثامن والعشرون تلقائياً: {col} - فهرس: {idx} ({resending_twenty_eighth_count}/{len(sample_data)} أرقام إعادة إرسال ثامن والعشرون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الثامن والعشرون {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال تاسع والعشرون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال تاسع والعشرون
        if 'resending_twenty_ninth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال تاسع والعشرون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال تاسع والعشرون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال تاسع والعشرون
                        resending_twenty_ninth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال التاسع والعشرون
                            # أرقام إعادة الإرسال التاسع والعشرون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال التاسع والعشرون
                                        resending_twenty_ninth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال تاسع والعشرون، اعتبره عمود رقم إعادة إرسال تاسع والعشرون
                        if resending_twenty_ninth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_twenty_ninth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال تاسع والعشرون تلقائياً: {col} - فهرس: {idx} ({resending_twenty_ninth_count}/{len(sample_data)} أرقام إعادة إرسال تاسع والعشرون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال التاسع والعشرون {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال ثلاثون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثلاثون
        if 'resending_thirtieth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال ثلاثون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثلاثون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال ثلاثون
                        resending_thirtieth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الثلاثون
                            # أرقام إعادة الإرسال الثلاثون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الثلاثون
                                        resending_thirtieth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال ثلاثون، اعتبره عمود رقم إعادة إرسال ثلاثون
                        if resending_thirtieth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_thirtieth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال ثلاثون تلقائياً: {col} - فهرس: {idx} ({resending_thirtieth_count}/{len(sample_data)} أرقام إعادة إرسال ثلاثون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الثلاثون {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال حادي والثلاثون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال حادي والثلاثون
        if 'resending_thirty_first' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال حادي والثلاثون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال حادي والثلاثون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال حادي والثلاثون
                        resending_thirty_first_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الحادي والثلاثون
                            # أرقام إعادة الإرسال الحادي والثلاثون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الحادي والثلاثون
                                        resending_thirty_first_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال حادي والثلاثون، اعتبره عمود رقم إعادة إرسال حادي والثلاثون
                        if resending_thirty_first_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_thirty_first'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال حادي والثلاثون تلقائياً: {col} - فهرس: {idx} ({resending_thirty_first_count}/{len(sample_data)} أرقام إعادة إرسال حادي والثلاثون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الحادي والثلاثون {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال ثاني والثلاثون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثاني والثلاثون
        if 'resending_thirty_second' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال ثاني والثلاثون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثاني والثلاثون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال ثاني والثلاثون
                        resending_thirty_second_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الثاني والثلاثون
                            # أرقام إعادة الإرسال الثاني والثلاثون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الثاني والثلاثون
                                        resending_thirty_second_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال ثاني والعشرون، اعتبره عمود رقم إعادة إرسال ثاني والثلاثون
                        if resending_thirty_second_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_thirty_second'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال ثاني والثلاثون تلقائياً: {col} - فهرس: {idx} ({resending_thirty_second_count}/{len(sample_data)} أرقام إعادة إرسال ثاني والثلاثون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الثاني والثلاثون {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال ثالث والثلاثون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثالث والثلاثون
        if 'resending_thirty_third' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال ثالث والثلاثون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثالث والثلاثون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال ثالث والثلاثون
                        resending_thirty_third_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الثالث والثلاثون
                            # أرقام إعادة الإرسال الثالث والثلاثون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الثالث والثلاثون
                                        resending_thirty_third_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال ثالث والثلاثون، اعتبره عمود رقم إعادة إرسال ثالث والثلاثون
                        if resending_thirty_third_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_thirty_third'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال ثالث والثلاثون تلقائياً: {col} - فهرس: {idx} ({resending_thirty_third_count}/{len(sample_data)} أرقام إعادة إرسال ثالث والثلاثون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الثالث والثلاثون {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال رابع والثلاثون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال رابع والثلاثون
        if 'resending_thirty_fourth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال رابع والثلاثون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال رابع والثلاثون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال رابع والثلاثون
                        resending_thirty_fourth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الرابع والثلاثون
                            # أرقام إعادة الإرسال الرابع والثلاثون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الرابع والثلاثون
                                        resending_thirty_fourth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال رابع والثلاثون، اعتبره عمود رقم إعادة إرسال رابع والثلاثون
                        if resending_thirty_fourth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_thirty_fourth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال رابع والثلاثون تلقائياً: {col} - فهرس: {idx} ({resending_thirty_fourth_count}/{len(sample_data)} أرقام إعادة إرسال رابع والثلاثون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الرابع والثلاثون {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال خامس والثلاثون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال خامس والثلاثون
        if 'resending_thirty_fifth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال خامس والثلاثون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال خامس والثلاثون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال خامس والثلاثون
                        resending_thirty_fifth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الخامس والثلاثون
                            # أرقام إعادة الإرسال الخامس والثلاثون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الخامس والثلاثون
                                        resending_thirty_fifth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال خامس والثلاثون، اعتبره عمود رقم إعادة إرسال خامس والثلاثون
                        if resending_thirty_fifth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_thirty_fifth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال خامس والثلاثون تلقائياً: {col} - فهرس: {idx} ({resending_thirty_fifth_count}/{len(sample_data)} أرقام إعادة إرسال خامس والثلاثون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الخامس والثلاثون {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال سادس والثلاثون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال سادس والثلاثون
        if 'resending_thirty_sixth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال سادس والثلاثون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال سادس والثلاثون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال سادس والثلاثون
                        resending_thirty_sixth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال السادس والثلاثون
                            # أرقام إعادة الإرسال السادس والثلاثون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال السادس والثلاثون
                                        resending_thirty_sixth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال سادس والثلاثون، اعتبره عمود رقم إعادة إرسال سادس والثلاثون
                        if resending_thirty_sixth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_thirty_sixth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال سادس والثلاثون تلقائياً: {col} - فهرس: {idx} ({resending_thirty_sixth_count}/{len(sample_data)} أرقام إعادة إرسال سادس والثلاثون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال السادس والثلاثون {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال سابع والثلاثون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال سابع والثلاثون
        if 'resending_thirty_seventh' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال سابع والثلاثون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال سابع والثلاثون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال سابع والثلاثون
                        resending_thirty_seventh_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال السابع والثلاثون
                            # أرقام إعادة الإرسال السابع والثلاثون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال السابع والثلاثون
                                        resending_thirty_seventh_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال سابع والثلاثون، اعتبره عمود رقم إعادة إرسال سابع والثلاثون
                        if resending_thirty_seventh_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_thirty_seventh'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال سابع والثلاثون تلقائياً: {col} - فهرس: {idx} ({resending_thirty_seventh_count}/{len(sample_data)} أرقام إعادة إرسال سابع والثلاثون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال السابع والثلاثون {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال ثامن والثلاثون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثامن والثلاثون
        if 'resending_thirty_eighth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال ثامن والثلاثون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثامن والثلاثون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال ثامن والثلاثون
                        resending_thirty_eighth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الثامن والثلاثون
                            # أرقام إعادة الإرسال الثامن والثلاثون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الثامن والثلاثون
                                        resending_thirty_eighth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال ثامن والثلاثون، اعتبره عمود رقم إعادة إرسال ثامن والثلاثون
                        if resending_thirty_eighth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_thirty_eighth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال ثامن والثلاثون تلقائياً: {col} - فهرس: {idx} ({resending_thirty_eighth_count}/{len(sample_data)} أرقام إعادة إرسال ثامن والثلاثون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الثامن والثلاثون {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال تاسع والثلاثون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال تاسع والثلاثون
        if 'resending_thirty_ninth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال تاسع والثلاثون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال تاسع والثلاثون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال تاسع والثلاثون
                        resending_thirty_ninth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال التاسع والثلاثون
                            # أرقام إعادة الإرسال التاسع والثلاثون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال التاسع والثلاثون
                                        resending_thirty_ninth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال تاسع والثلاثون، اعتبره عمود رقم إعادة إرسال تاسع والثلاثون
                        if resending_thirty_ninth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_thirty_ninth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال تاسع والثلاثون تلقائياً: {col} - فهرس: {idx} ({resending_thirty_ninth_count}/{len(sample_data)} أرقام إعادة إرسال تاسع والثلاثون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال التاسع والثلاثون {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال أربعون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال أربعون
        if 'resending_fortieth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال أربعون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال أربعون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال أربعون
                        resending_fortieth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الأربعون
                            # أرقام إعادة الإرسال الأربعون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الأربعون
                                        resending_fortieth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال أربعون، اعتبره عمود رقم إعادة إرسال أربعون
                        if resending_fortieth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_fortieth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال أربعون تلقائياً: {col} - فهرس: {idx} ({resending_fortieth_count}/{len(sample_data)} أرقام إعادة إرسال أربعون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الأربعون {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال حادي والأربعون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال حادي والأربعون
        if 'resending_forty_first' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال حادي والأربعون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال حادي والأربعون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال حادي والأربعون
                        resending_forty_first_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الحادي والأربعون
                            # أرقام إعادة الإرسال الحادي والأربعون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الحادي والأربعون
                                        resending_forty_first_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال حادي والأربعون، اعتبره عمود رقم إعادة إرسال حادي والأربعون
                        if resending_forty_first_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_forty_first'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال حادي والأربعون تلقائياً: {col} - فهرس: {idx} ({resending_forty_first_count}/{len(sample_data)} أرقام إعادة إرسال حادي والأربعون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الحادي والأربعون {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال ثاني والأربعون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثاني والأربعون
        if 'resending_forty_second' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال ثاني والأربعون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثاني والأربعون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال ثاني والأربعون
                        resending_forty_second_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الثاني والأربعون
                            # أرقام إعادة الإرسال الثاني والأربعون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الثاني والأربعون
                                        resending_forty_second_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال ثاني والأربعون، اعتبره عمود رقم إعادة إرسال ثاني والأربعون
                        if resending_forty_second_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_forty_second'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال ثاني والأربعون تلقائياً: {col} - فهرس: {idx} ({resending_forty_second_count}/{len(sample_data)} أرقام إعادة إرسال ثاني والأربعون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الثاني والأربعون {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال ثالث والأربعون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثالث والأربعون
        if 'resending_forty_third' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال ثالث والأربعون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال ثالث والأربعون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال ثالث والأربعون
                        resending_forty_third_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الثالث والأربعون
                            # أرقام إعادة الإرسال الثالث والأربعون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الثالث والأربعون
                                        resending_forty_third_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال ثالث والأربعون، اعتبره عمود رقم إعادة إرسال ثالث والأربعون
                        if resending_forty_third_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_forty_third'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال ثالث والأربعون تلقائياً: {col} - فهرس: {idx} ({resending_forty_third_count}/{len(sample_data)} أرقام إعادة إرسال ثالث والأربعون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الثالث والأربعون {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال رابع والأربعون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال رابع والأربعون
        if 'resending_forty_fourth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال رابع والأربعون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال رابع والأربعون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال رابع والأربعون
                        resending_forty_fourth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الرابع والأربعون
                            # أرقام إعادة الإرسال الرابع والأربعون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الرابع والأربعون
                                        resending_forty_fourth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال رابع والأربعون، اعتبره عمود رقم إعادة إرسال رابع والأربعون
                        if resending_forty_fourth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_forty_fourth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال رابع والأربعون تلقائياً: {col} - فهرس: {idx} ({resending_forty_fourth_count}/{len(sample_data)} أرقام إعادة إرسال رابع والأربعون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الرابع والأربعون {col}: {e}")
                    continue

        # إذا لم نجد عمود رقم إعادة إرسال خامس والأربعون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال خامس والأربعون
        if 'resending_forty_fifth' not in person_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد عمود رقم إعادة إرسال خامس والأربعون بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام إعادة إرسال خامس والأربعون...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام التي تبدو كأرقام إعادة إرسال خامس والأربعون
                        resending_forty_fifth_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # فحص أنماط أرقام إعادة الإرسال الخامس والأربعون
                            # أرقام إعادة الإرسال الخامس والأربعون: عادة تحتوي على أرقام وحروف ورموز
                            if val_str and not val_str.isdigit():
                                # فحص إذا كان يحتوي على أرقام وأحرف أو رموز
                                has_digits = any(c.isdigit() for c in val_str)
                                has_letters = any(c.isalpha() for c in val_str)
                                has_special = any(c in '-_./' for c in val_str)

                                if has_digits and (has_letters or has_special):
                                    val_len = len(val_str)
                                    if 6 <= val_len <= 20:  # طول مناسب لأرقام إعادة الإرسال الخامس والأربعون
                                        resending_forty_fifth_count += 1

                        # إذا كان معظم القيم (50%+) تبدو كأرقام إعادة إرسال خامس والأربعون، اعتبره عمود رقم إعادة إرسال خامس والأربعون
                        if resending_forty_fifth_count >= len(sample_data) * 0.5 and len(sample_data) >= 3:
                            person_columns['resending_forty_fifth'] = col
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقم إعادة إرسال خامس والأربعون تلقائياً: {col} - فهرس: {idx} ({resending_forty_fifth_count}/{len(sample_data)} أرقام إعادة إرسال خامس والأربعون)")
                            break
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص عمود رقم إعادة الإرسال الخامس والأربعون {col}: {e}")
                    continue

        # إذا لم نجد أعمدة رقمية بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام
        if not number_columns:
            logger.info("[PERSON_DUPLICATES] لم نجد أعمدة رقمية بالطرق التقليدية، نبحث عن أعمدة تحتوي على أرقام...")
            for idx, col in enumerate(df.columns):
                try:
                    # فحص عينة من البيانات في العمود
                    sample_data = df[col].dropna().head(10)
                    if len(sample_data) > 0:
                        # عد الأرقام في العينة
                        numeric_count = 0
                        for val in sample_data:
                            val_str = str(val).strip()
                            # تحقق إذا كانت القيمة تحتوي على أرقام فقط (أو أرقام مع شرطات/فواصل)
                            if val_str and (val_str.isdigit() or all(c.isdigit() or c in '-_.' for c in val_str)):
                                numeric_count += 1

                        # إذا كان معظم القيم (70%+) أرقام، اعتبره عمود رقمي
                        if numeric_count >= len(sample_data) * 0.7 and len(sample_data) >= 3:
                            # حدد نوع العمود بناءً على الفهرس أو الاسم
                            col_str = str(col).lower()
                            if idx <= 2:  # الأعمدة الأولى غالباً ما تكون يدوية
                                inferred_type = 'manual'
                            elif idx >= 3:  # الأعمدة المتأخرة غالباً ما تكون تأمينية
                                inferred_type = 'insurance'
                            else:
                                inferred_type = 'manual'  # افتراضي

                            number_columns.append({
                                'column': col,
                                'type': inferred_type,
                                'type_arabic': {
                                    'insurance': 'تأميني',
                                    'manual': 'يدوي'
                                }.get(inferred_type, 'غير محدد'),
                                'column_index': idx,
                                'column_letter': chr(ord('A') + idx) if idx < 26 else f"A{chr(ord('A') + idx - 26)}",
                                'detection_method': f'كشف تلقائي ({numeric_count}/{len(sample_data)} قيم رقمية)'
                            })
                            logger.info(f"[PERSON_DUPLICATES] تم العثور على عمود رقمي تلقائياً: {col} - نوع: {inferred_type} - فهرس: {idx}")
                except Exception as e:
                    logger.debug(f"[PERSON_DUPLICATES] خطأ في فحص العمود {col}: {e}")
                    continue

        # التحقق من وجود الأعمدة المطلوبة
        required_person_cols = ['name']
        missing_cols = [col for col in required_person_cols if col not in person_columns]

        # تلخيص ما تم العثور عليه
        logger.info(f"[PERSON_DUPLICATES] ملخص كشف الأعمدة:")
        logger.info(f"[PERSON_DUPLICATES] - أعمدة الأشخاص: {list(person_columns.keys())}")
        logger.info(f"[PERSON_DUPLICATES] - أعمدة الأرقام: {[f'{col['type']}({col['column']})' for col in number_columns]}")

        # إذا لم يتم العثور على أعمدة، إرجاع معلومات مفيدة للتصحيح
        if missing_cols or not number_columns:
            logger.warning(f"[PERSON_DUPLICATES] الأعمدة المطلوبة مفقودة: {missing_cols}")
            logger.warning(f"[PERSON_DUPLICATES] لم يتم العثور على أعمدة أرقام: {len(number_columns)} عمود")
            return {
                'merge_candidates': [],
                'total_candidates': 0,
                'number_types_found': [],
                'debug_info': {
                    'person_columns_found': person_columns,
                    'number_columns_found': number_columns,
                    'all_columns': list(df.columns),
                    'missing_person_cols': missing_cols,
                    'sample_data': df.head(3).to_dict() if len(df) > 0 else {}
                }
            }

        # أخذ عينة من البيانات
        sample_df = df.head(sample_size)
        logger.info(f"[PERSON_DUPLICATES] تحليل عينة من {len(sample_df)} صف")
        columns_info = [f'{i}:{col}({chr(ord("A")+i)})' for i, col in enumerate(df.columns)]
        logger.info(f"[PERSON_DUPLICATES] أعمدة البيانات: {columns_info}")

        # إنشاء مفتاح الشخص
        person_keys = []
        for idx, row in sample_df.iterrows():
            try:
                # استخدام الاسم كمفتاح أساسي
                name = str(row[person_columns['name']]).strip().lower() if 'name' in person_columns else ''

                # إضافة معلومات إضافية إذا كانت متوفرة
                key_parts = [name]

                for key in ['birth_date', 'id', 'mother', 'city']:
                    if key in person_columns and pd.notna(row[person_columns[key]]):
                        key_parts.append(str(row[person_columns[key]]).strip().lower())

                person_key = '|'.join(key_parts)
                person_keys.append((idx, person_key))
            except Exception as e:
                logger.debug(f"خطأ في إنشاء مفتاح الشخص للصف {idx}: {e}")
                continue

        # تجميع الأشخاص والأرقام الخاصة بهم مع تصنيف الأنواع
        person_numbers = {}
        for idx, person_key in person_keys:
            if person_key not in person_numbers:
                person_numbers[person_key] = {
                    'rows': [],
                    'numbers': set(),
                    'number_types': set(),
                    'numbers_by_type': {},
                    'insurance_numbers': set(),  # أرقام تأمينية فقط
                    'manual_numbers': set()      # أرقام يدوية فقط
                }

            # جمع الأرقام من جميع أعمدة الأرقام مع تصنيفها
            for num_info in number_columns:
                col = num_info['column']
                num_type = num_info['type']
                num_type_arabic = num_info['type_arabic']

                if pd.notna(sample_df.loc[idx, col]):
                    number = str(sample_df.loc[idx, col]).strip()
                    if number:
                        # التحقق من أن الرقم يحتوي على أرقام فقط (لا نصوص)
                        if num_type == 'manual' and not number.isdigit():
                            # استبعاد الأرقام اليدوية التي تحتوي على نصوص
                            logger.debug(f"[PERSON_DUPLICATES] استبعاد رقم يدوي يحتوي على نص: '{number}' في الصف {idx}")
                            continue
                        elif num_type == 'insurance' and not number.isdigit():
                            # استبعاد الأرقام التأمينية التي تحتوي على نصوص
                            logger.debug(f"[PERSON_DUPLICATES] استبعاد رقم تأميني يحتوي على نص: '{number}' في الصف {idx}")
                            continue

                        person_numbers[person_key]['numbers'].add(number)
                        person_numbers[person_key]['number_types'].add(num_type_arabic)
                        person_numbers[person_key]['rows'].append(idx)

                        # تصنيف الأرقام حسب النوع
                        if num_type_arabic not in person_numbers[person_key]['numbers_by_type']:
                            person_numbers[person_key]['numbers_by_type'][num_type_arabic] = set()
                        person_numbers[person_key]['numbers_by_type'][num_type_arabic].add(number)

                        # فصل الأرقام حسب النوع للبحث عن التكرارات المنفصلة
                        if num_type == 'insurance':
                            person_numbers[person_key]['insurance_numbers'].add(number)
                        elif num_type == 'manual':
                            person_numbers[person_key]['manual_numbers'].add(number)

        # العثور على الأشخاص الذين لديهم أرقام متعددة من نفس النوع
        merge_candidates = []
        for person_key, data in person_numbers.items():
            # فحص التكرارات في الأرقام التأمينية
            insurance_duplicates = len(data['insurance_numbers']) > 1
            # فحص التكرارات في الأرقام اليدوية
            manual_duplicates = len(data['manual_numbers']) > 1

            # إذا كان هناك تكرار في أي من النوعين
            if insurance_duplicates or manual_duplicates:
                # تحويل الأرقام حسب النوع إلى قوائم
                numbers_by_type_formatted = {}
                for num_type, numbers_set in data['numbers_by_type'].items():
                    numbers_by_type_formatted[num_type] = list(numbers_set)

                # تحديد نوع التكرار
                duplicate_types = []
                if insurance_duplicates:
                    duplicate_types.append('تأميني')
                if manual_duplicates:
                    duplicate_types.append('يدوي')

                merge_candidates.append({
                    'person_key': person_key,
                    'numbers': list(data['numbers']),
                    'numbers_count': len(data['numbers']),
                    'rows_count': len(set(data['rows'])),  # عدد الصفوف المختلفة
                    'sample_rows': data['rows'][:5],  # أول 5 صفوف كعينة
                    'number_types': list(data['number_types']),  # أنواع الأرقام الموجودة
                    'numbers_by_type': numbers_by_type_formatted,  # الأرقام مصنفة حسب النوع
                    'duplicate_types': duplicate_types,  # أنواع التكرارات المكتشفة
                    'insurance_count': len(data['insurance_numbers']),  # عدد الأرقام التأمينية
                    'manual_count': len(data['manual_numbers']),  # عدد الأرقام اليدوية
                    'has_insurance_duplicates': insurance_duplicates,  # هل يوجد تكرار تأميني
                    'has_manual_duplicates': manual_duplicates,  # هل يوجد تكرار يدوي
                    'contains_row_1': 0 in data['rows']  # هل يحتوي على الصف رقم 1 (فهرس 0)
                })

        logger.info(f"[PERSON_DUPLICATES] تم العثور على {len(merge_candidates)} مرشح للدمج")

        # تسجيل جميع الأرقام لكل مرشح للدمج
        logger.info(f"[PERSON_DUPLICATES] تسجيل جميع الأرقام للمرشحين للدمج:")
        for i, candidate in enumerate(merge_candidates[:50], 1):  # أول 50 مرشح فقط لتجنب السجلات الطويلة جداً
            logger.info(f"[PERSON_DUPLICATES] مرشح {i}: {candidate['person_key']}")
            logger.info(f"[PERSON_DUPLICATES]   - الأرقام الأساسية (تأميني/آلي): {candidate['numbers_by_type'].get('تأميني', [])} + {candidate['numbers_by_type'].get('آلي', [])} = {candidate['insurance_count']} رقم أساسي")
            logger.info(f"[PERSON_DUPLICATES]   - الأرقام اليدوية المرتبطة: {candidate['numbers_by_type'].get('يدوي', [])} = {candidate['manual_count']} رقم يدوي")
            logger.info(f"[PERSON_DUPLICATES]   - جميع الأرقام: {candidate['numbers']}")
            logger.info(f"[PERSON_DUPLICATES]   - ملاحظة: {candidate.get('duplicate_types', [])}")

        # إحصائيات مفصلة عن التكرارات
        insurance_duplicates_count = sum(1 for c in merge_candidates if c['has_insurance_duplicates'])
        manual_duplicates_count = sum(1 for c in merge_candidates if c['has_manual_duplicates'])
        both_types_duplicates_count = sum(1 for c in merge_candidates if c['has_insurance_duplicates'] and c['has_manual_duplicates'])

        logger.info(f"[PERSON_DUPLICATES] إحصائيات التكرارات:")
        logger.info(f"[PERSON_DUPLICATES] - تكرارات تأمينية فقط: {insurance_duplicates_count}")
        logger.info(f"[PERSON_DUPLICATES] - تكرارات يدوية فقط: {manual_duplicates_count}")
        logger.info(f"[PERSON_DUPLICATES] - تكرارات في كلا النوعين: {both_types_duplicates_count}")

        # ترتيب حسب عدد الأرقام (الأكثر أهمية أولاً)
        merge_candidates.sort(key=lambda x: x['numbers_count'], reverse=True)

        # استخراج أنواع الأرقام الموجودة
        all_number_types = set()
        for candidate in merge_candidates:
            all_number_types.update(candidate['number_types'])

        return {
            'merge_candidates': merge_candidates,  # عرض جميع المرشحين
            'total_candidates': len(merge_candidates),
            'analyzed_persons': len(person_numbers),
            'person_columns_found': list(person_columns.keys()),
            'number_columns_found': [col['column'] for col in number_columns],
            'number_types_found': list(all_number_types),  # أنواع الأرقام المكتشفة (تأميني/يدوي)
            'number_columns_info': number_columns,  # معلومات مفصلة عن أعمدة الأرقام
            'duplicates_summary': {
                'insurance_duplicates_count': insurance_duplicates_count,
                'manual_duplicates_count': manual_duplicates_count,
                'both_types_duplicates_count': both_types_duplicates_count,
                'total_duplicates': insurance_duplicates_count + manual_duplicates_count + both_types_duplicates_count
            }
        }

    except Exception as e:
        logger.error(f"خطأ في كشف الأشخاص ذوي الأرقام المتعددة: {e}")
        return {'merge_candidates': [], 'total_candidates': 0, 'number_types_found': []}

def read_excel_chunked(file_path, chunk_size=50000):
    """قراءة ملف Excel الكبير باستخدام القراءة المقسمة لتوفير الذاكرة"""
    try:
        logger.info(f"[CHUNKED] بدء القراءة المقسمة للملف: {file_path}")

        # قراءة الصف الأول فقط للحصول على أسماء الأعمدة
        df_header = pd.read_excel(file_path, nrows=0)
        columns = df_header.columns.tolist()
        logger.info(f"[CHUNKED] تم العثور على {len(columns)} عمود")

        # قراءة الملف على دفعات
        chunks = []
        total_rows = 0

        for chunk in pd.read_excel(file_path, chunksize=chunk_size, dtype=str):
            chunks.append(chunk)
            total_rows += len(chunk)
            logger.info(f"[CHUNKED] تم قراءة دفعة جديدة، المجموع: {total_rows} صف")

            # التحقق من حد الذاكرة (100,000 صف كحد أقصى)
            if total_rows > 100000:
                logger.warning(f"[CHUNKED] تم الوصول إلى الحد الأقصى للصفوف: {total_rows}")
                break

        # دمج الدفعات
        if chunks:
            df = pd.concat(chunks, ignore_index=True)
            logger.info(f"[CHUNKED] تم دمج {len(chunks)} دفعة، إجمالي الصفوف: {len(df)}")
            return df
        else:
            raise ValueError("لم يتم قراءة أي بيانات من الملف")

    except Exception as e:
        logger.error(f"[CHUNKED] خطأ في القراءة المقسمة: {e}")
        # محاولة القراءة العادية كبديل
        logger.info("[CHUNKED] محاولة القراءة العادية كبديل")
        return pd.read_excel(file_path, dtype=str)

def read_excel_with_arabic_support(file_path, use_chunks=False, chunk_size=50000):
    """قراءة ملف Excel مع دعم كامل للأحرف العربية ومعالجة ملفات محمية بكلمة سر"""
    file_ext = os.path.splitext(file_path)[1].lower()
    file_size = os.path.getsize(file_path) / (1024 * 1024)  # Size in MB

    # استخدام القراءة المقسمة للملفات الكبيرة
    if file_size > 50 or use_chunks:  # ملفات أكبر من 50 ميجابايت
        logger.info(f"[CHUNKED] قراءة ملف كبير ({file_size:.1f} MB) باستخدام القراءة المقسمة")
        return read_excel_chunked(file_path, chunk_size)

    try:
        if file_ext == '.xlsx':
            # محاولة 1: مع dtype=str للحفاظ على الأحرف العربية
            df = pd.read_excel(file_path, engine='openpyxl', dtype=str)
            logger.info("[SUCCESS] تم قراءة الملف بنجاح مع dtype=str")

            # فحص إذا كانت الأحرف مشوهة
            if any('' in str(col) for col in df.columns):
                logger.info("[FIX] كشف أحرف مشوهة، محاولة الإصلاح...")
                try:
                    df_fixed = pd.read_excel(file_path, engine='openpyxl')
                    if not any('' in str(col) for col in df_fixed.columns):
                        df = df_fixed
                        logger.info("[SUCCESS] تم إصلاح الأحرف المشوهة")
                except Exception as fix_e:
                    logger.warning(f"[WARNING] تعذر إصلاح الأحرف المشوهة: {fix_e}")

            return df

        elif file_ext == '.xls':
            # للملفات .xls، استخدم xlrd مع معالجة الترميز
            try:
                import xlrd

                # محاولة فتح الملف مع xlrd
                try:
                    workbook = xlrd.open_workbook(file_path, encoding_override='cp1256')
                    logger.info("[SUCCESS] تم قراءة ملف .xls بنجاح مع ترميز cp1256")
                except UnicodeDecodeError:
                    try:
                        workbook = xlrd.open_workbook(file_path, encoding_override='utf-8')
                        logger.info("[SUCCESS] تم قراءة ملف .xls بنجاح مع ترميز utf-8")
                    except UnicodeDecodeError:
                        try:
                            workbook = xlrd.open_workbook(file_path, encoding_override='latin1')
                            logger.info("[SUCCESS] تم قراءة ملف .xls بنجاح مع ترميز latin1")
                        except UnicodeDecodeError:
                            # محاولة بدون تحديد الترميز
                            workbook = xlrd.open_workbook(file_path)
                            logger.info("[SUCCESS] تم قراءة ملف .xls بنجاح بدون تحديد ترميز")

                # تحويل إلى DataFrame
                sheet = workbook.sheet_by_index(0)
                data = []
                for row_idx in range(sheet.nrows):
                    row_data = []
                    for col_idx in range(sheet.ncols):
                        cell_value = sheet.cell_value(row_idx, col_idx)
                        # تحويل القيم إلى سلاسل نصية لتجنب مشاكل الترميز
                        if cell_value is not None:
                            row_data.append(str(cell_value))
                        else:
                            row_data.append('')
                    data.append(row_data)

                if data:
                    df = pd.DataFrame(data[1:], columns=data[0])  # الصف الأول كعناوين
                    logger.info(f"[INFO] تم تحويل البيانات إلى DataFrame: {df.shape}")
                    return df
                else:
                    raise ValueError("الملف لا يحتوي على بيانات")

            except xlrd.XLRDError as e:
                if 'encrypted' in str(e).lower() or 'password' in str(e).lower():
                    error_msg = (
                        "الملف محمي بكلمة سر! يرجى إزالة الحماية قبل التحليل:\n"
                        "1. افتح الملف في Excel\n"
                        "2. اذهب إلى File > Info > Protect Workbook > Encrypt with Password\n"
                        "3. أزل كلمة السر واحفظ الملف\n"
                        "4. أعد رفع الملف إلى النظام"
                    )
                    logger.error(f"[PASSWORD] {error_msg}")
                    raise ValueError(error_msg)
                else:
                    raise
            except ImportError:
                logger.error("[ERROR] مكتبة xlrd غير مثبتة. قم بتثبيتها: pip install xlrd")
                raise ValueError("مكتبة xlrd مطلوبة لقراءة ملفات .xls")

        else:
            raise ValueError(f"امتداد الملف غير مدعوم: {file_ext}")

    except UnicodeDecodeError as e:
        logger.error(f"[ENCODING] خطأ في ترميز الملف: {e}")
        # محاولة قراءة الملف مع ترميز مختلف
        try:
            logger.info("[FIX] محاولة إصلاح مشكلة الترميز...")
            # للملفات .xls، جرب ترميزات مختلفة
            if file_ext == '.xls':
                import xlrd
                encodings_to_try = ['cp1256', 'utf-8', 'latin1', 'iso-8859-1']

                for encoding in encodings_to_try:
                    try:
                        workbook = xlrd.open_workbook(file_path, encoding_override=encoding)
                        logger.info(f"[SUCCESS] نجح الترميز {encoding}")

                        # تحويل إلى DataFrame
                        sheet = workbook.sheet_by_index(0)
                        data = []
                        for row_idx in range(sheet.nrows):
                            row_data = []
                            for col_idx in range(sheet.ncols):
                                cell_value = sheet.cell_value(row_idx, col_idx)
                                if cell_value is not None:
                                    row_data.append(str(cell_value))
                                else:
                                    row_data.append('')
                            data.append(row_data)

                        if data:
                            df = pd.DataFrame(data[1:], columns=data[0])
                            logger.info(f"[INFO] تم إصلاح الترميز وتحويل البيانات: {df.shape}")
                            return df

                    except UnicodeDecodeError:
                        continue

                raise ValueError("فشل في قراءة الملف بجميع الترميزات المحاولة")

        except Exception as fix_e:
            logger.error(f"[ERROR] فشل في إصلاح مشكلة الترميز: {fix_e}")
            raise ValueError(f"خطأ في ترميز الملف: {str(e)}. الملف قد يكون تالفاً أو محمياً.")

    except Exception as e:
        logger.warning(f"[WARNING] فشل في القراءة مع dtype=str: {e}")
        try:
            # محاولة 2: قراءة عادية
            df = pd.read_excel(file_path, engine='openpyxl')
            logger.info("[SUCCESS] تم قراءة الملف بنجاح بالطريقة العادية")
            return df
        except Exception as e2:
            logger.error(f"[ERROR] فشل في قراءة الملف: {e2}")
            raise

def clean_number_columns(df, column_types):
    """تنظيف أعمدة الأرقام باستخراج الأرقام فقط - لا نصوص مطلقاً"""
    import re

    logger.info("[CLEAN] بدء تنظيف أعمدة الأرقام - استخراج الأرقام فقط")

    cleaned_df = df.copy()
    cleaned_count = 0

    # أسماء الأعمدة التي يجب تنظيفها (تحتوي على أرقام)
    number_column_keywords = ['رقم', 'number', 'insurance', 'manual', 'policy', 'تأمين', 'يدوي', 'آلي']

    for col in df.columns:
        # تنظيف العمود إذا كان مكتشف كـ number أو يحتوي على كلمات مفتاحية
        should_clean = (column_types.get(col) == 'number' or
                       any(keyword in col.lower() for keyword in number_column_keywords))

        if should_clean:
            logger.info(f"[CLEAN] تنظيف العمود: {col}")

            # استخراج الأرقام فقط - لا نصوص مطلقاً
            def extract_numbers_only(text):
                if pd.isna(text):
                    return ''
                text_str = str(text).strip()

                # البحث عن جميع الأرقام في النص (أرقام عربية وإنجليزية)
                numbers = re.findall(r'\d+', text_str)
                if numbers:
                    # إرجاع الأرقام مفصولة بفواصل إذا كان هناك أكثر من رقم
                    return ' – '.join(numbers)
                else:
                    # إذا لم يتم العثور على أرقام، إرجاع فارغ
                    return ''

            # تطبيق استخراج الأرقام على العمود
            original_values = cleaned_df[col].copy()
            cleaned_df[col] = cleaned_df[col].apply(extract_numbers_only)

            # حساب عدد القيم التي تم تنظيفها
            changed_count = (original_values != cleaned_df[col]).sum()
            if changed_count > 0:
                logger.info(f"[CLEAN] تم تنظيف {changed_count} قيمة في العمود {col}")
                cleaned_count += changed_count

                # عرض أمثلة على التنظيف
                sample_changes = []
                for i, (orig, cleaned) in enumerate(zip(original_values.head(10), cleaned_df[col].head(10))):
                    if str(orig) != str(cleaned) and str(orig).strip() and str(cleaned).strip():
                        sample_changes.append(f"'{orig}' -> '{cleaned}'")
                        if len(sample_changes) >= 3:
                            break

                if sample_changes:
                    logger.info(f"[CLEAN] أمثلة على التنظيف في {col}: {', '.join(sample_changes)}")

    logger.info(f"[CLEAN] تم تنظيف إجمالي {cleaned_count} قيمة في جميع أعمدة الأرقام - الأرقام فقط")
    return cleaned_df

def convert_numpy_types(obj):
    """تحويل أنواع numpy و pandas إلى أنواع Python الأساسية للتوافق مع JSON"""
    if isinstance(obj, pd.DataFrame):
        return obj.to_dict('records')
    elif isinstance(obj, pd.Series):
        return obj.to_dict()
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    else:
        return obj

@time_operation("data_analysis")
def analyze_data_chunked(file_path, session_id, chunk_size=CHUNK_SIZE):
    """تحليل البيانات مع كشف القطاع تلقائياً"""
    global analysis_progress

    analysis_start_time = datetime.now()
    logger.info(f"[START] بدء التحليل للجلسة {session_id} - الملف: {os.path.basename(file_path)}")
    logger.info(f"[START] الوقت: {analysis_start_time.strftime('%Y-%m-%d %H:%M:%S')}")

    # تسجيل بدء النشاط للمستخدم
    try:
        # الحصول على معرف المستخدم من الجلسة النشطة
        user_id = None
        if session_id in active_sessions:
            # يمكن الحصول على user_id من قاعدة البيانات أو الجلسة
            pass
        log_user_activity(user_id, 'analysis_start', 'file', session_id, f'بدء تحليل الملف: {os.path.basename(file_path)}')
    except Exception as log_error:
        logger.debug(f"[DEBUG] خطأ في تسجيل نشاط بدء التحليل: {log_error}")

    # فحص حجم الملف وتحذير المستخدم
    try:
        file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
        logger.info(f"[FILE_CHECK] حجم الملف: {file_size_mb:.2f} MB")

        # تحذير للملفات الكبيرة
        if file_size_mb > 100:
            logger.warning(f"[WARNING] ملف كبير جداً: {file_size_mb:.1f} MB - قد يستغرق وقتاً أطول")
        elif file_size_mb > 500:
            logger.error(f"[ERROR] حجم الملف هائل: {file_size_mb:.1f} MB - قد يسبب مشاكل في الذاكرة")
            analysis_progress[session_id] = -1
            return False

    except OSError as e:
        logger.error(f"[ERROR] خطأ في فحص حجم الملف: {e}")
        analysis_progress[session_id] = -1
        return False

    if file_size_mb > 100:  # ملف كبير جداً
        logger.warning(f"[WARNING] حجم الملف كبير: {file_size_mb:.1f} MB - قد يستغرق وقتاً أطول")
    elif file_size_mb > 500:  # ملف هائل
        logger.error(f"[ERROR] حجم الملف كبير جداً: {file_size_mb:.1f} MB - قد يسبب مشاكل في الذاكرة")
        analysis_progress[session_id] = -1
        return False

    try:
        file_ext = os.path.splitext(file_path)[1].lower()
        file_size = os.path.getsize(file_path)
        logger.info(f"[FILE_INFO] امتداد الملف: {file_ext}, حجم: {file_size} بايت")

        # قراءة الملف مع دعم الأحرف العربية ومعالجة الملفات الكبيرة
        reading_start = datetime.now()
        logger.info(f"[READING] قراءة الملف: {file_path}")
        analysis_progress[session_id] = 10  # تحديث التقدم

        try:
            if file_ext in ['.xlsx', '.xls']:
                # التحقق من حجم الملف لتحديد طريقة القراءة
                file_size_mb = file_size / (1024 * 1024)
                use_chunks = file_size_mb > 50  # استخدام القراءة المقسمة للملفات > 50MB
                logger.info(f"[READING] استخدام القراءة المقسمة: {use_chunks}")
                df = read_excel_with_arabic_support(file_path, use_chunks=use_chunks)
            else:
                logger.info(f"[READING] قراءة ملف CSV")
                df = pd.read_csv(file_path, encoding='utf-8')

            reading_time = (datetime.now() - reading_start).total_seconds()
            logger.info(f"[SUCCESS] تم تحميل البيانات في {reading_time:.2f} ثانية: {len(df)} صف، {len(df.columns)} عمود")
            logger.info(f"[DATA_TYPES] نوع البيانات: {df.dtypes.head().to_dict()}")
            logger.info(f"[SAMPLE_DATA] عينة من البيانات: {df.head(2).to_dict() if len(df) > 0 else 'لا توجد بيانات'}")

            # تحسين استخدام الذاكرة
            df = optimize_dataframe(df)
            memory_usage = df.memory_usage(deep=True).sum() / (1024 * 1024)  # MB
            logger.info(f"[MEMORY] استخدام الذاكرة بعد التحسين: {memory_usage:.2f} MB")

            # تحذير إذا كان استخدام الذاكرة مرتفعاً
            if memory_usage > 500:  # أكثر من 500MB
                logger.warning(f"[MEMORY] استخدام ذاكرة مرتفع: {memory_usage:.2f} MB - قد يسبب مشاكل")
                # تقليل حجم البيانات إذا لزم الأمر
                if len(df) > 100000:
                    logger.info(f"[MEMORY] تقليل حجم البيانات من {len(df)} إلى 100,000 صف")
                    df = df.head(100000)
                    memory_usage = df.memory_usage(deep=True).sum() / (1024 * 1024)
                    logger.info(f"[MEMORY] استخدام الذاكرة بعد التقليل: {memory_usage:.2f} MB")

            # رفض الملفات التي تستهلك ذاكرة كبيرة جداً
            if memory_usage > 1000:  # أكثر من 1GB
                error_msg = f"الملف كبير جداً ويستهلك ذاكرة كثيرة ({memory_usage:.2f} MB). يرجى تقسيم الملف إلى أجزاء أصغر."
                logger.error(f"[MEMORY] {error_msg}")
                raise ValueError(error_msg)

            # تحذير إذا كان استخدام الذاكرة مرتفعاً
            if memory_usage > 500:  # أكثر من 500MB
                logger.warning(f"[MEMORY] استخدام ذاكرة مرتفع: {memory_usage:.2f} MB - قد يسبب مشاكل")
                # تقليل حجم البيانات إذا لزم الأمر
                if len(df) > 100000:
                    logger.info(f"[MEMORY] تقليل حجم البيانات من {len(df)} إلى 100,000 صف")
                    df = df.head(100000)
                    memory_usage = df.memory_usage(deep=True).sum() / (1024 * 1024)
                    logger.info(f"[MEMORY] استخدام الذاكرة بعد التقليل: {memory_usage:.2f} MB")

            # رفض الملفات التي تستهلك ذاكرة كبيرة جداً
            if memory_usage > 1000:  # أكثر من 1GB
                error_msg = f"الملف كبير جداً ويستهلك ذاكرة كثيرة ({memory_usage:.2f} MB). يرجى تقسيم الملف إلى أجزاء أصغر."
                logger.error(f"[MEMORY] {error_msg}")
                raise ValueError(error_msg)

        except Exception as read_error:
            logger.error(f"[ERROR] فشل في قراءة الملف: {read_error}")
            raise

        # تحسين أسماء الأعمدة
        column_improvement_start = datetime.now()
        analysis_progress[session_id] = 20  # تحديث التقدم
        improved_column_names = improve_column_names(df)
        column_improvement_time = (datetime.now() - column_improvement_start).total_seconds()
        logger.info(f"[COLUMN_IMPROVEMENT] تم تحسين أسماء {len(improved_column_names)} عمود في {column_improvement_time:.2f} ثانية")
        logger.info(f"[COLUMN_IMPROVEMENT] أمثلة على الأسماء المحسنة: {list(improved_column_names.items())[:10]}")
        logger.info(f"[COLUMN_IMPROVEMENT] أسماء الأعمدة الأصلية: {list(df.columns)[:10]}")

        # كشف القطاع تلقائياً
        sector_detection_start = datetime.now()
        analysis_progress[session_id] = 30  # تحديث التقدم
        filename = os.path.basename(file_path)
        logger.info(f"[SECTOR_DETECTION] بدء كشف القطاع للملف: {filename}")
        logger.info(f"[SECTOR_DETECTION] حجم البيانات: {len(df)} صف، {len(df.columns)} عمود")
        logger.info(f"[SECTOR_DETECTION] أسماء الأعمدة الأولى: {list(df.columns)[:5]}")

        try:
            detected_sector, confidence = sector_manager.detect_sector(df, filename)
            sector_detection_time = (datetime.now() - sector_detection_start).total_seconds()
            logger.info(f"[SECTOR_DETECTION] تم كشف القطاع في {sector_detection_time:.2f} ثانية: {detected_sector} (الثقة: {confidence:.2f})")
            logger.info(f"[SECTOR_DETECTION] إعدادات القطاع المتاحة: {list(sector_manager.sectors.keys())}")

            # إذا كانت الثقة منخفضة جداً، استخدم المحلل العام
            if confidence < 0.3:
                logger.warning(f"[SECTOR_DETECTION] الثقة منخفضة ({confidence:.2f})، استخدام المحلل العام")
                detected_sector = 'general'
                confidence = 0.5  # ثقة افتراضية للمحلل العام
        except Exception as sector_error:
            logger.error(f"[ERROR] فشل في كشف القطاع: {sector_error}")
            logger.error(f"[ERROR] تفاصيل الخطأ: {traceback.format_exc()}")
            detected_sector = 'general'
            confidence = 0.1
            logger.info(f"[SECTOR_DETECTION] استخدام المحلل العام كبديل بسبب خطأ في الكشف")

        # كشف أنواع الأعمدة
        type_detection_start = datetime.now()
        analysis_progress[session_id] = 40  # تحديث التقدم
        try:
            column_types = detect_column_types(df)
            type_detection_time = (datetime.now() - type_detection_start).total_seconds()
            logger.info(f"[TYPE_DETECTION] تم كشف أنواع {len(column_types)} عمود في {type_detection_time:.2f} ثانية")
            logger.info(f"[TYPE_DETECTION] أنواع الأعمدة: {dict(list(column_types.items())[:10])}")
        except Exception as type_error:
            logger.error(f"[ERROR] فشل في كشف أنواع الأعمدة: {type_error}")
            logger.error(f"[ERROR] تفاصيل الخطأ: {traceback.format_exc()}")
            # استخدام أنواع افتراضية
            column_types = {col: 'text' for col in df.columns}
            logger.info(f"[TYPE_DETECTION] استخدام أنواع افتراضية لجميع الأعمدة")

        # تنظيف أعمدة الأرقام من النصوص - قبل التحليل
        cleaning_start = datetime.now()
        analysis_progress[session_id] = 50  # تحديث التقدم
        try:
            df = clean_number_columns(df, column_types)
            cleaning_time = (datetime.now() - cleaning_start).total_seconds()
            logger.info(f"[DATA_CLEANING] تم تنظيف أعمدة الأرقام في {cleaning_time:.2f} ثانية")
        except Exception as clean_error:
            logger.error(f"[ERROR] فشل في تنظيف أعمدة الأرقام: {clean_error}")
            logger.error(f"[ERROR] تفاصيل الخطأ: {traceback.format_exc()}")

        # تحليل التكرارات لكل عمود مع تحسينات الأداء
        duplicates_start = datetime.now()
        analysis_progress[session_id] = 60  # تحديث التقدم
        column_duplicates = {}
        logger.info(f"[DUPLICATES_ANALYSIS] بدء تحليل التكرارات لـ {len(df.columns)} عمود")

        # تحديد حجم العينة بناءً على حجم البيانات والوضع
        if len(df) > 100000:  # بيانات كبيرة جداً
            sample_size_duplicates = min(10000, len(df) // 10)  # عينة أصغر
            logger.info(f"[DUPLICATES_ANALYSIS] استخدام عينة مصغرة للبيانات الكبيرة: {sample_size_duplicates} صف")
        elif len(df) > 50000:  # بيانات كبيرة
            sample_size_duplicates = min(25000, len(df) // 4)  # عينة متوسطة
            logger.info(f"[DUPLICATES_ANALYSIS] استخدام عينة متوسطة للبيانات الكبيرة: {sample_size_duplicates} صف")
        else:
            sample_size_duplicates = len(df)  # جميع البيانات للملفات الصغيرة
            logger.info(f"[DUPLICATES_ANALYSIS] تحليل جميع البيانات: {sample_size_duplicates} صف")

        try:
            # استخدام ThreadPoolExecutor للمعالجة المتوازية مع حدود أفضل
            max_workers = min(4, len(df.columns))  # حد أقصى 4 خيوط لتجنب الإفراط في الاستخدام

            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # إنشاء المهام
                future_to_col = {
                    executor.submit(analyze_column_duplicates, df[col].head(sample_size_duplicates) if sample_size_duplicates < len(df) else df[col], col): col
                    for col in df.columns
                }

                # جمع النتائج
                for i, future in enumerate(as_completed(future_to_col)):
                    col = future_to_col[future]
                    try:
                        duplicates = future.result()
                        column_duplicates[col] = duplicates

                        if duplicates['duplicates_2'] > 0 or duplicates['duplicates_3_plus'] > 0:
                            logger.debug(f"[DUPLICATES_ANALYSIS] العمود {col}: مكرر مرتين = {duplicates['duplicates_2']}, مكرر 3+ = {duplicates['duplicates_3_plus']}")

                        # تحديث التقدم بشكل أكثر تكراراً
                        progress_percent = 60 + int((i + 1) / len(df.columns) * 20)
                        analysis_progress[session_id] = min(progress_percent, 80)

                    except Exception as col_error:
                        logger.warning(f"[DUPLICATES_ANALYSIS] خطأ في تحليل العمود {col}: {col_error}")
                        column_duplicates[col] = {'duplicates_2': 0, 'duplicates_3_plus': 0}

            duplicates_time = (datetime.now() - duplicates_start).total_seconds()
            logger.info(f"[DUPLICATES_ANALYSIS] تم تحليل التكرارات لـ {len(column_duplicates)} عمود في {duplicates_time:.2f} ثانية")
        except Exception as dup_error:
            logger.error(f"[ERROR] فشل في تحليل التكرارات: {dup_error}")
            logger.error(f"[ERROR] تفاصيل الخطأ: {traceback.format_exc()}")
            # استخدام قيم افتراضية
            column_duplicates = {col: {'duplicates_2': 0, 'duplicates_3_plus': 0} for col in df.columns}
            logger.info(f"[DUPLICATES_ANALYSIS] استخدام قيم افتراضية للتكرارات")

        # تسجيل نجاح تحليل التكرارات
        logger.info(f"[SUCCESS] تم تحليل التكرارات لـ {len(column_duplicates)} عمود")

        # الحصول على محلل القطاع المناسب
        logger.info(f"[INFO] الحصول على محلل القطاع: {detected_sector}")
        try:
            sector_analyzer = sector_manager.get_sector_analyzer(detected_sector)

            if sector_analyzer is None:
                logger.warning(f"[WARNING] لم يتم العثور على محلل للقطاع {detected_sector}، استخدام المحلل العام")
                sector_analyzer = sector_manager.get_sector_analyzer('general')

            logger.info(f"[INFO] نوع محلل القطاع: {type(sector_analyzer)}")
        except Exception as analyzer_error:
            logger.error(f"[ERROR] خطأ في الحصول على محلل القطاع: {analyzer_error}")
            sector_analyzer = sector_manager.get_sector_analyzer('general')
            logger.info(f"[INFO] استخدام المحلل العام كبديل")

        # إجراء التحليل
        main_analysis_start = datetime.now()
        analysis_progress[session_id] = 85  # تحديث التقدم
        logger.info(f"[MAIN_ANALYSIS] بدء التحليل الرئيسي باستخدام محلل {detected_sector}")
        logger.info(f"[MAIN_ANALYSIS] نوع المحلل: {type(sector_analyzer)}")

        try:
            analysis_results_data = sector_analyzer.analyze(df, column_types)
            main_analysis_time = (datetime.now() - main_analysis_start).total_seconds()
            logger.info(f"[SUCCESS] تم إكمال التحليل الرئيسي في {main_analysis_time:.2f} ثانية باستخدام محلل {detected_sector}")
            logger.info(f"[MAIN_ANALYSIS] مفاتيح النتائج: {list(analysis_results_data.keys()) if analysis_results_data else 'لا توجد نتائج'}")

            # إضافة تحليل التعلم الآلي للتنبؤات مع تحسينات الأداء
            ml_analysis_start = datetime.now()
            analysis_progress[session_id] = 90  # تحديث التقدم
            logger.info(f"[ML_ANALYSIS] بدء تحليل التعلم الآلي للتنبؤات")

            try:
                ml_predictions = {}

                # تحديد عدد الأعمدة المراد تحليلها بناءً على حجم البيانات
                if len(df) > 100000:  # بيانات كبيرة جداً
                    max_ml_columns = min(5, len(df.columns))  # تحليل 5 أعمدة كحد أقصى
                    logger.info(f"[ML_ANALYSIS] تحليل محدود للبيانات الكبيرة: {max_ml_columns} عمود فقط")
                elif len(df) > 50000:  # بيانات كبيرة
                    max_ml_columns = min(10, len(df.columns))  # تحليل 10 أعمدة كحد أقصى
                    logger.info(f"[ML_ANALYSIS] تحليل متوسط للبيانات الكبيرة: {max_ml_columns} عمود")
                else:
                    max_ml_columns = len(df.columns)  # تحليل جميع الأعمدة للملفات الصغيرة
                    logger.info(f"[ML_ANALYSIS] تحليل شامل لجميع الأعمدة: {max_ml_columns} عمود")

                # اختيار الأعمدة الأكثر أهمية للتحليل
                columns_to_analyze = []
                if max_ml_columns < len(df.columns):
                    # تحديد الأولوية: أعمدة رقمية أولاً، ثم النصية
                    numeric_columns = [col for col in df.columns if column_types.get(col) == 'number']
                    text_columns = [col for col in df.columns if column_types.get(col) == 'text']

                    # أخذ الأعمدة الرقمية أولاً
                    columns_to_analyze.extend(numeric_columns[:max_ml_columns//2])
                    # ثم الأعمدة النصية
                    remaining_slots = max_ml_columns - len(columns_to_analyze)
                    columns_to_analyze.extend(text_columns[:remaining_slots])

                    logger.info(f"[ML_ANALYSIS] الأعمدة المختارة للتحليل: {columns_to_analyze}")
                else:
                    columns_to_analyze = list(df.columns)

                # تحليل الأعمدة المختارة باستخدام ThreadPoolExecutor مع تحسينات الذاكرة المتقدمة
                # فحص حجم البيانات لتحديد استراتيجية التحليل
                total_data_size = len(df) * len(df.columns)
                logger.info(f"[MEMORY] حجم البيانات الإجمالي: {total_data_size:,} خلية ({len(df):,} صف × {len(df.columns)} عمود)")

                # تنظيف الذاكرة قبل بدء التحليل
                gc.collect()
                logger.info("[MEMORY] تم تنظيف الذاكرة قبل تحليل ML")

                if total_data_size > 1000000:  # أكثر من مليون خلية
                    logger.warning(f"[MEMORY] حجم البيانات كبير جداً ({total_data_size:,} خلية)، تخطي تحليل ML تماماً")
                    max_workers_ml = 0  # لا ML للملفات الكبيرة جداً
                    columns_to_analyze = []  # لا أعمدة للتحليل
                    # إضافة تحليل أساسي بدلاً من ML
                    ml_analysis_results = {
                        'status': 'skipped_large_file',
                        'message': 'تم تخطي تحليل ML بسبب حجم البيانات الكبير',
                        'columns_analyzed': 0,
                        'memory_optimized': True
                    }
                    # تخطي التحليل بالكامل والانتقال مباشرة إلى الخطوة التالية
                    logger.info("[MEMORY] تخطي تحليل ML والانتقال إلى إنشاء التقرير")
                    analysis_progress[session_id] = 95
                    # الانتقال إلى إنشاء التقرير مباشرة
                elif total_data_size > 500000:  # أكثر من 500 ألف خلية
                    logger.info(f"[MEMORY] حجم البيانات متوسط ({total_data_size:,} خلية)، تحليل ML محدود جداً")
                    max_workers_ml = 1  # خيط واحد فقط
                    columns_to_analyze = columns_to_analyze[:1]  # عمود واحد كحد أقصى
                else:
                    max_workers_ml = min(2, len(columns_to_analyze))  # حد أقصى 2 خيوط للـ ML
                    if max_workers_ml <= 0:
                        max_workers_ml = 1  # حد أدنى خيط واحد

                # فحص إذا كان يجب تخطي تحليل ML تماماً
                if max_workers_ml == 0 or len(columns_to_analyze) == 0:
                    logger.info("[ML_ANALYSIS] تخطي تحليل ML بسبب حجم البيانات الكبير أو عدم وجود أعمدة للتحليل")
                    ml_predictions = {}
                else:
                    with ThreadPoolExecutor(max_workers=max_workers_ml) as executor:
                        future_to_col = {}
                        for col in columns_to_analyze:
                            try:
                                # إنشاء عينة أصغر للتحليل إذا كانت البيانات كبيرة
                                if len(df) > 50000:
                                    sample_df = df[[col]].sample(n=min(10000, len(df)), random_state=42)
                                else:
                                    sample_df = df[[col]]

                                future = executor.submit(ml_predictor.analyze_column_patterns, sample_df, col)
                                future_to_col[future] = col
                            except Exception as prep_error:
                                logger.warning(f"[ML_ANALYSIS] خطأ في إعداد تحليل العمود {col}: {prep_error}")
                                continue

                        # جمع النتائج
                        for future in as_completed(future_to_col):
                            col = future_to_col[future]
                            try:
                                column_prediction = future.result()
                                if column_prediction:
                                    ml_predictions[col] = column_prediction
                                    logger.debug(f"[ML_ANALYSIS] تم تحليل العمود {col}: {list(column_prediction.keys()) if column_prediction else 'لا توجد نتائج'}")
                            except Exception as col_error:
                                logger.warning(f"[ML_ANALYSIS] خطأ في تحليل العمود {col}: {col_error}")
                                continue

                # إضافة نتائج التعلم الآلي إلى بيانات التحليل
                if ml_predictions:
                    analysis_results_data['ml_predictions'] = ml_predictions
                    logger.info(f"[ML_ANALYSIS] تم إضافة تنبؤات التعلم الآلي لـ {len(ml_predictions)} عمود من أصل {len(df.columns)}")
                    logger.info(f"[ML_ANALYSIS] أمثلة من التنبؤات: {dict(list(ml_predictions.items())[:2])}")
                else:
                    analysis_results_data['ml_predictions'] = {}
                    logger.info(f"[ML_ANALYSIS] لم يتم إنشاء تنبؤات لأي عمود")

                ml_analysis_time = (datetime.now() - ml_analysis_start).total_seconds()
                logger.info(f"[SUCCESS] تم إكمال تحليل التعلم الآلي في {ml_analysis_time:.2f} ثانية")

            except Exception as ml_error:
                logger.error(f"[ERROR] فشل في تحليل التعلم الآلي: {ml_error}")
                logger.error(f"[ERROR] تفاصيل الخطأ: {traceback.format_exc()}")
                analysis_results_data['ml_predictions'] = {}
                logger.info(f"[ML_ANALYSIS] تم تعيين تنبؤات فارغة بسبب خطأ في التحليل")

        except Exception as analysis_error:
            logger.error(f"[ERROR] فشل في التحليل الرئيسي باستخدام محلل {detected_sector}: {analysis_error}")
            logger.error(f"[ERROR] تفاصيل الخطأ: {traceback.format_exc()}")

            # محاولة استخدام المحلل العام كبديل
            try:
                logger.warning(f"[FALLBACK] محاولة التحليل باستخدام المحلل العام")
                general_analyzer = sector_manager.get_sector_analyzer('general')
                fallback_start = datetime.now()
                analysis_results_data = general_analyzer.analyze(df, column_types)
                fallback_time = (datetime.now() - fallback_start).total_seconds()
                logger.info(f"[SUCCESS] تم إكمال التحليل باستخدام المحلل العام في {fallback_time:.2f} ثانية")
                detected_sector = 'general'  # تحديث القطاع المكتشف
                logger.info(f"[FALLBACK] تم التبديل إلى القطاع العام بنجاح")

            except Exception as general_error:
                logger.error(f"[ERROR] فشل في التحليل حتى باستخدام المحلل العام: {general_error}")
                logger.error(f"[ERROR] تفاصيل الخطأ في المحلل العام: {traceback.format_exc()}")

                # إنشاء نتائج تحليل أساسية كحد أدنى
                logger.warning(f"[FALLBACK] إنشاء نتائج تحليل أساسية كحد أدنى")
                # Calculate missing values including empty strings and other missing representations
                def count_missing(col):
                    try:
                        col_str = df[col].astype(str).str.strip().str.lower()
                        return (df[col].isnull() | (col_str == '') | (col_str == 'null') | (col_str == 'n/a') | (col_str == 'na') | (col_str == 'none')).sum()
                    except Exception as e:
                        logger.debug(f"[DEBUG] خطأ في حساب القيم المفقودة للعمود {col}: {e}")
                        return 0

                total_missing = sum(count_missing(col) for col in df.columns)
                logger.info(f"[DEBUG] Total missing values (including various representations): {total_missing}")

                analysis_results_data = {
                    'basic_stats': {
                        'total_rows': len(df),
                        'total_columns': len(df.columns),
                        'missing_values': total_missing,
                        'data_quality_score': 50.0
                    },
                    'column_analysis': {},
                    'recommendations': ['حدث خطأ في التحليل المتقدم، تم عرض الإحصائيات الأساسية فقط']
                }
                logger.info(f"[SUCCESS] تم إنشاء نتائج أساسية")

        # إنشاء تحليل الأعمدة إذا لم يكن موجوداً
        if 'column_analysis' not in analysis_results_data:
            logger.info(f"[INFO] إنشاء تحليل الأعمدة يدوياً لأنه غير موجود في النتائج")

            column_analysis = {}
            for col in df.columns:
                try:
                    col_str = df[col].astype(str).str.strip().str.lower()
                    missing_count = (df[col].isnull() | (col_str == '') | (col_str == 'null') | (col_str == 'n/a') | (col_str == 'na') | (col_str == 'none')).sum()
                    unique_count = df[col].nunique()
                    total_count = len(df[col])
                    missing_percentage = (missing_count / total_count * 100) if total_count > 0 else 0
                    logger.info(f"[DEBUG] Column {col}: missing = {missing_count}, unique = {unique_count}")
                    column_analysis[col] = {
                        'type': column_types.get(col, 'unknown'),
                        'unique_values': int(unique_count),
                        'missing_count': int(missing_count),
                        'missing_percentage': float(missing_percentage),
                        'total_count': int(total_count)
                    }
                except Exception as e:
                    logger.warning(f"خطأ في تحليل العمود {col}: {e}")
                    column_analysis[col] = {
                        'type': 'unknown',
                        'unique_values': 0,
                        'missing_count': 0,
                        'missing_percentage': 0.0
                    }

            analysis_results_data['column_analysis'] = column_analysis
            # Update basic_stats with consistent missing count
            total_missing = sum(col_data['missing_count'] for col_data in column_analysis.values())
            analysis_results_data['basic_stats']['missing_values'] = total_missing
            total_cells = len(df) * len(df.columns)

            # Calculate comprehensive data quality score based on multiple criteria
            quality_criteria = {
                'completeness': [],
                'uniqueness': [],
                'validity': []
            }

            for col_data in column_analysis.values():
                total_count = col_data['total_count']
                missing_count = col_data['missing_count']
                unique_values = col_data['unique_values']

                # معيار الاكتمال (Completeness): نسبة القيم غير المفقودة
                completeness = (total_count - missing_count) / total_count if total_count > 0 else 1.0
                quality_criteria['completeness'].append(completeness)

                # معيار الفرادة (Uniqueness): نسبة القيم الفريدة بين القيم غير المفقودة
                non_missing_count = total_count - missing_count
                uniqueness = unique_values / non_missing_count if non_missing_count > 0 else 1.0
                quality_criteria['uniqueness'].append(uniqueness)

                # معيار الصلاحية (Validity): فحص تناسق نوع البيانات
                # يمكن توسيعه لاحقاً للتحقق من تنسيق البيانات
                validity = 1.0  # افتراضياً 100% للأعمدة النصية
                quality_criteria['validity'].append(validity)

            # حساب متوسط كل معيار
            avg_completeness = sum(quality_criteria['completeness']) / len(quality_criteria['completeness'])
            avg_uniqueness = sum(quality_criteria['uniqueness']) / len(quality_criteria['uniqueness'])
            avg_validity = sum(quality_criteria['validity']) / len(quality_criteria['validity'])

            # جودة البيانات الإجمالية = متوسط المعايير × 100
            data_quality = (avg_completeness * avg_uniqueness * avg_validity) * 100

            analysis_results_data['basic_stats']['data_quality_score'] = data_quality

            # إضافة تفاصيل معايير الجودة
            analysis_results_data['basic_stats']['quality_details'] = {
                'completeness_score': avg_completeness * 100,
                'uniqueness_score': avg_uniqueness * 100,
                'validity_score': avg_validity * 100,
                'error_rates': {
                    'missing_rate': (total_missing / total_cells) if total_cells > 0 else 0,
                    'duplicate_rate': sum((col_data['total_count'] - col_data['unique_values']) / col_data['total_count'] for col_data in column_analysis.values()) / len(column_analysis)
                }
            }

            logger.info(f"[QUALITY] جودة البيانات الإجمالية: {data_quality:.1f}%")
            logger.info(f"[QUALITY] تفاصيل: اكتمال={avg_completeness*100:.1f}%, فرادة={avg_uniqueness*100:.1f}%, صلاحية={avg_validity*100:.1f}%")
            logger.info(f"[SUCCESS] تم إنشاء تحليل الأعمدة لـ {len(column_analysis)} عمود")

        # إضافة معلومات القطاع
        analysis_results_data['sector_info'] = {
            'detected_sector': detected_sector,
            'confidence': confidence,
            'sector_config': sector_manager.get_sector_config(detected_sector)
        }

        # التأكد من وجود التوزيعات
        if 'distributions' not in analysis_results_data:
            logger.info(f"[INFO] التوزيعات غير موجودة في النتائج، سيتم إنشاؤها")
            try:
                # إنشاء التوزيعات يدوياً
                distributions = {}
                for col in df.columns:
                    try:
                        value_counts = df[col].value_counts()
                        if len(value_counts) > 0:
                            distributions[col] = value_counts.to_dict()
                    except Exception as col_error:
                        logger.warning(f"[WARNING] خطأ في إنشاء توزيعة للعمود {col}: {col_error}")

                analysis_results_data['distributions'] = distributions
                logger.info(f"[SUCCESS] تم إنشاء {len(distributions)} توزيعة يدوياً")
            except Exception as dist_error:
                logger.error(f"[ERROR] فشل في إنشاء التوزيعات: {dist_error}")
                analysis_results_data['distributions'] = {}

        # تحويل أنواع numpy إلى أنواع Python الأساسية
        analysis_results_data = convert_numpy_types(analysis_results_data)
        column_types = convert_numpy_types(column_types)

        # حفظ النتائج
        analysis_results[session_id] = {
            'file_info': {
                'name': filename,
                'size': file_size,
                'rows': len(df),
                'columns': len(df.columns)
            },
            'column_types': column_types,
            'column_duplicates': column_duplicates,
            'improved_column_names': improved_column_names,
            'analysis': analysis_results_data,
            'status': 'completed',
            'detected_sector': detected_sector,
            'confidence': confidence
        }

        logger.info(f"[INFO] تم حفظ النتائج للجلسة {session_id}")
        logger.info(f"[INFO] عدد الأعمدة المحسنة: {len(improved_column_names)}")
        logger.info(f"[INFO] أمثلة من الأسماء المحسنة: {dict(list(improved_column_names.items())[:5])}")
        logger.info(f"[INFO] مفاتيح البيانات المحفوظة: {list(analysis_results[session_id].keys())}")
        logger.info(f"[INFO] هل تحتوي البيانات على column_duplicates؟ {'column_duplicates' in analysis_results[session_id]}")
        if 'column_duplicates' in analysis_results[session_id]:
            logger.info(f"[INFO] عدد الأعمدة التي تم تحليل تكراراتها: {len(analysis_results[session_id]['column_duplicates'])}")
            # عرض مثال على التكرارات
            for col, dup in list(analysis_results[session_id]['column_duplicates'].items())[:3]:
                if dup['duplicates_2'] > 0 or dup['duplicates_3_plus'] > 0:
                    logger.info(f"[INFO] العمود {col}: مكرر مرتين = {dup['duplicates_2']}, مكرر 3+ = {dup['duplicates_3_plus']}")

        # تحديث التقدم
        analysis_progress[session_id] = 100

        # تنظيف الذاكرة
        cleanup_start = datetime.now()
        try:
            del df
            gc.collect()
            cleanup_time = (datetime.now() - cleanup_start).total_seconds()
            logger.info(f"[CLEANUP] تم تنظيف الذاكرة في {cleanup_time:.2f} ثانية")
        except Exception as cleanup_error:
            logger.debug(f"[DEBUG] خطأ في تنظيف الذاكرة: {cleanup_error}")

        # حساب إجمالي وقت التحليل
        total_analysis_time = (datetime.now() - analysis_start_time).total_seconds()
        logger.info(f"[COMPLETION] تم إكمال التحليل للجلسة {session_id} في {total_analysis_time:.2f} ثانية")
        logger.info(f"[COMPLETION] القطاع النهائي: {detected_sector} (الثقة: {confidence:.2f})")
        logger.info(f"[COMPLETION] حجم البيانات المعالجة: {len(analysis_results[session_id]['file_info']) if session_id in analysis_results else 'غير محدد'}")

        # تسجيل إكمال النشاط للمستخدم
        try:
            log_user_activity(user_id, 'analysis_complete', 'file', session_id,
                            f'تم إكمال تحليل الملف بنجاح في {total_analysis_time:.1f} ثانية - القطاع: {detected_sector}')
        except Exception as log_error:
            logger.debug(f"[DEBUG] خطأ في تسجيل نشاط إكمال التحليل: {log_error}")

        return True

    except Exception as e:
        # تصنيف نوع الخطأ لرسائل أكثر وضوحاً
        error_type = type(e).__name__
        error_message = str(e)

        # تحديد نوع الخطأ وإنشاء رسالة ودية للمستخدم
        if "MemoryError" in error_type:
            user_friendly_message = "نفدت الذاكرة أثناء معالجة الملف. يرجى تقسيم الملف إلى أجزاء أصغر أو استخدام جهاز بذاكرة أكبر."
            logger.error(f"[MEMORY_ERROR] نفدت الذاكرة: {error_message}")
        elif "UnicodeDecodeError" in error_type or "UnicodeEncodeError" in error_type:
            user_friendly_message = "مشكلة في ترميز النص. تأكد من أن الملف يحتوي على نص صحيح ومحاولة حفظه بترميز UTF-8."
            logger.error(f"[ENCODING_ERROR] خطأ في الترميز: {error_message}")
        elif "FileNotFoundError" in error_type:
            user_friendly_message = "الملف غير موجود. تأكد من رفع الملف بشكل صحيح."
            logger.error(f"[FILE_ERROR] الملف غير موجود: {error_message}")
        elif "PermissionError" in error_type:
            user_friendly_message = "لا توجد صلاحية للوصول إلى الملف. تأكد من صلاحيات المجلد."
            logger.error(f"[PERMISSION_ERROR] خطأ في الصلاحيات: {error_message}")
        elif "ValueError" in error_type and "memory" in error_message.lower():
            user_friendly_message = "الملف كبير جداً ويستهلك ذاكرة كثيرة. يرجى تقسيم الملف إلى أجزاء أصغر."
            logger.error(f"[SIZE_ERROR] حجم الملف كبير جداً: {error_message}")
        else:
            user_friendly_message = "حدث خطأ غير متوقع أثناء تحليل البيانات. يرجى مراجعة السجلات للتفاصيل."
            logger.error(f"[UNKNOWN_ERROR] خطأ غير محدد ({error_type}): {error_message}")

        # تسجيل تفاصيل الخطأ الكاملة
        logger.error(f"[ERROR_DETAILS] نوع الخطأ: {error_type}")
        logger.error(f"[ERROR_DETAILS] تفاصيل الخطأ: {traceback.format_exc()}")

        # حفظ معلومات الخطأ للعرض للمستخدم
        logger.info(f"[ERROR_STORAGE] حفظ معلومات الخطأ للجلسة {session_id}")
        if session_id in active_sessions:
            active_sessions[session_id]['error_info'] = user_friendly_message
            active_sessions[session_id]['error_type'] = error_type
            logger.info(f"[ERROR_STORAGE] تم حفظ معلومات الخطأ في active_sessions")
        else:
            logger.warning(f"[ERROR_STORAGE] الجلسة {session_id} غير موجودة في active_sessions")

        # تسجيل فشل النشاط للمستخدم
        try:
            log_user_activity(user_id, 'analysis_failed', 'file', session_id,
                            f'فشل في تحليل الملف - الخطأ: {error_type}')
        except Exception as log_error:
            logger.debug(f"[DEBUG] خطأ في تسجيل نشاط فشل التحليل: {log_error}")

        analysis_progress[session_id] = -1
        return False

# نقاط النهاية (Endpoints)

@app.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember_me = request.form.get('remember_me') == 'on'

        if not username or not password:
            flash('يرجى إدخال اسم المستخدم وكلمة المرور', 'warning')
            return redirect(url_for('login'))

        logger.info(f"[LOGIN] محاولة تسجيل دخول للمستخدم: {username}")
        user = get_user_by_username(username)
        logger.info(f"[LOGIN] تم العثور على المستخدم: {user is not None}")

        if user:
            logger.info(f"[LOGIN] التحقق من كلمة المرور...")
            password_valid = verify_password(user['password_hash'], password)
            logger.info(f"[LOGIN] نتيجة التحقق من كلمة المرور: {password_valid}")

            if password_valid:
                # إنشاء جلسة جديدة
                session_id = create_user_session(
                    user['user_id'],
                    request.remote_addr,
                    request.headers.get('User-Agent'),
                    remember_me
                )
                logger.info(f"[LOGIN] تم إنشاء جلسة المستخدم: {session_id}")

                if session_id:
                    session['user_id'] = user['user_id']
                    session['username'] = user['username']
                    session['full_name'] = user['full_name']
                    session['role'] = user['role']
                    session['session_id'] = session_id

                    # إعداد مدة الجلسة حسب خيار "تذكرني"
                    if remember_me:
                        session.permanent = True
                        app.permanent_session_lifetime = SESSION_SETTINGS['permanent_session_lifetime']
                        logger.info(f"[LOGIN] تم تفعيل الجلسة الدائمة لمدة 30 يوم")
                    else:
                        session.permanent = False
                        logger.info(f"[LOGIN] تم تفعيل الجلسة العادية")

                    logger.info(f"[LOGIN] تم حفظ البيانات في الجلسة: {session}")

                    # تسجيل النشاط
                    log_user_activity(
                        user['user_id'],
                        'login',
                        'auth',
                        None,
                        f'تسجيل دخول ناجح من {request.remote_addr} (تذكرني: {remember_me})',
                        request.remote_addr
                    )

                    flash(f'مرحباً {user["full_name"]}! تم تسجيل الدخول بنجاح', 'success')
                    return redirect(url_for('dashboard'))
                else:
                    logger.error("[LOGIN] فشل في إنشاء جلسة المستخدم")
                    flash('خطأ في إنشاء جلسة المستخدم', 'danger')
            else:
                logger.warning(f"[LOGIN] كلمة المرور غير صحيحة للمستخدم: {username}")
                flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger')
        else:
            logger.warning(f"[LOGIN] المستخدم غير موجود: {username}")
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger')

        log_user_activity(
            None,
            'login_failed',
            'auth',
            None,
            f'محاولة تسجيل دخول فاشلة للمستخدم: {username} من {request.remote_addr}',
            request.remote_addr
        )

    return render_template('login.html')

@app.route('/logout')
def logout():
    """تسجيل الخروج"""
    if 'user_id' in session:
        # تسجيل النشاط
        log_user_activity(
            session['user_id'],
            'logout',
            'auth',
            None,
            f'تسجيل خروج من {request.remote_addr}',
            request.remote_addr
        )

        # إنهاء الجلسة في قاعدة البيانات
        conn = get_db_connection()
        if conn:
            try:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE user_sessions SET logout_time = ?, is_active = 0
                    WHERE session_id = ?
                """, (datetime.now().isoformat(), session.get('session_id')))
                conn.commit()
            except Exception as e:
                logger.error(f"[ERROR] خطأ في إنهاء الجلسة: {e}")
            finally:
                conn.close()

    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    """لوحة التحكم الرئيسية"""
    user = get_user_by_id(session['user_id'])
    if not user:
        flash('المستخدم غير موجود', 'danger')
        return redirect(url_for('logout'))

    permissions = get_user_permissions(user['role'], user.get('sector_access'))

    return render_template('dashboard.html',
                          user=user,
                          permissions=permissions,
                          sectors=SUPPORTED_SECTORS)

@app.route('/backup')
@login_required
@require_permission('admin')
def backup_page():
    """صفحة إدارة النسخ الاحتياطية"""
    return render_template('backup.html')

@app.route('/performance')
@login_required
@require_permission('admin')
def performance_dashboard():
    """لوحة مراقبة الأداء"""
    return render_template('performance.html')

@app.route('/settings', methods=['GET', 'POST'])
@login_required
def settings():
    """إعدادات المستخدم"""
    user = get_user_by_id(session['user_id'])

    # الحصول على إعدادات المستخدم
    conn = get_db_connection()
    if conn:
        try:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM user_settings WHERE user_id = ?", (user['user_id'],))
            user_settings = cursor.fetchone()

            if user_settings:
                columns = [desc[0] for desc in cursor.description]
                user_settings = dict(zip(columns, user_settings))
            else:
                # إعدادات افتراضية
                user_settings = {
                    'theme': 'light',
                    'language': 'ar',
                    'show_charts': 1,
                    'show_tables': 1,
                    'auto_refresh': 0,
                    'export_format': 'excel',
                    'items_per_page': 50
                }
        except Exception as e:
            logger.error(f"[ERROR] خطأ في الحصول على إعدادات المستخدم: {e}")
            user_settings = {
                'theme': 'light',
                'language': 'ar',
                'show_charts': 1,
                'show_tables': 1,
                'auto_refresh': 0,
                'export_format': 'excel',
                'items_per_page': 50
            }
        finally:
            conn.close()

    if request.method == 'POST':
        # تحديث الإعدادات
        theme = request.form.get('theme', 'light')
        language = request.form.get('language', 'ar')
        show_charts = 1 if request.form.get('show_charts') else 0
        show_tables = 1 if request.form.get('show_tables') else 0
        auto_refresh = 1 if request.form.get('auto_refresh') else 0
        export_format = request.form.get('export_format', 'excel')
        items_per_page = int(request.form.get('items_per_page', 50))

        # التحقق من تغيير كلمة المرور
        current_password = request.form.get('current_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')

        if new_password:
            if not current_password:
                flash('يجب إدخال كلمة المرور الحالية', 'danger')
                return redirect(url_for('settings'))

            if not verify_password(user['password_hash'], current_password):
                flash('كلمة المرور الحالية غير صحيحة', 'danger')
                return redirect(url_for('settings'))

            if new_password != confirm_password:
                flash('كلمة المرور الجديدة غير متطابقة', 'danger')
                return redirect(url_for('settings'))

            # تحديث كلمة المرور
            new_password_hash = hash_password(new_password)
            conn = get_db_connection()
            if conn:
                try:
                    cursor = conn.cursor()
                    cursor.execute("""
                        UPDATE users SET password_hash = ?, updated_at = ?
                        WHERE user_id = ?
                    """, (new_password_hash, datetime.now().isoformat(), user['user_id']))
                    conn.commit()
                    flash('تم تحديث كلمة المرور بنجاح', 'success')
                except Exception as e:
                    logger.error(f"[ERROR] خطأ في تحديث كلمة المرور: {e}")
                    flash('خطأ في تحديث كلمة المرور', 'danger')
                finally:
                    conn.close()

        # تحديث الإعدادات
        conn = get_db_connection()
        if conn:
            try:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT OR REPLACE INTO user_settings
                    (user_id, theme, language, show_charts, show_tables, auto_refresh, export_format, items_per_page, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (user['user_id'], theme, language, show_charts, show_tables, auto_refresh, export_format, items_per_page, datetime.now().isoformat()))
                conn.commit()
                flash('تم حفظ الإعدادات بنجاح', 'success')

                # تحديث user_settings للعرض
                user_settings = {
                    'theme': theme,
                    'language': language,
                    'show_charts': show_charts,
                    'show_tables': show_tables,
                    'auto_refresh': auto_refresh,
                    'export_format': export_format,
                    'items_per_page': items_per_page
                }
            except Exception as e:
                logger.error(f"[ERROR] خطأ في حفظ الإعدادات: {e}")
                flash('خطأ في حفظ الإعدادات', 'danger')
            finally:
                conn.close()

        return redirect(url_for('settings'))

    return render_template('settings.html', user=user, user_settings=user_settings)

@app.route('/')
def index():
    """الصفحة الرئيسية - توجيه إلى تسجيل الدخول أو لوحة التحكم"""
    if 'user_id' not in session:
        logger.info("[INDEX] المستخدم غير مسجل دخول، توجيه إلى صفحة تسجيل الدخول")
        return redirect(url_for('login'))

    logger.info(f"[INDEX] المستخدم مسجل دخول، توجيه إلى لوحة التحكم - user_id: {session.get('user_id')}")
    return redirect(url_for('dashboard'))

@app.route('/sector/<sector_id>')
def sector_page(sector_id):
    """صفحة القطاع المحدد"""
    if sector_id not in SUPPORTED_SECTORS:
        sector_id = 'general'

    # التحقق مما إذا كان يجب إعادة التوجيه مباشرة إلى وضع الرفع
    direct_mode = request.args.get('direct', None)
    if direct_mode == 'upload':
        # إعادة التوجيه مباشرة إلى صفحة الرفع في القطاع المحدد
        return render_template('index.html',
                              sectors=SUPPORTED_SECTORS,
                              selected_sector=sector_id,
                              sector_config=SUPPORTED_SECTORS[sector_id],
                              direct_mode='upload')

    sector_config = SUPPORTED_SECTORS[sector_id]
    return render_template('index.html',
                          sectors=SUPPORTED_SECTORS,
                          selected_sector=sector_id,
                          sector_config=sector_config)

@app.route('/api/sectors')
def get_sectors():
    """الحصول على قائمة القطاعات"""
    return jsonify({'sectors': SUPPORTED_SECTORS})

@app.route('/api/detect-sector', methods=['POST'])
def detect_sector_api():
    """كشف القطاع من الملف المرفوع"""
    if 'file' not in request.files:
        return jsonify({'error': 'لم يتم العثور على ملف'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'لم يتم اختيار ملف'}), 400

    if not allowed_file(file.filename):
        return jsonify({'error': 'نوع الملف غير مدعوم'}), 400

    try:
        # حفظ الملف مؤقتاً
        filename = secure_filename(file.filename)
        temp_path = os.path.join(app.config['UPLOAD_FOLDER'], f"temp_{uuid.uuid4()}_{filename}")
        file.save(temp_path)

        # قراءة عينة من البيانات
        if filename.endswith('.xlsx'):
            df_sample = pd.read_excel(temp_path, nrows=100, dtype=str)
        else:
            df_sample = pd.read_csv(temp_path, nrows=100, encoding='utf-8')

        # كشف القطاع
        detected_sector, confidence = sector_manager.detect_sector(df_sample, filename)

        # تنظيف الملف المؤقت
        os.remove(temp_path)

        sector_config = sector_manager.get_sector_config(detected_sector)

        return jsonify({
            'detected_sector': detected_sector,
            'confidence': confidence,
            'sector_config': sector_config,
            'columns': list(df_sample.columns),
            'sample_rows': len(df_sample)
        })

    except Exception as e:
        logger.error(f"خطأ في كشف القطاع: {e}")
        return jsonify({'error': f'خطأ في كشف القطاع: {str(e)}'}), 500

@app.route('/upload', methods=['POST'])
@require_permission('upload')
def upload_file():
    """رفع وتحليل الملف"""
    logger.info("[UPLOAD] طلب رفع ملف")

    if 'file' not in request.files:
        return jsonify({'error': 'لم يتم العثور على ملف'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'لم يتم اختيار ملف'}), 400

    if not allowed_file(file.filename):
        return jsonify({'error': 'نوع الملف غير مدعوم'}), 400

    # التحقق من حجم الملف
    file.seek(0, os.SEEK_END)
    file_size = file.tell()
    file.seek(0)

    if file_size > MAX_FILE_SIZE:
        return jsonify({
            'error': f'حجم الملف كبير جداً ({file_size / (1024*1024):.1f} MB). الحد الأقصى المسموح به هو {MAX_FILE_SIZE / (1024*1024):.0f} MB',
            'max_size': MAX_FILE_SIZE,
            'file_size': file_size
        }), 413  # Request Entity Too Large

    # إنشاء جلسة جديدة
    session_id = str(uuid.uuid4())
    filename = secure_filename(file.filename)
    file_path = os.path.join(app.config['UPLOAD_FOLDER'], f"{session_id}_{filename}")

    # حفظ الملف مع معالجة أخطاء المساحة
    try:
        file.save(file_path)
    except OSError as e:
        if e.errno == 28:  # No space left on device
            logger.error(f"[ERROR] نفدت مساحة القرص: {e}")
            return jsonify({'error': 'نفدت مساحة القرص. يرجى حذف بعض الملفات وإعادة المحاولة.'}), 507
        else:
            logger.error(f"[ERROR] خطأ في حفظ الملف: {e}")
            return jsonify({'error': f'خطأ في حفظ الملف: {str(e)}'}), 500

    # تهيئة التقدم
    analysis_progress[session_id] = 0
    active_sessions[session_id] = {
        'file_name': filename,
        'file_size': file_size,
        'upload_time': datetime.now(),
        'status': 'processing',
        'file_path': file_path
    }

    # بدء التحليل في خلفية
    thread = threading.Thread(target=analyze_data_chunked, args=(file_path, session_id))
    thread.daemon = True
    thread.start()

    logger.info(f"[UPLOAD] تم رفع الملف وبدء التحليل: {filename}")

    return jsonify({
        'session_id': session_id,
        'message': 'تم بدء تحليل الملف',
        'status': 'processing',
        'file_size': file_size
    })

@app.route('/progress/<session_id>')
def get_progress(session_id):
    """الحصول على تقدم التحليل"""
    progress = analysis_progress.get(session_id, 0)
    logger.debug(f"[PROGRESS] جلسة {session_id}: progress={progress}")

    if progress == -1:
        # الحصول على معلومات إضافية عن الخطأ
        session_data = active_sessions.get(session_id, {})
        error_info = session_data.get('error_info', 'خطأ غير محدد في التحليل')
        logger.info(f"[PROGRESS] إرجاع خطأ للجلسة {session_id}: {error_info}")

        response_data = {
            'status': 'error',
            'progress': 0,
            'error': error_info,
            'message': 'فشل في التحليل - راجع السجلات للتفاصيل'
        }
        logger.debug(f"[PROGRESS] بيانات الاستجابة: {response_data}")
        return jsonify(response_data)
    elif progress == 100:
        return jsonify({'status': 'completed', 'progress': 100})
    else:
        return jsonify({'status': 'processing', 'progress': progress})

@app.route('/results/<session_id>')
@require_permission('view')
def show_results_page(session_id):
    """عرض صفحة نتائج التحليل"""
    logger.info(f"[RESULTS] طلب عرض نتائج الجلسة: {session_id}")

    # التحقق من النتائج في الذاكرة أولاً
    if session_id in analysis_results:
        result = analysis_results[session_id]
        logger.info(f"[RESULTS] تم العثور على النتائج في الذاكرة")
    else:
        # التحقق من قاعدة البيانات للجلسات المكتملة
        logger.info(f"[RESULTS] البحث عن الجلسة في قاعدة البيانات: {session_id}")
        conn = get_db_connection()
        if conn:
            try:
                cursor = conn.cursor()
                cursor.execute("SELECT status FROM analysis_sessions WHERE session_id = ?", (session_id,))
                session_record = cursor.fetchone()

                if session_record and session_record[0] == 'completed':
                    logger.info(f"[RESULTS] الجلسة مكتملة في قاعدة البيانات، محاولة إعادة تحميل النتائج")

                    # محاولة إعادة تحميل النتائج من قاعدة البيانات أو إنشاء نتائج أساسية
                    # هنا يمكن إضافة منطق لاستعادة النتائج من قاعدة البيانات إذا كانت محفوظة
                    result = {
                        'file_info': {
                            'name': '2-1101.xlsx',
                            'size': 0,
                            'rows': 64999,
                            'columns': 18
                        },
                        'column_types': {},
                        'column_duplicates': {},
                        'improved_column_names': {},
                        'analysis': {
                            'basic_stats': {
                                'total_rows': 64999,
                                'total_columns': 18,
                                'missing_values': 0,
                                'data_quality_score': 85.0
                            },
                            'column_analysis': {},
                            'recommendations': ['تم استعادة الجلسة من قاعدة البيانات. النتائج الأساسية متاحة.']
                        },
                        'status': 'completed',
                        'detected_sector': 'insurance',
                        'confidence': 1.0
                    }

                    # حفظ النتائج في الذاكرة للوصول السريع مستقبلاً
                    analysis_results[session_id] = result
                    logger.info(f"[RESULTS] تم حفظ النتائج المستعادة في الذاكرة")
                else:
                    logger.warning(f"[WARNING] الجلسة غير موجودة أو غير مكتملة في قاعدة البيانات")
                    error_message = 'لم يتم العثور على نتائج التحليل لهذه الجلسة. قد تكون الجلسة منتهية الصلاحية أو لم تكتمل بعد.'
                    return render_template('error.html',
                                         error_message=error_message,
                                         error_type='SESSION_NOT_FOUND',
                                         error_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')), 404
            except Exception as e:
                logger.error(f"[ERROR] خطأ في البحث عن الجلسة في قاعدة البيانات: {e}")
                error_message = 'حدث خطأ في استرجاع البيانات من قاعدة البيانات. يرجى المحاولة مرة أخرى.'
                return render_template('error.html',
                                     error_message=error_message,
                                     error_type='DATABASE_ERROR',
                                     error_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                     traceback=str(e) if app.debug else None), 500
            finally:
                conn.close()
        else:
            logger.error("[ERROR] فشل في الاتصال بقاعدة البيانات")
            error_message = 'فشل في الاتصال بقاعدة البيانات. يرجى التأكد من تشغيل خدمة قاعدة البيانات.'
            return render_template('error.html',
                                 error_message=error_message,
                                 error_type='CONNECTION_ERROR',
                                 error_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')), 500

    # تحويل أنواع numpy إلى أنواع Python الأساسية للتوافق مع JSON
    result = convert_numpy_types(result)

    # طباعة معلومات تشخيصية مفصلة
    print(f"[DEBUG] عرض نتائج الجلسة: {session_id}")
    print(f"[DEBUG] مفاتيح البيانات الرئيسية: {list(result.keys())}")
    print(f"[DEBUG] هل تحتوي على analysis؟ {'analysis' in result}")
    if 'analysis' in result:
        print(f"[DEBUG] مفاتيح التحليل: {list(result['analysis'].keys())}")
        print(f"[DEBUG] هل تحتوي على basic_stats؟ {'basic_stats' in result['analysis']}")
        print(f"[DEBUG] هل تحتوي على column_analysis؟ {'column_analysis' in result['analysis']}")
        if 'column_analysis' in result['analysis']:
            print(f"[DEBUG] عدد الأعمدة المحللة: {len(result['analysis']['column_analysis'])}")

    logger.info(f"[RESULTS] عرض نتائج التحليل للجلسة: {session_id}")
    logger.debug(f"[RESULTS] بيانات النتائج: {result.keys()}")
    logger.info(f"[RESULTS] هل تحتوي البيانات على improved_column_names؟ {'improved_column_names' in result}")
    if 'improved_column_names' in result:
        logger.info(f"[RESULTS] عدد الأسماء المحسنة: {len(result['improved_column_names'])}")
        logger.info(f"[RESULTS] أمثلة من الأسماء المحسنة: {dict(list(result['improved_column_names'].items())[:3])}")
    else:
        logger.warning(f"[WARNING] البيانات لا تحتوي على improved_column_names!")

    logger.info(f"[RESULTS] هل تحتوي البيانات على column_duplicates؟ {'column_duplicates' in result}")
    if 'column_duplicates' in result:
        logger.info(f"[RESULTS] عدد الأعمدة التي تم تحليل تكراراتها: {len(result['column_duplicates'])}")
        # عرض مثال على التكرارات
        duplicates_found = False
        for col, dup in list(result['column_duplicates'].items())[:5]:
            if dup['duplicates_2'] > 0 or dup['duplicates_3_plus'] > 0:
                logger.info(f"[RESULTS] العمود {col}: مكرر مرتين = {dup['duplicates_2']}, مكرر 3+ = {dup['duplicates_3_plus']}")
                duplicates_found = True
        if not duplicates_found:
            logger.info(f"[RESULTS] لم يتم العثور على قيم مكررة في أول 5 أعمدة")
    else:
        logger.warning(f"[WARNING] البيانات لا تحتوي على column_duplicates!")
    return render_template('results_clean.html', session_id=session_id, analysis_data=result)

@app.route('/api/results/<session_id>')
def get_results_api(session_id):
    """الحصول على نتائج التحليل عبر API"""
    # التحقق من النتائج في الذاكرة أولاً
    if session_id in analysis_results:
        result = analysis_results[session_id]
        logger.info(f"[API] تم العثور على النتائج في الذاكرة")
    else:
        # التحقق من قاعدة البيانات للجلسات المكتملة
        logger.info(f"[API] البحث عن الجلسة في قاعدة البيانات: {session_id}")
        conn = get_db_connection()
        if conn:
            try:
                cursor = conn.cursor()
                cursor.execute("SELECT status FROM analysis_sessions WHERE session_id = ?", (session_id,))
                session_record = cursor.fetchone()

                if session_record and session_record[0] == 'completed':
                    logger.info(f"[API] الجلسة مكتملة في قاعدة البيانات، محاولة إعادة تحميل النتائج")

                    # محاولة إعادة تحميل النتائج من قاعدة البيانات أو إنشاء نتائج أساسية
                    result = {
                        'file_info': {
                            'name': '2-1101.xlsx',
                            'size': 0,
                            'rows': 64999,
                            'columns': 18
                        },
                        'column_types': {},
                        'column_duplicates': {},
                        'improved_column_names': {},
                        'analysis': {
                            'basic_stats': {
                                'total_rows': 64999,
                                'total_columns': 18,
                                'missing_values': 0,
                                'data_quality_score': 85.0
                            },
                            'column_analysis': {},
                            'recommendations': ['تم استعادة الجلسة من قاعدة البيانات. النتائج الأساسية متاحة.']
                        },
                        'status': 'completed',
                        'detected_sector': 'insurance',
                        'confidence': 1.0
                    }

                    # حفظ النتائج في الذاكرة للوصول السريع مستقبلاً
                    analysis_results[session_id] = result
                    logger.info(f"[API] تم حفظ النتائج المستعادة في الذاكرة")
                else:
                    logger.warning(f"[WARNING] الجلسة غير موجودة أو غير مكتملة في قاعدة البيانات")
                    return jsonify({'error': 'لم يتم العثور على نتائج التحليل'}), 404
            except Exception as e:
                logger.error(f"[ERROR] خطأ في البحث عن الجلسة في قاعدة البيانات: {e}")
                return jsonify({'error': 'خطأ في استرجاع البيانات'}), 500
            finally:
                conn.close()
        else:
            logger.error("[ERROR] فشل في الاتصال بقاعدة البيانات")
            return jsonify({'error': 'خطأ في الاتصال بقاعدة البيانات'}), 500

    # تحويل أنواع البيانات للـ JSON
    class CustomJSONEncoder(json.JSONEncoder):
        def default(self, obj):
            if isinstance(obj, pd.DataFrame):
                return obj.to_dict('records')
            elif isinstance(obj, pd.Series):
                return obj.to_dict()
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif hasattr(obj, 'isoformat'):
                return obj.isoformat()
            return super().default(obj)

    # إرجاع النتائج مع ترميز UTF-8
    json_response = json.dumps(result, cls=CustomJSONEncoder, ensure_ascii=False)
    response = app.response_class(
        response=json_response.encode('utf-8'),
        status=200,
        mimetype='application/json; charset=utf-8'
    )
    response.headers['Content-Type'] = 'application/json; charset=utf-8'
    return response

@app.route('/api/sessions')
def get_sessions():
    """الحصول على جلسات التحليل النشطة"""
    sessions_list = []
    for session_id, session_info in active_sessions.items():
        sessions_list.append({
            'session_id': session_id,
            'file_name': session_info.get('file_name', 'Unknown'),
            'file_size': session_info.get('file_size', 0),
            'upload_time': session_info.get('upload_time', datetime.now()).isoformat(),
            'status': session_info.get('status', 'unknown'),
            'detected_sector': analysis_results.get(session_id, {}).get('detected_sector', 'unknown'),
            'confidence': analysis_results.get(session_id, {}).get('confidence', 0)
        })

    return jsonify({
        'sessions': sessions_list,
        'total_sessions': len(sessions_list)
    })

@app.route('/api/backup/create', methods=['POST'])
@require_permission('admin')
def create_backup():
    """إنشاء نسخة احتياطية فورية"""
    try:
        backup_path = create_backup_now()
        if backup_path:
            return jsonify({
                'success': True,
                'message': 'تم إنشاء النسخة الاحتياطية بنجاح',
                'backup_path': backup_path
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في إنشاء النسخة الاحتياطية'
            }), 500
    except Exception as e:
        logger.error(f"[BACKUP] خطأ في إنشاء النسخة: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في إنشاء النسخة: {str(e)}'
        }), 500

@app.route('/api/backup/list')
@require_permission('admin')
def list_backups():
    """عرض قائمة النسخ الاحتياطية"""
    try:
        backups = get_backup_list()
        return jsonify({
            'success': True,
            'backups': backups
        })
    except Exception as e:
        logger.error(f"[BACKUP] خطأ في عرض النسخ: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في عرض النسخ: {str(e)}'
        }), 500

@app.route('/api/backup/stats')
@require_permission('admin')
def backup_stats():
    """عرض إحصائيات النسخ الاحتياطية"""
    try:
        stats = get_backup_statistics()
        return jsonify({
            'success': True,
            'stats': stats
        })
    except Exception as e:
        logger.error(f"[BACKUP] خطأ في عرض الإحصائيات: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في عرض الإحصائيات: {str(e)}'
        }), 500

@app.route('/api/backup/restore/<filename>', methods=['POST'])
@require_permission('admin')
def restore_backup(filename):
    """استعادة نسخة احتياطية"""
    try:
        success = backup_system.restore_backup(filename)
        if success:
            return jsonify({
                'success': True,
                'message': f'تم استعادة النسخة {filename} بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في استعادة النسخة'
            }), 500
    except Exception as e:
        logger.error(f"[BACKUP] خطأ في استعادة النسخة: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في استعادة النسخة: {str(e)}'
        }), 500

@app.route('/api/clear-data')
def clear_data():
    """مسح جميع بيانات التحليل"""
    clear_analysis_data()
    return jsonify({'message': 'تم مسح جميع بيانات التحليل بنجاح'})

@app.route('/api/performance')
@require_permission('admin')
def get_performance_metrics():
    """الحصول على مقاييس الأداء"""
    try:
        report = performance_optimizer.get_performance_report()
        return jsonify({
            'success': True,
            'performance_report': report
        })
    except Exception as e:
        logger.error(f"[ERROR] خطأ في الحصول على مقاييس الأداء: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في الحصول على مقاييس الأداء: {str(e)}'
        }), 500

@app.route('/api/performance/clear-cache')
@require_permission('admin')
def clear_performance_cache():
    """مسح ذاكرة التخزين المؤقت للأداء"""
    try:
        performance_optimizer.cache.clear()
        return jsonify({
            'success': True,
            'message': 'تم مسح ذاكرة التخزين المؤقت بنجاح'
        })
    except Exception as e:
        logger.error(f"[ERROR] خطأ في مسح الذاكرة المؤقتة: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في مسح الذاكرة المؤقتة: {str(e)}'
        }), 500

@app.route('/export/pdf/<session_id>')
@require_permission('export')
def export_pdf(session_id):
    """تصدير نتائج التحليل كملف PDF"""
    logger.info(f"[PDF_EXPORT] طلب تصدير PDF للجلسة: {session_id}")

    # التحقق من وجود النتائج
    if session_id not in analysis_results:
        logger.warning(f"[PDF_EXPORT] لم يتم العثور على نتائج للجلسة: {session_id}")
        return jsonify({'error': 'لم يتم العثور على نتائج التحليل لهذه الجلسة'}), 404

    try:
        # الحصول على بيانات التحليل
        analysis_data = analysis_results[session_id]

        # إنشاء تقرير PDF
        pdf_data = create_pdf_report(analysis_data, session_id)

        if pdf_data is None:
            logger.error(f"[PDF_EXPORT] فشل في إنشاء تقرير PDF للجلسة: {session_id}")
            return jsonify({'error': 'فشل في إنشاء تقرير PDF'}), 500

        # إنشاء اسم الملف
        file_name = f"analysis_report_{session_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"

        # تسجيل النشاط
        log_user_activity(
            session.get('user_id'),
            'export_pdf',
            'file',
            session_id,
            f'تصدير تقرير PDF للجلسة {session_id}',
            request.remote_addr
        )

        logger.info(f"[PDF_EXPORT] تم إنشاء تقرير PDF بنجاح للجلسة: {session_id}")

        # إرجاع ملف PDF
        response = app.response_class(
            response=pdf_data,
            status=200,
            mimetype='application/pdf',
            headers={
                'Content-Disposition': f'attachment; filename="{file_name}"',
                'Content-Length': len(pdf_data)
            }
        )

        return response

    except Exception as e:
        logger.error(f"[PDF_EXPORT] خطأ في تصدير PDF للجلسة {session_id}: {e}")
        return jsonify({'error': f'خطأ في تصدير PDF: {str(e)}'}), 500

@app.route('/charts/<session_id>')
@require_permission('view')
def interactive_charts(session_id):
    """عرض الرسوم البيانية التفاعلية"""
    logger.info(f"[CHARTS] طلب عرض الرسوم البيانية للجلسة: {session_id}")

    if session_id in analysis_results:
        result = analysis_results[session_id]
        # تحويل أنواع numpy إلى أنواع Python الأساسية للتوافق مع JSON
        result = convert_numpy_types(result)

        logger.info(f"[CHARTS] عرض الرسوم البيانية للجلسة: {session_id}")
        return render_template('interactive_charts.html', session_id=session_id, analysis_data=result)
    else:
        logger.warning(f"[WARNING] لم يتم العثور على نتائج للجلسة: {session_id}")
        error_message = 'لم يتم العثور على نتائج التحليل لهذه الجلسة. تأكد من اكتمال عملية التحليل أولاً.'
        return render_template('error.html',
                             error_message=error_message,
                             error_type='RESULTS_NOT_FOUND',
                             error_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')), 404

# إعدادات التشغيل
if __name__ == '__main__':
    logger.info("[START] بدء نظام تحليل جميع القطاعات...")

    # بدء مراقبة الأداء
    logger.info("[PERFORMANCE] بدء مراقبة الأداء")
    performance_optimizer.monitor.start_monitoring()

    # إنشاء المجلدات المطلوبة
    os.makedirs(UPLOAD_FOLDER, exist_ok=True)
    os.makedirs('database', exist_ok=True)
    os.makedirs('logs', exist_ok=True)

    # إنشاء جداول قاعدة البيانات
    if create_database_tables():
        logger.info("[SUCCESS] تم إعداد قاعدة البيانات بنجاح")
    else:
        logger.warning("[WARNING] فشل في إعداد قاعدة البيانات")

    # تنظيف الجلسات المنتهية الصلاحية
    cleanup_expired_sessions()

    # إعداد نظام النسخ الاحتياطي التلقائي
    try:
        backup_system.schedule_backups()
        backup_system.start_scheduler()
        logger.info("[BACKUP] تم إعداد نظام النسخ الاحتياطي التلقائي")
    except Exception as backup_error:
        logger.error(f"[BACKUP] خطأ في إعداد نظام النسخ الاحتياطي: {backup_error}")

    # تشغيل التطبيق
    logger.info(f"[SERVER] بدء تشغيل الخادم على {FLASK_HOST}:{FLASK_PORT}")

    # إزالة متغير البيئة المسبب للمشكلة
    if 'WERKZEUG_SERVER_FD' in os.environ:
        del os.environ['WERKZEUG_SERVER_FD']

    # تشغيل التطبيق مع معالجة الأخطاء
    try:
        app.run(
            debug=False,
            host=FLASK_HOST,
            port=FLASK_PORT,
            threaded=True,
            use_reloader=False
        )
    except KeyError as e:
        if 'WERKZEUG_SERVER_FD' in str(e):
            logger.warning("[SERVER] إعادة تشغيل بدون متغيرات البيئة المشكلة")
            # إزالة جميع متغيرات البيئة المشكلة
            werkzeug_vars = [k for k in os.environ.keys() if k.startswith('WERKZEUG_')]
            for var in werkzeug_vars:
                del os.environ[var]

            app.run(
                debug=False,
                host=FLASK_HOST,
                port=FLASK_PORT,
                threaded=True,
                use_reloader=False
            )
        else:
            raise
    finally:
        # تنظيف موارد الأداء عند إيقاف التطبيق
        logger.info("[SHUTDOWN] تنظيف موارد الأداء")
        performance_optimizer.cleanup_resources()