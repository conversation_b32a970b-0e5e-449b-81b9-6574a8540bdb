
/* Mobile Optimizations */
@media (max-width: 768px) {
    .charts-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .chart-card {
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .chart-container {
        height: 300px; /* تقليل الارتفاع للهواتف */
    }
    
    .header-content {
        flex-direction: column;
        gap: 10px;
        padding: 0 15px;
    }
    
    .stats-bar {
        flex-wrap: wrap;
        justify-content: center;
        gap: 10px;
    }
    
    .stat-item {
        padding: 8px 15px;
        font-size: 0.9rem;
    }
    
    .welcome-section h2 {
        font-size: 1.5rem;
    }
    
    .control-btn {
        padding: 6px 12px;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .main-container {
        padding: 0 10px;
    }
    
    .chart-card {
        padding: 10px;
    }
    
    .chart-container {
        height: 250px;
    }
    
    .welcome-section {
        padding: 15px;
    }
    
    .section-header h3 {
        font-size: 1.3rem;
    }
}

/* Touch Optimizations */
@media (hover: none) and (pointer: coarse) {
    .control-btn {
        min-height: 44px; /* Apple's recommended touch target size */
        min-width: 44px;
    }
    
    .chart-card:hover {
        transform: none; /* Disable hover effects on touch devices */
    }
    
    .stat-item {
        min-height: 44px;
        display: flex;
        align-items: center;
    }
}

/* PWA Support */
@media (display-mode: standalone) {
    .dashboard-header {
        padding-top: env(safe-area-inset-top, 20px);
    }
    
    .main-container {
        padding-bottom: env(safe-area-inset-bottom, 20px);
    }
}
