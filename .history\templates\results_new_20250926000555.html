<!DOCTYPE html>
<html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>نتائج التحليل - نظام تحليل البيانات</title>
        <!-- Bootstrap CSS -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- Font Awesome -->
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <!-- Chart.js -->
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background-color: #63a0dd;
            }

            .card {
                border: none;
                border-radius: 15px;
                box-shadow: 0 4px 6px rgba(252, 248, 248, 0.1);
                margin-bottom: 20px;
            }

            .card-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: rgb(248, 242, 242);
                border-radius: 15px 15px 0 0 !important;
                border: none;
            }

            /* Interactive Charts Styles */
            .interactive-charts-section {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: none;
                border-radius: 20px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                margin: 30px 0;
            }

            .chart-controls {
                background: rgba(255,255,255,0.1);
                border-radius: 15px;
                padding: 20px;
                margin-bottom: 30px;
            }

            .chart-controls .form-select {
                background: rgba(255,255,255,0.1);
                border: 1px solid rgba(255,255,255,0.3);
                color: white;
            }

            .chart-controls .form-select option {
                background: #667eea;
                color: white;
            }

            .chart-container {
                background: white;
                border-radius: 15px;
                padding: 10px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                height: 350px;
            }

            .chart-card {
                background: white;
                border-radius: 15px;
                overflow: hidden;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                transition: transform 0.3s ease;
            }

            .chart-card:hover {
                transform: translateY(-5px);
            }

            .btn-outline-light {
                border: 2px solid rgba(255,255,255,0.5);
                color: white;
            }

            .btn-outline-light:hover {
                background: rgba(255,255,255,0.2);
                border-color: white;
                color: white;
            }

            .stat-card {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-radius: 15px;
                padding: 20px;
                text-align: center;
                margin-bottom: 20px;
            }

            .table-responsive {
                border-radius: 10px;
                overflow: hidden;
            }

            .table th {
                background-color: #f8f9fa;
                border: none;
                font-weight: 600;
            }

            .table td {
                border: none;
                border-bottom: 1px solid #dee2e6;
            }

            .badge {
                font-size: 0.8rem;
            }

            .alert {
                border: none;
                border-radius: 10px;
            }

            .navbar {
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .progress {
                height: 8px;
                border-radius: 4px;
            }

            .chart-wrapper {
                position: relative;
                height: 400px;
                margin-bottom: 20px;
            }

            .chart-canvas {
                max-height: 400px;
            }

            .distribution-table {
                font-size: 0.9rem;
            }

            .distribution-table th {
                background-color: #e9ecef;
                font-weight: 600;
                text-align: center;
            }

            .distribution-table td {
                vertical-align: middle;
            }

            .stats-summary {
                background-color: #f8f9fa;
                border-radius: 10px;
                padding: 15px;
            }

            .chart-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
            }

            .chart-card {
                border: none;
                border-radius: 15px;
                overflow: hidden;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                margin-bottom: 30px;
            }

            .chart-wrapper-large {
                position: relative;
                height: 500px;
                margin-bottom: 20px;
            }

            .chart-canvas-large {
                max-height: 500px;
            }

            .stats-summary-large {
                background-color: #f8f9fa;
                border-radius: 15px;
                padding: 20px;
                height: 100%;
            }

            .bg-gradient {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            }

            .text-gradient {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }

            .btn-gradient {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: none;
                color: white;
            }

            .btn-gradient:hover {
                background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
                color: white;
            }

            .distributions-section {
                margin-top: 30px;
            }

            .distributions-section .card {
                margin-bottom: 40px;
            }

            .distributions-section .chart-wrapper-large {
                height: 450px;
            }

            .distributions-section .table {
                margin-bottom: 0;
            }

            .distributions-section .stats-summary-large {
                display: flex;
                flex-direction: column;
                justify-content: center;
            }

            /* Table Sky Theme */
            .table-sky {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-radius: 10px;
                overflow: hidden;
            }

            .table-sky thead th {
                background: rgba(255, 255, 255, 0.1);
                border: none;
                color: white;
                font-weight: 600;
                text-align: center;
                vertical-align: middle;
                padding: 15px 10px;
            }

            .table-sky tbody td {
                background: rgba(255, 255, 255, 0.05);
                border: none;
                color: white;
                text-align: center;
                vertical-align: middle;
                padding: 12px 10px;
            }

            .table-sky tbody tr:hover td {
                background: rgba(255, 255, 255, 0.1);
            }

            .table-sky .badge {
                background: rgba(255, 255, 255, 0.2) !important;
                color: white !important;
                border: 1px solid rgba(255, 255, 255, 0.3);
            }

            /* Sortable Headers */
            .sortable {
                cursor: pointer;
                user-select: none;
                position: relative;
            }

            .sortable:hover {
                background: rgba(255, 255, 255, 0.15) !important;
            }

            .sort-icon {
                margin-left: 5px;
                opacity: 0.5;
                transition: opacity 0.3s ease;
            }

            .sortable:hover .sort-icon {
                opacity: 1;
            }

            .sortable.sort-asc .sort-icon:before {
                content: '\f145';
                /* fa-sort-up */
            }

            .sortable.sort-desc .sort-icon:before {
                content: '\f144';
                /* fa-sort-down */
            }

            /* Merge Table Sortable Headers */
            #mergeAnalysisTable .sortable {
                background: rgba(255, 255, 255, 0.1);
                transition: all 0.3s ease;
                cursor: pointer;
                user-select: none;
            }

            #mergeAnalysisTable .sortable:hover {
                background: rgba(255, 255, 255, 0.2) !important;
                transform: translateY(-1px);
            }

            #mergeAnalysisTable .sort-icon {
                margin-right: 5px;
                margin-left: 0;
                font-size: 0.8em;
                opacity: 0.5;
                transition: opacity 0.3s ease;
            }

            #mergeAnalysisTable .sortable:hover .sort-icon {
                opacity: 1;
            }

            #mergeAnalysisTable .sortable.sort-asc .sort-icon:before {
                content: '\f145';
                /* fa-sort-up */
            }

            #mergeAnalysisTable .sortable.sort-desc .sort-icon:before {
                content: '\f144';
                /* fa-sort-down */
            }

            /* Person Key Column Styling */
            #mergeAnalysisTable tbody td:first-child {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white !important;
                font-weight: 600;
                text-shadow: 0 1px 2px rgba(0,0,0,0.3);
                border: none;
                position: relative;
                overflow: hidden;
            }

            #mergeAnalysisTable tbody td:first-child::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                transition: left 0.5s;
            }

            #mergeAnalysisTable tbody tr:hover td:first-child::before {
                left: 100%;
            }

            #mergeAnalysisTable tbody td:first-child small {
                color: rgba(255,255,255,0.8) !important;
                font-weight: 400;
                text-shadow: none;
            }

            #mergeAnalysisTable tbody td:first-child:hover {
                background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
                transform: scale(1.02);
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                z-index: 10;
            }

            /* Enhanced table styling for merge table */
            #mergeAnalysisTable.table-sky tbody td:first-child {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                color: white !important;
            }

            #mergeAnalysisTable.table-sky tbody tr:hover td:first-child {
                background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
            }

            /* PersonRecord Table Styles */
            #personTable {
                width: 100%;
                border-collapse: collapse;
                text-align: center;
                margin-top: 20px;
            }

            #personTable th, #personTable td {
                border: 1px solid #333;
                padding: 8px;
            }

            #personTable th {
                background-color: #f2f2f2;
            }

            .note-normal {
                color: green;
                font-weight: bold;
            }

            .note-merge {
                color: orange;
                font-weight: bold;
            }

            .note-conflict {
                color: red;
                font-weight: bold;
            }

            .note-review {
                color: blue;
                font-weight: bold;
            }

            /* Enhanced PersonRecord Table Styles */
            #personTable {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
                font-size: 14px;
                background-color: #ffffff;
                box-shadow: 0 4px 8px rgba(0,0,0,0.15);
                border-radius: 8px;
                overflow: hidden;
            }

            #personTable th, #personTable td {
                padding: 16px 12px;
                text-align: center;
                border: 1px solid #dee2e6;
                font-weight: 600;
            }

            #personTable th {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                font-weight: bold;
                position: sticky;
                top: 0;
                z-index: 10;
                transition: all 0.3s ease;
            }

            #personTable th:hover {
                background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
                color: #667eea !important;
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                border: 2px solid #667eea !important;
            }

            #personTable tbody tr:nth-child(even) {
                background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 25%, #d1ecf1 50%, #e6f3ff 75%, #f0f8ff 100%);
                border-bottom: 1px solid #b8daff;
                box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.8), 0 1px 3px rgba(0, 123, 255, 0.08);
                border-left: 3px solid #cce7ff;
            }

            #personTable tbody tr:nth-child(odd) {
                background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 20%, #f0f4ff 40%, #e8f0ff 60%, #f0f4ff 80%, #ffffff 100%);
                border-bottom: 1px solid #d4edda;
                box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.9), 0 1px 3px rgba(40, 167, 69, 0.06);
                border-left: 3px solid #d1ecf1;
            }

            #personTable tbody tr:hover {
                background: linear-gradient(135deg, #e8f4fd 0%, #b3e5fc 30%, #81d4fa 60%, #4fc3f7 80%, #e8f4fd 100%);
                cursor: pointer;
                transform: translateY(-3px) scale(1.008);
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                box-shadow: 0 8px 25px rgba(33, 150, 243, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.6);
                border-left: 6px solid #1976d2;
                border-radius: 0 12px 12px 0;
                z-index: 5;
            }

            /* Special styling for conflict rows - VIBRANT CRIMSON RED with enhanced glow */
            #personTable tbody tr:has(.merge-reason:contains("🚨")) {
                background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 25%, #ef9a9a 50%, #e57373 75%, #ffebee 100%);
                border-left: 8px solid #d32f2f;
                box-shadow: 0 4px 15px rgba(211, 47, 47, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
                position: relative;
                border-radius: 0 12px 12px 0;
                animation: pulseConflict 3s infinite ease-in-out;
            }

            #personTable tbody tr:has(.merge-reason:contains("🚨")):hover {
                background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 30%, #ef5350 60%, #f44336 80%, #ffebee 100%);
                border-left: 10px solid #b71c1c;
                box-shadow: 0 8px 25px rgba(211, 47, 47, 0.35), inset 0 1px 0 rgba(255, 255, 255, 0.4);
                transform: translateY(-5px) scale(1.012);
                z-index: 15;
            }

            /* Special styling for merge needed rows - WARM AMBER ORANGE with golden glow */
            #personTable tbody tr:has(.merge-reason:contains("⚠️")) {
                background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 25%, #ffe082 50%, #ffd54f 75%, #fff8e1 100%);
                border-left: 8px solid #f57c00;
                box-shadow: 0 4px 15px rgba(245, 124, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
                border-radius: 0 12px 12px 0;
                animation: pulseWarning 4s infinite ease-in-out;
            }

            #personTable tbody tr:has(.merge-reason:contains("⚠️")):hover {
                background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 30%, #ffe082 60%, #ffb74d 80%, #fff8e1 100%);
                border-left: 10px solid #ef6c00;
                box-shadow: 0 8px 25px rgba(245, 124, 0, 0.35), inset 0 1px 0 rgba(255, 255, 255, 0.4);
                transform: translateY(-5px) scale(1.012);
                z-index: 15;
            }

            /* Special styling for normal rows - FRESH EMERALD GREEN with minty glow */
            #personTable tbody tr:has(.merge-reason:contains("✅")) {
                background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 25%, #a5d6a7 50%, #81c784 75%, #e8f5e8 100%);
                border-left: 8px solid #2e7d32;
                box-shadow: 0 4px 15px rgba(46, 125, 50, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
                border-radius: 0 12px 12px 0;
                animation: pulseSuccess 5s infinite ease-in-out;
            }

            #personTable tbody tr:has(.merge-reason:contains("✅")):hover {
                background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 30%, #a5d6a7 60%, #66bb6a 80%, #e8f5e8 100%);
                border-left: 10px solid #1b5e20;
                box-shadow: 0 8px 25px rgba(46, 125, 50, 0.35), inset 0 1px 0 rgba(255, 255, 255, 0.4);
                transform: translateY(-5px) scale(1.012);
                z-index: 15;
            }

            /* Special styling for review needed rows - MYSTICAL INDIGO PURPLE with ethereal glow */
            #personTable tbody tr:has(.merge-reason:contains("❓")) {
                background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 25%, #ce93d8 50%, #ba68c8 75%, #f3e5f5 100%);
                border-left: 8px solid #7b1fa2;
                box-shadow: 0 4px 15px rgba(123, 31, 162, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
                border-radius: 0 12px 12px 0;
                animation: pulseReview 6s infinite ease-in-out;
            }

            #personTable tbody tr:has(.merge-reason:contains("❓")):hover {
                background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 30%, #ce93d8 60%, #ab47bc 80%, #f3e5f5 100%);
                border-left: 10px solid #6a1b9a;
                box-shadow: 0 8px 25px rgba(123, 31, 162, 0.35), inset 0 1px 0 rgba(255, 255, 255, 0.4);
                transform: translateY(-5px) scale(1.012);
                z-index: 15;
            }

            #personTable .person-name {
                font-weight: 700;
                color: #2c3e50;
                max-width: 200px;
                word-wrap: break-word;
                background: #f8f9fa;
                padding: 10px 14px;
                border-radius: 6px;
                border: 1px solid #dee2e6;
                transition: all 0.2s ease;
                font-size: 14px;
                line-height: 1.4;
            }

            #personTable .insurance-numbers, #personTable .manual-numbers {
                font-family: 'Courier New', monospace;
                background: #fff3cd;
                padding: 8px 12px;
                border-radius: 6px;
                color: #856404;
                border: 1px solid #ffeaa7;
                font-weight: 600;
                transition: all 0.2s ease;
                font-size: 13px;
                line-height: 1.4;
                word-spacing: 3px;
                letter-spacing: 0.5px;
            }

            #personTable .merge-reason {
                padding: 10px 14px;
                border-radius: 8px;
                font-weight: 700;
                border: 2px solid;
                text-transform: uppercase;
                font-size: 12px;
                letter-spacing: 0.5px;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
                overflow: hidden;
                text-shadow: 0 1px 1px rgba(0,0,0,0.1);
                line-height: 1.3;
                word-wrap: break-word;
            }

            /* Specific styling for each merge reason type */
            #personTable tbody tr:has(.merge-reason:contains("🚨")) .merge-reason {
                background: linear-gradient(135deg, #ff4757 0%, #ff3838 50%, #ff4757 100%);
                color: white;
                border-color: #ff3838;
                box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3);
            }

            #personTable tbody tr:has(.merge-reason:contains("⚠️")) .merge-reason {
                background: linear-gradient(135deg, #ffa726 0%, #fb8c00 50%, #ffa726 100%);
                color: white;
                border-color: #fb8c00;
                box-shadow: 0 2px 8px rgba(255, 167, 38, 0.3);
            }

            #personTable tbody tr:has(.merge-reason:contains("✅")) .merge-reason {
                background: linear-gradient(135deg, #4caf50 0%, #388e3c 50%, #4caf50 100%);
                color: white;
                border-color: #388e3c;
                box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
            }

            #personTable tbody tr:has(.merge-reason:contains("❓")) .merge-reason {
                background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 50%, #9c27b0 100%);
                color: white;
                border-color: #7b1fa2;
                box-shadow: 0 2px 8px rgba(156, 39, 176, 0.3);
            }

            #personTable .merge-reason::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
                transition: left 0.5s;
            }

            #personTable .merge-reason:hover::before {
                left: 100%;
            }

            #personTable .additional-info {
                font-size: 13px;
                color: #495057;
                max-width: 160px;
                word-wrap: break-word;
                background-color: #f8f9fa;
                padding: 4px 8px;
                border-radius: 4px;
                border: 1px solid #dee2e6;
            }

            #personTable .document-number {
                font-family: 'Courier New', monospace;
                background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
                color: #383d41;
                padding: 6px 10px;
                border-radius: 6px;
                font-weight: 600;
                border: 1px solid #adb5bd;
                font-size: 13px;
                letter-spacing: 0.5px;
            }

            #personTable .count-info {
                background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
                color: #155724;
                padding: 4px 8px;
                border-radius: 4px;
                font-weight: 600;
                border: 1px solid #28a745;
                font-size: 13px;
            }

            /* Enhanced animations for table rows */
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(40px) scale(0.95);
                    filter: blur(2px);
                }

                to {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                    filter: blur(0);
                }
            }

            @keyframes slideInLeft {
                from {
                    opacity: 0;
                    transform: translateX(-60px) skewX(-15deg) scale(0.9);
                }

                to {
                    opacity: 1;
                    transform: translateX(0) skewX(0deg) scale(1);
                }
            }

            @keyframes slideInRight {
                from {
                    opacity: 0;
                    transform: translateX(60px) skewX(15deg) scale(0.9);
                }

                to {
                    opacity: 1;
                    transform: translateX(0) skewX(0deg) scale(1);
                }
            }

            @keyframes bounceIn {
                0% {
                    opacity: 0;
                    transform: scale(0.1) rotate(-180deg);
                    filter: blur(3px);
                }

                50% {
                    opacity: 1;
                    transform: scale(1.1) rotate(0deg);
                    filter: blur(1px);
                }

                70% {
                    transform: scale(0.95) rotate(0deg);
                    filter: blur(0);
                }

                100% {
                    opacity: 1;
                    transform: scale(1) rotate(0deg);
                    filter: blur(0);
                }
            }

            @keyframes pulseGlow {
                0%, 100% {
                    box-shadow: 0 0 8px rgba(255, 255, 255, 0.4), 0 0 16px rgba(255, 255, 255, 0.2);
                }

                50% {
                    box-shadow: 0 0 16px rgba(255, 255, 255, 0.6), 0 0 32px rgba(255, 255, 255, 0.4);
                }
            }

            @keyframes rainbowGlow {
                0% {
                    box-shadow: 0 0 12px #ff6b6b, 0 0 24px #ff6b6b;
                }

                16.66% {
                    box-shadow: 0 0 12px #ffa726, 0 0 24px #ffa726;
                }

                33.33% {
                    box-shadow: 0 0 12px #ffeb3b, 0 0 24px #ffeb3b;
                }

                50% {
                    box-shadow: 0 0 12px #4caf50, 0 0 24px #4caf50;
                }

                66.66% {
                    box-shadow: 0 0 12px #2196f3, 0 0 24px #2196f3;
                }

                83.33% {
                    box-shadow: 0 0 12px #9c27b0, 0 0 24px #9c27b0;
                }

                100% {
                    box-shadow: 0 0 12px #ff6b6b, 0 0 24px #ff6b6b;
                }
            }

            @keyframes shimmer {
                0% {
                    background-position: -200% 0;
                }

                100% {
                    background-position: 200% 0;
                }
            }

            @keyframes pulseConflict {
                0%, 100% {
                    box-shadow: 0 4px 15px rgba(211, 47, 47, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
                }

                50% {
                    box-shadow: 0 6px 20px rgba(211, 47, 47, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.4);
                }
            }

            @keyframes pulseWarning {
                0%, 100% {
                    box-shadow: 0 4px 15px rgba(245, 124, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
                }

                50% {
                    box-shadow: 0 6px 20px rgba(245, 124, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.4);
                }
            }

            @keyframes pulseSuccess {
                0%, 100% {
                    box-shadow: 0 4px 15px rgba(46, 125, 50, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
                }

                50% {
                    box-shadow: 0 6px 20px rgba(46, 125, 50, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.4);
                }
            }

            @keyframes pulseReview {
                0%, 100% {
