
// Enhanced Export Functions
class AdvancedExporter {
    constructor() {
        this.formats = ['png', 'jpg', 'pdf', 'svg', 'excel'];
        this.quality = 1.0;
    }
    
    // تصدير الرسم البياني بجودة عالية
    exportChart(chart, filename, format = 'png', options = {}) {
        const defaultOptions = {
            quality: this.quality,
            backgroundColor: '#ffffff',
            width: 1920,
            height: 1080
        };
        
        const exportOptions = { ...defaultOptions, ...options };
        
        try {
            let dataURL;
            
            switch (format.toLowerCase()) {
                case 'png':
                    dataURL = chart.toBase64Image('image/png', exportOptions.quality);
                    break;
                case 'jpg':
                case 'jpeg':
                    dataURL = chart.toBase64Image('image/jpeg', exportOptions.quality);
                    break;
                case 'svg':
                    // SVG export would require additional library
                    console.warn('SVG export requires additional implementation');
                    return;
                default:
                    dataURL = chart.toBase64Image('image/png', exportOptions.quality);
            }
            
            this.downloadFile(dataURL, `${filename}.${format}`);
            this.showExportSuccess(filename, format);
            
        } catch (error) {
            console.error('Export failed:', error);
            this.showExportError(error.message);
        }
    }
    
    // تصدير البيانات إلى Excel
    exportToExcel(data, filename) {
        // This would require a library like SheetJS
        console.log('Excel export functionality - requires SheetJS implementation');
    }
    
    // تصدير تقرير PDF شامل
    exportToPDF(data, filename) {
        // This would require a library like jsPDF
        console.log('PDF export functionality - requires jsPDF implementation');
    }
    
    downloadFile(dataURL, filename) {
        const link = document.createElement('a');
        link.href = dataURL;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
    
    showExportSuccess(filename, format) {
        const message = `تم تصدير ${filename} بصيغة ${format.toUpperCase()} بنجاح`;
        this.showNotification(message, 'success');
    }
    
    showExportError(error) {
        const message = `خطأ في التصدير: ${error}`;
        this.showNotification(message, 'error');
    }
    
    showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `export-notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            animation: slideIn 0.3s ease;
            background: ${type === 'success' ? '#27ae60' : '#e74c3c'};
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}

// Global exporter instance
window.advancedExporter = new AdvancedExporter();

// CSS for notifications
const notificationCSS = `
@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}
`;

const style = document.createElement('style');
style.textContent = notificationCSS;
document.head.appendChild(style);
